/* eslint-disable no-param-reassign */
import qs from 'qs'
import { useUserStore } from '@/store'
import { platform } from '@/utils/platform'

export type CustomRequestOptions = UniApp.RequestOptions & {
  query?: Record<string, any>
  /** 出错时是否隐藏错误提示 */
  hideErrorToast?: boolean
  /** 是否需要token */
  noToken?: boolean
} & IUniUploadFileOptions // 添加uni.uploadFile参数类型

// 请求基准地址
const baseUrl = import.meta.env.VITE_SERVER_BASEURL

// 拦截器配置
const httpInterceptor = {
  // 拦截前触发
  invoke(options: CustomRequestOptions) {
    // 接口请求支持通过 query 参数配置 queryString
    if (options.query) {
      const queryStr = qs.stringify(options.query)
      if (options.url.includes('?')) {
        options.url += `&${queryStr}`
      } else {
        options.url += `?${queryStr}`
      }
    }
    // 非 http 开头需拼接地址
    if (!options.url.startsWith('http')) {
      // #ifdef H5
      if (JSON.parse(__VITE_APP_PROXY__)) {
        // console.log(options.url, '=====requrl')
        options.url.includes('ehs-')
          ? (options.url = import.meta.env.VITE_APP_PROXY_PREFIX + options.url)
          : (options.url = import.meta.env.VITE_SERVER_MODEL + options.url)
        // if (
        //   options.url.startsWith('/edu-inter-server') ||
        //   options.url.startsWith('/edu-app-server')
        // ) {
        //   options.url = import.meta.env.VITE_APP_PROXY_PREFIX + options.url
        // } else if (options.url.startsWith('/train-server')) {
        //   options.url = import.meta.env.VITE_BASE_workbench + options.url
        // } else if (options.url.startsWith('/ehs-clnt-platform-service')) {
        //   options.url = import.meta.env.VITE_BASE_workbench + options.url
        // } else {
        //   options.url = import.meta.env.VITE_SERVER_MODEL + options.url
        // }
      } else {
        options.url = import.meta.env.VITE_SERVER_MODEL + options.url
      }
      // #endif
      // 非H5正常拼接
      // #ifndef H5
      options.url = baseUrl + options.url
      // #endif
      // TIPS: 如果需要对接多个后端服务，也可以在这里处理，拼接成所需要的地址
    } else {
      // console.log(options.url)
    }
    // console.log(options.url, '=====requrl')
    // 1. 请求超时
    // options.timeout = 10000 // 10s
    options.timeout = 60000 // 1m
    // 2. （可选）添加小程序端请求头标识
    options.header = {
      platform, // 可选，与 uniapp 定义的平台一致，告诉后台来源
      ...options.header,
    }
    // 3. 添加 token 请求头标识
    const userStore = useUserStore()
    const { token } = userStore.userInfo as unknown as IUserInfo
    const { roleCodes } = userStore.userInfo as unknown as any
    const { id } = userStore.userInfo as unknown as any

    // 对 /login/checkSysPower 接口特殊处理，使用存储的用户信息中的 token
    if (options.url.includes('/login/checkSysPower')) {
      // 从本地存储获取 token
      const storedUserInfo = uni.getStorageSync('@userInfo')
      const storedToken = storedUserInfo?.token
      if (!options.noToken && storedToken) {
        options.header.token = storedToken
      }
    } else {
      // 其他接口使用 userStore 中的 token
      if (!options.noToken && token) {
        options.header.token = token
      }
    }

    options.header.roleCodes = roleCodes
    options.header.id = id
  },
}

export const requestInterceptor = {
  install() {
    // 拦截 request 请求
    uni.addInterceptor('request', httpInterceptor)
    // 拦截 uploadFile 文件上传
    uni.addInterceptor('uploadFile', httpInterceptor)
  },
}
