// import { defineStore } from 'pinia'
// import { ref } from 'vue'
// import { http } from '@/utils/http'
// import { $api } from '@/api/index'

// const initState = { nickname: '', avatar: '', token: '' }

// interface IUserQuery {
//   name: ''
// }

// export const useUserStore = defineStore(
//   'user',
//   () => {
//     // 临时ticket,用于获取token
//     const userTicket = ref('')
//     const setUserTicket = (val: string) => {
//       userTicket.value = val
//     }
//     const clearUserTicket = () => {
//       userTicket.value = ''
//     }

//     const userInfo = ref<IUserInfo>({ ...initState })
//     const setUserInfo = (val: IUserInfo) => {
//       userInfo.value = val
//     }
//     const clearUserInfo = () => {
//       userInfo.value = { ...initState }
//     }

//     const isLogined = computed(() => !!userInfo.value.token)

//     // 获取用户信息
//     const getUserInfo = async (query: any) => {
//       const url = $api.getUrl($api.type.nil, $api.name.nil.foo)
//       // const url = `/login/getTokenLoginInfoByToken?sysCode=hazard_inves_app&token=${query}`
//       return http<IUserInfo>({
//         url,
//         query,
//         method: 'GET',
//         noToken: true,
//       })
//     }

//     return {
//       userInfo,
//       setUserInfo,
//       clearUserInfo,
//       userTicket,
//       setUserTicket,
//       clearUserTicket,
//       isLogined,
//       getUserInfo,
//     }
//   },
//   {
//     persist: true,
//   },
// )
import { defineStore } from 'pinia'
import { ref } from 'vue'
import { http } from '@/utils/http'
import { $api } from '@/api/index'

const initState = {
  userName: '',
  id: '',
  photoUrl: '',
  token: '',
  syscode: '',
}

interface IUserQuery {
  sysCode: string
  token: string
}

export const useUserStore = defineStore(
  'user',
  () => {
    // 临时ticket,用于获取token
    const userTicket = ref('')
    const setUserTicket = (val: string) => {
      userTicket.value = val
    }
    const clearUserTicket = () => {
      userTicket.value = ''
    }

    const userInfo = ref<IUserInfo>({ ...initState })
    const setUserInfo = (val: IUserInfo) => {
      userInfo.value = val
    }
    const clearUserInfo = () => {
      userInfo.value = { ...initState }
    }

    const isLogined = computed(() => !!userInfo.value.token)

    // 获取用户信息
    const getUserInfo = async (query: IUserQuery) => {
      const url = $api.getUrl(
        $api.type.hazard,
        `/login/getTokenLoginInfoByToken?sysCode=${query.sysCode}&token=${query.token}`,
      )
      // const url = `/login/getTokenLoginInfoByToken?sysCode=${query.sysCode}&token=${query.token}`
      return http.get(url)
      // return http<IUserInfo>({
      //   url,
      //   query,
      //   method: 'GET',
      //   noToken: true,
      // })
    }

    // checkSysPower接口获取token
    const checkSysPower = async (parameters: any) => {
      return http.post($api.type.platform + '/login/checkSysPower', parameters)
    }

    return {
      userInfo,
      setUserInfo,
      clearUserInfo,
      userTicket,
      setUserTicket,
      clearUserTicket,
      isLogined,
      getUserInfo,
      checkSysPower
    }
  },
  {
    persist: true,
  },
)
