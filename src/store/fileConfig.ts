import { $api } from '@/api'
import { http } from '@/utils/http'
import { defineStore } from 'pinia'
import { ref } from 'vue'

export const usefileConfigStore = defineStore(
  'fileConfig',
  () => {
    const fileUrl = ref<any>({})
    const setfileConfig = (val: any) => {
      fileUrl.value = val
    }
    const getfileConfig = async () => {
      const url = `${$api.type.hazard}/file/getFileConfig`
      return http.get(url)
    }
    const clearfileConfigData = () => {
      fileUrl.value = null
    }

    return {
      fileUrl,
      getfileConfig,
      setfileConfig,
      clearfileConfigData,
    }
  },
  {
    persist: true,
  },
)
