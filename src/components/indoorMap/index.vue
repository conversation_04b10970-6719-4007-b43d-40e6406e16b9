<template>
  <div class="w-full h-full relative">
    <!-- <div class="h-[15px] absolute top-[0] z-20" @click="loadFloorData">6666666666</div> -->
    <div class="w-full h-[700px] relative" ref="ElGIs"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, watch, nextTick } from 'vue'
import { GISOBJ } from './gisGlobal'
import gisPopup from './gisPopup.vue'

const ElGIs = ref()
interface DeviceItem {
  [key: string]: string | number
}
interface Props {
  deviceList?: DeviceItem[]
  floorInfo?: {
    unitId: string
    buildingId: string
    floorId: string
    floorAreaImg?: string
  }
  pointer?: {
    x: number
    y: number
  }
  isAddMark?: boolean
}
const props = withDefaults(defineProps<Props>(), {
  deviceList: () => [],
  isAddMark: false,
})
const emits = defineEmits(['addMark'])
const floorData = computed(() => {
  return props?.floorInfo
})
const pointer = computed(() => {
  return props?.deviceList || []
})
const defPonter = computed(() => {
  return props?.pointer || {}
})

watch(
  () => floorData.value,
  (val) => {
    if (val?.unitId && val.buildingId && val.floorId) {
      loadFloorData()
    }
  },
  {
    deep: true,
  },
)

watch(
  () => pointer.value,
  () => {
    loadFloorData()
  },
  {
    deep: true,
  },
)
watch(
  () => defPonter.value,
  () => {
    if (props.isAddMark) {
      addDeftPointer()
    }
  },
  {
    deep: true,
  },
)
const addDeftPointer = () => {
  gisMap3DM.addNewMark({ ...defPonter.value, z: 0 })
}
let popup: any

let gisMap3DM: any
const initGis = () => {
  ElGIs.value.appendChild(GISOBJ.getDom())
  gisMap3DM = GISOBJ.getIndoorMap()
    ; (window as any).gisMap3DM = gisMap3DM
  GISOBJ.render()
  gisMap3DM.clearPopup()
}
uni.showLoading({
  title: '加载中...',
  icon: 'none',
  mask: true,
})

const loadFloorData = () => {
  // 渲染平面图

  gisMap3DM.showFloorData(
    window.IndoorMap.ViewType.IndoorAreaVector,
    floorData.value?.unitId ?? undefined, // 单位id
    floorData.value?.buildingId ?? undefined, // 楼栋id
    floorData.value?.floorId, // 楼层id
    undefined, // 图纸地址 （用于查询不到室内GIS数据时自动跳转，可缺省）
    // floorData.value?.floorAreaImg
    //   ? GISOBJ.baseurl + '/img1/floorImage/' + floorData.value.floorAreaImg
    //   : undefined,
    function (mapType: string, success: any, objArgs: any, indoor: any) {
      uni.hideLoading()
      // success
      GISOBJ.render()
      if (props.isAddMark) {
        addDeftPointer()
      }
      indoor.showFloorDataDevice(pointer.value ?? [], 'mapX', 'mapY', 'mapZ', function filter() {
        // 过滤条件，可对数据做修改，返回true表示去掉此条数据
        // item, markFieldNameX, markFieldNameY
        return false
      })
    },
  )
}
const bandEvent = () => {
  gisMap3DM.onMouseClick = function (e: any) {
    if (!props.isAddMark) return
    gisMap3DM.clearNewMark()
    GISOBJ.render()
    const pointer = gisMap3DM
      .getMap()
      .getView()
      .ClientToScene(new window.THREE.Vector2(e.getX(), e.getY()), 0)
    const { x, y } = pointer
    gisMap3DM.addNewMark({ x, y, z: 0 })
    emits('addMark', { x, y })
  }
  // 楼层图斑点击事件
  gisMap3DM.onAreaSelected = function (data: any, e: any, obj: any, target: any) {
    // console.log('🚀 ~ bandEvent ~ 楼层图斑点击事件.point:', target.point)
  }
  // 楼层网格点击事件
  gisMap3DM.onGridSelected = function (data: any, e: any, obj: any, target: any) { }
  gisMap3DM.onNullSelected = function (data: any) { }
  // 设备点位图标 点击事件
  gisMap3DM.onDeviceSelected = function (data: any, e: any, obj: any, target: any) { }
}

onMounted(() => {
  initGis()
  bandEvent()

  setTimeout(() => {
    loadFloorData()
    if (props.isAddMark) {
      addDeftPointer()
    }
  }, 500)
})

defineOptions({ name: 'floorGis' })
</script>

<style module lang="scss"></style>
