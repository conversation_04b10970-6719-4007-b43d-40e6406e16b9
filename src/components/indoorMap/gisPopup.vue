<template>
  <div class="gisPopup flex-row justify-between">
    <span class="title text-ellipsis">{{ name }}</span>
    <span class="title text-ellipsis">{{ name2 }}</span>

    <span class="detailBtn" v-if="hasBtn" track @click="clickHandle">查看</span>
  </div>
</template>

<script lang="ts" setup>
import { computed, watch, ref } from 'vue'

interface Props {
  hasBtn: boolean
  name: string
  name2: string

  onClick: () => void
}

const props = withDefaults(defineProps<Props>(), {
  name: '',
  hasBtn: false,
  onClick: () => { },
})

// const onClick = ()=>{
//   // console.log('onClick--------------')
// }
const clickHandle = () => {
  // console.log('clickHandle--------------', props)
  // console.log('clickHandle--------------', props.name)
  props.onClick()
}
// defineExpose({ myname,myhasBtn })
</script>

<style lang="scss" scoped>
.gisPopup {
  position: relative;
  z-index: 1;
  display: flex;
  align-items: center;
  padding: 5px 18px;
  background: #fff;
  background: linear-gradient(0deg, #02336b 0%, #0262cf 100%);

  border: 1px solid #0080ff;
  border-radius: 4px;
  box-shadow: 0px 0px 4px 0px rgba(21, 109, 211, 0.5);

  // &::before,
  &::after {
    position: absolute;
    bottom: -21px;
    left: 50%;
    z-index: 1;
    width: 0;
    height: 0;
    content: '';
    border: 10px solid transparent;
    transform: translateX(-50%);
  }

  // &::before{
  //     border-top: 10px solid rgba(2, 51, 107, 1);
  //     bottom: -19px;
  //     z-index: 2;
  // }
  &::after {
    border-top: 10px solid rgba(0, 128, 255, 1);
  }

  .title {
    font-size: 16px;
    font-weight: 600;
    color: #fff;
    // width: 180px;
    white-space: nowrap;
  }

  .detailBtn {
    box-sizing: border-box;
    display: inline-block;
    width: 50px;
    padding: 5px 0;
    margin-left: 20px;
    font-size: 12px;
    color: #fff;
    text-align: center;
    white-space: nowrap;
    cursor: pointer;
    background: #0d417c;
    border: 1px solid #49b8ff;
    border-radius: 4px;
  }
}
</style>
