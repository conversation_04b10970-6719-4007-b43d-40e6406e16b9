<template>
  <view class="chart-wrapper" :style="{ height: `${height}rpx` }">
    <view class="w-full h-full" ref="lineChartRef"></view>
  </view>
</template>
<script lang="ts" setup>
import echarts from '@/utils/echart'
import { ILineProps } from './type'
// import { sleep } from '@/utils/index'

defineOptions({ name: 'lineChartComp' })

const props = withDefaults(defineProps<ILineProps>(), {
  height: 500,
  chartData: () => ({
    xData: [],
    yData: [],
  }),
  extendOption: () => ({}),
})

const lineChartRef = ref()

let lineChart = null

const option = {
  grid: {
    left: '3%',
    right: '3%',
    bottom: '20%',
    containLabel: true,
  },
  legend: {
    type: 'scroll',
    icon: 'rect',
    bottom: '0',
    left: 'center',
    data: [],
  },
  xAxis: [
    {
      type: 'category',
      boundaryGap: false,
      data: [],
      axisLabel: {
        // x轴文字的配置
        show: true,
        interval: 0, // 使x轴文字显示全
        fontSize: 12,
        rotate: 45, // 倾斜角度（正数顺时针，负数逆时针）
      },
    },
  ],
  yAxis: [
    {
      type: 'value',
    },
  ],
  series: [],
}

function initChart(chartData) {
  if (!lineChart) {
    lineChart = echarts.init(lineChartRef.value.$el)
  }

  option.xAxis[0].data = chartData.xData
  option.legend.data = chartData.yData.map((item, index) => item.name)
  option.series = chartData.yData.map((item, index) => ({
    name: item.name,
    type: 'line',
    smooth: true,
    areaStyle: {
      opacity: 0.3,
      //   color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
      //     {
      //       offset: 0,
      //       color: 'rgb(255, 191, 0)',
      //     },
      //     {
      //       offset: 1,
      //       color: 'rgb(224, 62, 76)',
      //     },
      //   ]),
    },
    data: item.data,
  }))

  const _opt = Object.assign(option, {
    ...props.extendOption,
  })
  lineChart.setOption(_opt)
}

function destroyEcharts() {
  if (lineChart) {
    lineChart.dispose()
    lineChart = null
  }
}

onMounted(() => {
  watch(
    () => props.chartData,
    async (val: any) => {
      // 判断数组里每个对象的 value 数组是否全为 "0"
      // const allZero = val.data.every((item: any) => {
      //   return item.value.every((val: any) => +val === 0)
      // })
      // isEmpty.value = !val.data.length || allZero
      // await sleep(500)
      // if (!isEmpty.value && lineChart.value) initEcharts(val)
      nextTick(() => {
        initChart(val)
      })
    },
    { immediate: true, deep: true },
  )
})

onBeforeUnmount(() => {
  destroyEcharts()
})
</script>

<style lang="scss" scoped>
.chart-wrapper {
  width: 100%;
  height: 500rpx;
}
</style>
