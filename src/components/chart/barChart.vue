<template>
  <view class="chart-wrapper" :style="{ height: `${height}rpx` }">
    <Empty v-if="isEmpty" />
    <view v-else class="w-full h-full" ref="barChartRef"></view>
  </view>
</template>
<script lang="ts" setup>
import echarts from '@/utils/echart'
import { IBarProps } from './type'
// import { sleep } from '@/utils/index'
import Empty from '@/components/empty/index.vue'

defineOptions({ name: 'BarChartComp' })

const props = withDefaults(defineProps<IBarProps>(), {
  height: 500,
  chartData: () => ({
    xData: [],
    yData: [],
  }),
  graphicColor: () => [],
  extendOption: () => ({}),
})

const isEmpty = ref(true)

const barChartRef = ref()

let barChart = null

const setBarColor = (params: any) => {
  const _color = [
    {
      offset: 0,
      color: '#58ACFF',
    },
    {
      offset: 1,
      color: '#0072E4',
    },
  ]
  if (props.graphicColor?.length > 0) {
    if (props.graphicColor?.length === 1) {
      _color[0].color = props.graphicColor[0][0]
      _color[1].color = props.graphicColor[0][1]
    } else {
      if (props.graphicColor[params.dataIndex]) {
        _color[0].color = props.graphicColor[params.dataIndex][0]
        _color[1].color = props.graphicColor[params.dataIndex][1]
      }
    }
  }

  return new echarts.graphic.LinearGradient(0, 0, 0, 1, _color)
}

const option = {
  grid: {
    left: '3%',
    right: '0%',
    bottom: '3%',
    containLabel: true,
  },
  xAxis: [
    {
      type: 'category',
      data: [],
      axisTick: {
        alignWithLabel: true,
        interval: 0,
      },
      axisLabel: {
        // x轴文字的配置
        show: true,
        interval: 0, // 使x轴文字显示全
        fontSize: 12,
        rotate: 45, // 倾斜角度（正数顺时针，负数逆时针）
        // formatter: function (value: string) {
        //   // 每4个字符换行，可根据需要自定义
        //   return value.length > 4 ? value.slice(0, 4) + '\n' + value.slice(4) : value
        // },
        formatter: function (params: string) {
          let newParamsName = ''
          const paramsNameNumber = params.length
          const provideNumber = 4 // 一行显示几个字
          if (paramsNameNumber > provideNumber) {
            newParamsName = params.slice(0, provideNumber) + '...'
          } else {
            newParamsName = params
          }
          return newParamsName
        },
      },
    },
  ],
  yAxis: [
    {
      type: 'value',
    },
  ],
  series: [
    {
      name: '',
      type: 'bar',
      barWidth: '40%',
      barMaxWidth: '30',
      itemStyle: {
        color: setBarColor,
        borderRadius: 30,
      },
      data: [],
    },
  ],
}

function initChart(chartData) {
  if (!barChart) {
    barChart = echarts.init(barChartRef.value.$el)
  }

  option.xAxis[0].data = chartData.xData
  option.series[0].data = chartData.yData

  const _opt = Object.assign(option, {
    ...props.extendOption,
  })
  barChart.setOption(_opt)
}

function destroyEcharts() {
  if (barChart) {
    barChart.dispose()
    barChart = null
  }
}

onMounted(() => {
  watch(
    () => props.chartData,
    async (val: any) => {
      // 判断数组里每个对象的 value 数组是否全为 "0"
      // const allZero = val.data.every((item: any) => {
      //   return item.value.every((val: any) => +val === 0)
      // })
      // isEmpty.value = !val.data.length || allZero
      // await sleep(500)
      // if (!isEmpty.value && barChart.value) initEcharts(val)
      isEmpty.value = !val.yData.length
      if (!isEmpty.value) {
        nextTick(() => {
          initChart(val)
        })
      } else {
        destroyEcharts()
      }
    },
    { immediate: true, deep: true },
  )
})

onBeforeUnmount(() => {
  destroyEcharts()
})
</script>

<style lang="scss" scoped>
.chart-wrapper {
  width: 100%;
  height: 500rpx;
  display: flex;
  align-items: center;
}
</style>
