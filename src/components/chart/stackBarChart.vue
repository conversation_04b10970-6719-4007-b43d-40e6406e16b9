<template>
  <view class="chart-wrapper" :style="{ height: `${height}rpx` }">
    <view class="w-full h-full" ref="stackBarChartRef"></view>
  </view>
</template>
<script lang="ts" setup>
import echarts from '@/utils/echart'
import { IStackBarProps } from './type'
// import { sleep } from '@/utils/index'

defineOptions({ name: 'BarChartComp' })

const props = withDefaults(defineProps<IStackBarProps>(), {
  height: 500,
  chartData: () => ({
    category: [],
    yData: [],
  }),
  barColor: () => [],
  extendOption: () => ({}),
})

const stackBarChartRef = ref()

let stackBarChart = null

const option = {
  grid: {
    left: '3%',
    right: '3%',
    bottom: '20%',
    containLabel: true,
  },
  legend: {
    type: 'scroll',
    icon: 'rect',
    bottom: '0',
    left: 'center',
    data: [],
  },
  xAxis: [
    {
      type: 'category',
      data: [],
    },
  ],
  yAxis: [
    {
      type: 'value',
    },
  ],
  series: [],
}

function initChart(chartData) {
  if (!stackBarChart) {
    stackBarChart = echarts.init(stackBarChartRef.value.$el)
  }
  const colorList = props.barColor

  option.xAxis[0].data = chartData.category
  option.legend.data = chartData.yData.map((item, index) => item.name)
  option.series = chartData.yData.map((item, index) => ({
    name: item.name,
    type: 'bar',
    stack: 'total',
    barWidth: '20%',
    itemStyle: {
      color: colorList[index],
    },
    label: {
      show: true,
      distance: 20,
      align: 'center',
      verticalAlign: 'middle',
      fontSize: 12,
      position: 'left',
      color: '#666',
      formatter: function (params: any) {
        return params.data ? `{insideLeft|${params.data} ▶}` : ''
      },
      rich: {
        insideLeft: {
          color: colorList[index],
          width: 0,
          lineHeight: 35,
          fontSize: '14',
        },
      },
    },
    data: item.data,
  }))

  const _opt = Object.assign(option, {
    ...props.extendOption,
  })
  stackBarChart.setOption(_opt)
}

function destroyEcharts() {
  if (stackBarChart) {
    stackBarChart.dispose()
    stackBarChart = null
  }
}

onMounted(() => {
  watch(
    () => props.chartData,
    async (val: any) => {
      // 判断数组里每个对象的 value 数组是否全为 "0"
      // const allZero = val.data.every((item: any) => {
      //   return item.value.every((val: any) => +val === 0)
      // })
      // isEmpty.value = !val.data.length || allZero
      // await sleep(500)
      // if (!isEmpty.value && stackBarChart.value) initEcharts(val)
      nextTick(() => {
        initChart(val)
      })
    },
    { immediate: true, deep: true },
  )
})

onBeforeUnmount(() => {
  destroyEcharts()
})
</script>

<style lang="scss" scoped>
.chart-wrapper {
  width: 100%;
  height: 500rpx;
}
</style>
