<template>
  <view class="chart-wrapper" :style="{ height: `${height}rpx` }">
    <view class="w-full h-full" ref="pieChartRef"></view>
  </view>
</template>
<script lang="ts" setup>
import echarts from '@/utils/echart'
import { IPieProps } from './type'
// import { sleep } from '@/utils/index'

defineOptions({ name: 'pieChartComp' })

const props = withDefaults(defineProps<IPieProps>(), {
  height: 500,
  chartData: () => [],
  extendOption: () => ({}),
})

const pieChartRef = ref()

let pieChart = null

const option = {
  //   legend: {
  //     type: 'scroll',
  //     orient: 'vertical',
  //     right: 0,
  //     top: 'middle',
  //     padding: [20, 10],
  //     width: '50',
  //     height: '50%',
  //     backgroundColor: 'transparent',
  //     borderColor: '#ccc',
  //     borderWidth: 1,
  //     borderRadius: 8,
  //     icon: 'circle',
  //   },
  grid: {
    left: '3%',
    right: '3%',
    bottom: '30%',
    containLabel: true,
  },
  legend: {
    type: 'scroll',
    icon: 'rect',
    bottom: '0',
    left: 'center',
    data: [],
  },
  series: [
    {
      name: '',
      type: 'pie',
      center: ['50%', '40%'],
      radius: ['40%', '60%'],
      left: 'left',
      //   width: '90%',
      itemStyle: {
        borderRadius: 10,
        borderColor: '#fff',
        borderWidth: 2,
      },
      label: {
        // position: 'inside',
        formatter: '{name|{b}}\n{value|{c}}条\n({percentage|{d}}%)',
        minMargin: 5,
        lineHeight: 15,
        rich: {
          //   percentage: {
          //     fontSize: 10,
          //     color: '#999',
          //   },
        },
      },
      labelLayout: function (params) {
        const isLeft = params.labelRect.x < pieChart.getWidth() / 2
        const points = params.labelLinePoints
        // Update the end point.
        points[2][0] = isLeft ? params.labelRect.x : params.labelRect.x + params.labelRect.width
        return {
          labelLinePoints: points,
        }
      },
      data: [],
    },
  ],
}

function initChart(chartData) {
  if (!pieChart) {
    pieChart = echarts.init(pieChartRef.value.$el)
  }

  option.legend.data = chartData.map((item, index) => item.name)
  option.series[0].data = chartData

  const _opt = Object.assign(option, {
    ...props.extendOption,
  })
  pieChart.setOption(_opt)
}

function destroyEcharts() {
  if (pieChart) {
    pieChart.dispose()
    pieChart = null
  }
}

onMounted(() => {
  watch(
    () => props.chartData,
    async (val: any) => {
      // 判断数组里每个对象的 value 数组是否全为 "0"
      // const allZero = val.data.every((item: any) => {
      //   return item.value.every((val: any) => +val === 0)
      // })
      // isEmpty.value = !val.data.length || allZero
      // await sleep(500)
      // if (!isEmpty.value && pieChart.value) initEcharts(val)
      nextTick(() => {
        initChart(val)
      })
    },
    { immediate: true, deep: true },
  )
})

onBeforeUnmount(() => {
  destroyEcharts()
})
</script>

<style lang="scss" scoped>
.chart-wrapper {
  width: 100%;
  height: 500rpx;
}
</style>
