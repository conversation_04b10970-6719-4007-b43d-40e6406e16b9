<template>
  <view class="chart-wrapper" :style="{ height: `${height}rpx` }">
    <view class="w-full h-full" ref="radarChartRef"></view>
  </view>
</template>
<script lang="ts" setup>
import echarts from '@/utils/echart'
import { IRadarRadarProps } from './type'
import { sleep } from '@/utils/index'
import upIcon from './assets/up_icon.png'
import downIcon from './assets/down_icon.png'

defineOptions({ name: 'RadarChartComp' })

const props = withDefaults(defineProps<IRadarRadarProps>(), {
  height: 500,
  chartData: () => ({
    indicator: [],
    data: [],
  }),
  compareMark: '',
  extendOption: () => ({}),
})

const radarChartRef = ref()

let radarChart = null

const option = {
  grid: {
    left: '10%',
    right: '10%',
    bottom: '15%',
    containLabel: true,
  },
  legend: {
    bottom: '0',
    left: 'center',
    data: [],
  },
  radar: [
    {
      shape: 'circle',
      center: ['50%', '50%'],
      radius: 80,
      indicator: [],
      axisName: {
        fontSize: 14,
        fontWeight: 700,
        color: '#333',
      },
    },
    {
      radius: 70,
      indicator: [],
      startAngle: -25,
      splitArea: {
        show: false,
      },
      axisLine: {
        show: false,
      },
      splitLine: {
        show: false,
      },
      axisName: {
        formatter: function (value) {
          const _indicator = props.chartData.indicator.map((item) => item.name)
          const _data = props.chartData.data

          const _axisName = []

          const _i = _indicator.indexOf(value)

          let compareData = null
          if (props.compareMark) {
            const filterList = _data.filter((item) => item.name === props.compareMark)
            compareData = filterList[0].value
          }

          _data.forEach((item) => {
            const _value = item.value[_i]

            let _text = `{legend|${item.axisName}: ${_value}}`
            if (props.compareMark && props.compareMark !== item.name) {
              if (_value > compareData[_i]) _text += ' {upIcon|}'
              if (_value < compareData[_i]) _text += ' {downIcon|}'
            }

            _axisName.push(_text)
          })
          return _axisName.join('\n')
        },
        rich: {
          legend: {
            align: 'lett',
          },
          upIcon: {
            height: 12,
            align: 'left',
            backgroundColor: {
              image: upIcon,
            },
          },
          downIcon: {
            height: 12,
            align: 'left',
            backgroundColor: {
              image: downIcon,
            },
          },
        },
        color: '#333',
        fontSize: 14,
        lineHeight: 18,
        backgroundColor: '#F2F4F7',
        padding: [5, 7],
        borderRadius: 4,
      },
    },
  ],
  series: [
    {
      name: '',
      type: 'radar',
      areaStyle: {
        opacity: 0.3,
      },
      data: [],
    },
  ],
}

function initChart(chartData) {
  if (!radarChart) {
    radarChart = echarts.init(radarChartRef.value.$el)
  }

  option.radar[0].indicator = chartData.indicator
  option.radar[1].indicator = chartData.indicator
  option.legend.data = chartData.data.map((item) => item.name)
  option.series[0].data = chartData.data

  const _opt = Object.assign(option, {
    ...props.extendOption,
  })
  radarChart.setOption(_opt)
}

function destroyEcharts() {
  if (radarChart) {
    radarChart.dispose()
    radarChart = null
  }
}

onMounted(() => {
  watch(
    () => props.chartData,
    async (val: any) => {
      // 判断数组里每个对象的 value 数组是否全为 "0"
      // const allZero = val.data.every((item: any) => {
      //   return item.value.every((val: any) => +val === 0)
      // })
      // isEmpty.value = !val.data.length || allZero
      // await sleep(500)
      // if (!isEmpty.value && radarChart.value) initEcharts(val)
      nextTick(() => {
        initChart(val)
      })
    },
    { immediate: true, deep: true },
  )
})

onBeforeUnmount(() => {
  destroyEcharts()
})
</script>

<style lang="scss" scoped>
.chart-wrapper {
  width: 100%;
  height: 500rpx;
}
</style>
