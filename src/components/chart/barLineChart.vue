<template>
  <view class="chart-wrapper" :style="{ height: `${height}rpx` }">
    <view class="w-full h-full" ref="barLineChartRef"></view>
  </view>
</template>
<script lang="ts" setup>
import echarts from '@/utils/echart'
import { IBarLineProps } from './type'
// import { sleep } from '@/utils/index'

defineOptions({ name: 'BarLineChartComp' })

const props = withDefaults(defineProps<IBarLineProps>(), {
  height: 500,
  chartData: () => ({
    xData: [],
    yData: [],
  }),
  graphicColor: () => [],
  extendOption: () => ({}),
})

const barLineChartRef = ref()

let barLineChart = null

const option = {
  grid: {
    left: '3%',
    right: '3%',
    bottom: '15%',
    containLabel: true,
  },
  legend: {
    type: 'scroll',
    // icon: 'rect',
    bottom: '0',
    left: 'center',
    data: [],
  },
  xAxis: [
    {
      type: 'category',
      data: [],
      axisTick: {
        alignWithLabel: true,
        interval: 0,
      },
      axisLabel: {
        // x轴文字的配置
        show: true,
        interval: 0, // 使x轴文字显示全
        fontSize: 12,
      },
    },
  ],
  yAxis: [
    {
      type: 'value',
      name: '',
      minInterval: 1,
    },
    {
      type: 'value',
      min: 0,
      max: 100,
      interval: 10,
      axisLabel: {
        formatter: '{value} %',
      },
    },
  ],
  series: [],
}

function initChart(chartData) {
  if (!barLineChart) {
    barLineChart = echarts.init(barLineChartRef.value.$el)
  }

  option.xAxis[0].data = chartData.xData
  option.legend.data = chartData.yData.map((item, index) => item.name)
  option.series = chartData.yData.map((item, index) => {
    if (item.type === 'bar') {
      return {
        name: item.name,
        type: 'bar',
        stack: 'total',
        barWidth: '40%',
        barMaxWidth: '30',
        data: item.data,
      }
    }
    if (item.type === 'line') {
      return {
        name: item.name,
        yAxisIndex: 1,
        type: 'line',
        smooth: true,
        data: item.data,
      }
    }
  })

  const _opt = Object.assign(option, {
    ...props.extendOption,
  })
  barLineChart.setOption(_opt)
}

function destroyEcharts() {
  if (barLineChart) {
    barLineChart.dispose()
    barLineChart = null
  }
}

onMounted(() => {
  watch(
    () => props.chartData,
    async (val: any) => {
      // 判断数组里每个对象的 value 数组是否全为 "0"
      // const allZero = val.data.every((item: any) => {
      //   return item.value.every((val: any) => +val === 0)
      // })
      // isEmpty.value = !val.data.length || allZero
      // await sleep(500)
      // if (!isEmpty.value && barLineChart.value) initEcharts(val)
      nextTick(() => {
        initChart(val)
      })
    },
    { immediate: true, deep: true },
  )
})

onBeforeUnmount(() => {
  destroyEcharts()
})
</script>

<style lang="scss" scoped>
.chart-wrapper {
  width: 100%;
  height: 500rpx;
}
</style>
