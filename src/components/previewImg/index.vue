<template>
  <view class="preview-box">
    <wd-popup v-model="popupShow" @close="handleClose">
      <view class="preview-imgs">
        <wd-swiper v-model:current="curIndex" :list="imgs" :autoplay="false"></wd-swiper>
      </view>
    </wd-popup>
  </view>
</template>

<script lang="ts" setup>
defineOptions({ name: 'PreviewImgs' })

interface IProps {
  show: boolean
  index: number
  imgs: string[]
}
const props = withDefaults(defineProps<IProps>(), {
  show: false,
  index: 0,
  info: [],
})

const emits = defineEmits(['update:show'])
const popupShow = computed({
  get: () => {
    return props.show
  },
  set: (v) => {
    emits('update:show', v)
  },
})
const curIndex = computed(() => props.index)

const handleClose = () => {
  popupShow.value = false
}
</script>

<style scoped lang="scss">
.preview-box {
  position: fixed;
  z-index: 9999;

  :deep(.wd-popup) {
    background: rgba(0, 0, 0, 0.6) !important;
    // border-radius: 0 !important;
  }
}
.preview-imgs {
  width: 100vw;
  height: 70vh;

  :deep(.wd-swiper) {
    height: 100% !important;
  }
  :deep(.wd-swiper__track) {
    height: 100% !important;
  }
}
</style>
