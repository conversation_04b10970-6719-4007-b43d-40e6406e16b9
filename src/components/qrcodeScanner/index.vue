<template>
  <view class="qrcode-wrapper">
    <view class="close-box" @click="onClose">
      <wd-icon :color="'#fff'" name="close-circle-filled" size="22px"></wd-icon>
    </view>
    <view id="qrcode-scanner" class="qrcode-reader"></view>
  </view>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import { Html5Qrcode } from 'html5-qrcode'
import type { QrcodeSuccessCallback, QrcodeErrorCallback } from 'html5-qrcode'
import { debounce } from '@/utils'
defineOptions({
  name: 'QrcodeScanner',
})

const props = defineProps({
  qrbox: {
    type: Number,
    default: 250,
  },
  fps: {
    type: Number,
    default: 10,
  },
})

const emit = defineEmits(['result', 'fail', 'colse'])

let html5Qrcode = null
const qrcodeConfig = {
  // aspectRatio: 9 / 16,
  fps: props.fps,
  // qrbox: props.qrbox,
  qrbox: {
    width: 280,
    height: 280,
  },
}

// const initTime = Date.now()

// function init() {
//   // console.log('init-qrcode')
//   html5Qrcode = new Html5Qrcode('qrcode-scanner', false)
//   // console.log('🚀 ~ getState :', html5Qrcode.getState())
//   if (html5Qrcode.getState() > 0) {
//     html5Qrcode.start({ facingMode: 'environment' }, qrcodeConfig, onScanSuccess, onScanError)
//   } else {
//     html5Qrcode.clear()
//     emit('colse')
//   }
// }

const init = debounce(() => {
  // console.log('init-qrcode3')
  console.time()
  html5Qrcode = new Html5Qrcode('qrcode-scanner', false)
  // console.log('🚀 ~ getState :', html5Qrcode.getState())
  if (html5Qrcode.getState() > 0) {
    html5Qrcode.start({ facingMode: 'environment' }, qrcodeConfig, onScanSuccess, onScanError)
  } else {
    html5Qrcode.clear()
    emit('colse')
  }
}, 1000)

const onScanSuccess: QrcodeSuccessCallback = (decodedText, res) => {
  html5Qrcode
    .stop()
    .then(() => {
      emit('result', decodedText, res)
      html5Qrcode.clear()
    })
    .catch(() => { })
}
const onScanError: QrcodeErrorCallback = (message, error) => {
  // const now = Date.now();
  // if(now - initTime > 5e3) {
  // 	html5Qrcode.stop().finally(() => {
  // 		emit('fail', message, error);
  // 		html5Qrcode.clear()
  // 	})
  // }
}

function onClose() {
  if (html5Qrcode.isScanning) {
    html5Qrcode.stop().finally(() => {
      html5Qrcode.clear()
      emit('colse')
    })
  }
  emit('colse')
}

// onShow(() => {
//   // console.log('onShow1')
//   // init()
// })

onMounted(() => {
  setTimeout(() => {
    init()
  }, 100)
})
</script>

<style scoped>
.qrcode-wrapper {
  position: relative;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  background-color: #000;
}

.close-box {
  position: absolute;
  top: 80rpx;
  right: 30rpx;
  z-index: 9;
  padding: 8px;
  border-radius: 50%;
}

.qrcode-reader {
  position: absolute;
  top: 50%;
  width: 100%;
  transform: translateY(-50%);
}
</style>
