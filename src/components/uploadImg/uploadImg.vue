<template>
  <uni-file-picker ref="uploadImg" :imageStyles="imageStyles" :readonly="readonlyValue" :value="files" :limit="limit"
    @select="select" :sourceType="sourceType" @delete="del"></uni-file-picker>
  <view class="cavas_block">
    <mu-canvas ref="canvasImage" :width="650" unit="rpx" :height="1300" backgroundColor="#FFFFFF"
      :elementList="shareImageList" :auto="false"></mu-canvas>
  </view>
</template>

<script setup>
import { ref, reactive, watch } from 'vue'
import { showToast } from '@/utils'
import { upfile } from '@/utils/req'
import UniFilePicker from '@/uni_modules/uni-file-picker/components/uni-file-picker/uni-file-picker.vue'
import MuCanvas from '@/uni_modules/mu-canvas/components/mu-canvas/mu-canvas.vue'
import { useAppStore, usefileConfigStore, useUserStore } from '@/store'
// import { getWatermarkBizData } from './featch.js'
// const ocrUrl = import.meta.env.VITE_SERVER_MODEL_OCR_URL
// const imgBaseUrl = import.meta.env.VITE_PREVIEW_BASEURL
const usefileStore = usefileConfigStore()
// const VITE_PREVIEW_BASEURL = ref(usefileStore.fileUrl + '/')
const imgBaseUrl = ref(usefileStore.fileUrl + '/')
// getbaseurl()
// // eslint-disable-next-line camelcase
// function getbaseurl() {
//   const dynamicsUrl = location.origin
//   if (location.protocol === 'https:') {
//     return (VITE_PREVIEW_BASEURL.value = dynamicsUrl + import.meta.env.VITE_PREVIEW_BASEURL_dev)
//   } else {
//     return (VITE_PREVIEW_BASEURL.value = import.meta.env.VITE_PREVIEW_BASEURL)
//   }
// }
const VITE_UPLOAD_BASEURL = import.meta.env.VITE_UPLOAD_BASEURL
const VITE_SERVER_PLATFORM = import.meta.env.VITE_SERVER_PLATFORM

const props = defineProps({
  sourceType: {
    type: Array,
    default: () => {
      return ['album', 'camera']
    },
  },
  modelValue: {
    // '值'
    type: String,
    default: '',
  },
  readonlyValue: {
    // '值'
    type: Boolean,
    default: false,
  },
  limit: {
    // 上传文件数量
    type: [String, Number],
    default: 10, // 隐患排查所有的上传图片，默认上传3张
  },
  size: {
    // 上传文件大小
    type: [String, Number],
    default: 10,
  },
  type: {
    // 上传文件类型限制
    type: String,
    default: () => 'png,jpg,jpeg',
  },
  imageStyles: {
    // 图片样式
    type: Object,
    default: () => { },
  },
  fileList: {
    // 回显已文件列表
    type: Array,
    default: () => [],
  },
  ocr: {
    // 是否需要调用识别
    type: Boolean,
    default: false,
  },
  ocrType: {
    type: Number,
    required: false,
  },
  customCanvas: {
    // 自定义画布
    type: Boolean,
    default: false,
  },
  positionName: {
    // 位置名
    type: String,
    default: '',
  },
})
const emit = defineEmits(['update:modelValue', 'change', 'upAllImage'])
const uploadImg = ref(null)
// 文件ID
const fileIds = ref([])
// 回显文件
const files = ref([])
const canvasImage = ref(null)
const shareImageList = reactive({
  images: [
    {
      path: '', // 图片路径[必填]
      dx: 0, // 左上角x轴[必填]
      dy: 0, // 左上角y轴[必填]
      dWidth: 325, // 宽度[必填]
      dHeight: 500, // 高度[必填]
      sx: 0, // 绘画的宽度[选填]
      sy: 0, // 绘画的高度[选填]
    },
  ],
  text: [
    {
      value: `拍照时间：`, // 渲染的文字
      color: '#000000', // 文字颜色[选填]
      size: '15', // 大小[选填]
      font: '', // 字体[选填]
      x: 20, // 左上角x轴[必填]
      y: 530, // 左上角y轴[必填]
      // backgroundColor: "#ffffff", //背景色
      maxWidth: '', // 字体最大宽度
      maxHeight: '', // 字体最大高度
    },
  ],
  qr: [
    // 渲染的二维码
    {
      url: '', // 二维码文字[必填]
      dx: 72, // 左上角x轴[必填]
      dy: 240, // 左上角y轴[必填]
      size: 210, // 二维码大小
      color: '#000000', // 二维码前景色
      backgroundColor: '#ffffff', // 二维码背景色
    },
  ],
})

// 校验并上传文件
const select = async (e) => {
  for (const fileObj of e.tempFiles) {
    const index = e.tempFiles.indexOf(fileObj)
    // 判断文件大小
    if (fileObj.size / 1024 / 1024 > props.size) {
      showToast('', '每张图片最大上传为10M!')
      uploadImg.value.clearFiles(index)
      continue
    }
    // 判断文件类型
    const typeList = props.type.split(',')
    if (typeList.length && !typeList.includes(fileObj.extname)) {
      showToast('', `仅支持上传${props.type}等类型`)
      uploadImg.value.clearFiles(index)
      continue
    }
    // 需要图片识别
    if (props.ocr) {
      uni.showLoading({
        title: '图片识别中...',
        duration: 2000,
      })
      uni.uploadFile({
        url: VITE_UPLOAD_BASEURL, // 图片识别接口
        filePath: e.tempFilePaths[index],
        name: 'file',
        formData: {
          type_of_card: props.ocrType, // 识别国徽两面参数，1：国徽面，2：人像面
        },
        success: (uploadFileRes) => {
          if (
            uploadFileRes.statusCode === 200 ||
            uploadFileRes.statusCode === '200' ||
            uploadFileRes.statusCode === 'success'
          ) {
            const result = JSON.parse(uploadFileRes.data)
            emit('set', result)
          }
          uni.hideLoading()
        },
        fail: (err) => {
          uni.hideLoading()
          // console.log('err:', err)
          // reject(err)
        },
      })
    }
    if (props.customCanvas) {
      const res = await upfile(
        '/file/uploads',
        { filePath: e.tempFilePaths[index] },
        'ehs-clnt-device-app',
        'post',
      )
      if ((res.code === 200 || res.code === '200' || res.code === 'success') && res.data) {
        uni.showLoading({
          title: '上传中...',
          icon: 'none',
        })
        try {
          const path = res.data.filePath
          shareImageList.text[0].value = `拍照时间：${getNowDate()}`
          shareImageList.images[0].path = `${imgBaseUrl.value}${path}`
          canvasImage.value.redraw(async (e) => {
            // console.log('e:', e)
            const srcs = await canvasImage.value.getImgSrc()
            const ret = await upfile('/file/uploads', { filePath: srcs }, '/ehs-clnt-device-app')
            if (ret.data && (ret.code === 200 || ret.code === '200' || ret.code === 'success')) {
              fileIds.value.push(ret.data.id)
              res.data.url = imgBaseUrl.value + ret.data.path
              files.value.push(res.data)
              emit('upAllImage', files.value)
            } else {
              uploadImg.value.clearFiles(index)
            }
            uni.hideLoading()
          })
        } catch (e) {
          uni.hideLoading()
          uni.showToast({
            title: `${e}`,
            duration: 1000,
            icon: 'none',
          })
        }
      } else {
        uploadImg.value.clearFiles(index)
      }
    } else {
      const res = await upfile(
        '/file/upload',
        { filePath: e.tempFilePaths[index] },
        '/ehs-clnt-device-app',
        'post',
      )
      if ((res.code === 200 || res.code === '200' || res.code === 'success') && res.data) {
        fileIds.value.push(res.data.id)
        res.data.url = imgBaseUrl.value + res.data.path
        files.value.push(res.data)
        emit('upAllImage', files.value)
      } else {
        uploadImg.value.clearFiles(index)
      }
    }
  }
}

const getNowDate = () => {
  const date = new Date()
  const year = date.getFullYear().toString().padStart(4, '0')
  const month = (date.getMonth() + 1).toString().padStart(2, '0')
  const day = date.getDate().toString().padStart(2, '0')
  const hours = date.getHours().toString().padStart(2, '0')
  const minutes = date.getMinutes().toString().padStart(2, '0')
  const seconds = date.getSeconds().toString().padStart(2, '0')
  return `${year}年${month}月${day}日 ${hours}:${minutes}:${seconds}`
}

const del = (n) => {
  files.value.splice(n.index, 1)
  fileIds.value = files.value.map((item) => item.id)
}
watch(
  () => fileIds.value,
  (nVal) => {
    if (nVal) {
      emit('update:modelValue', fileIds.value.join(','))
    }
  },
  { deep: true },
)

// 回显
watch(
  () => props.fileList,
  (nVal) => {
    files.value = nVal.map((item) => {
      const flag = item.path.includes('http')
      return {
        id: item.id,
        name: item.fileName,
        url: flag ? item.path : imgBaseUrl.value + item.path,
      }
    })
    fileIds.value = nVal.map((item) => item.id)
  },
  { immediate: true, deep: true },
)
// watch(
//   () => props.modelValue,
//   (nVal) => {
//     // console.log('nVal', nVal)
//     fileIds.value = props.modelValue.split(',')
//   },
// )
</script>
<style lang="scss" scoped>
.cavas_block {
  position: fixed;
  top: 99999rpx;
  z-index: 999999;
  overflow: hidden;
  background-color: #fff;
}
</style>
