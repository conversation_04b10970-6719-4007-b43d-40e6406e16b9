<template>
  <view class="header-bar" :style="{ paddingTop: appInfo.top + 'px' }">
    <text class="text-2xl color-white">页面头部</text>
  </view>
</template>

<script lang="ts" setup>
import { useAppStore } from '@/store'

defineOptions({ name: 'HeaderBar' })

const store = useAppStore()

const appInfo = computed(() => {
  return store.appInfo
})

// console.log(JSON.stringify(appInfo))
</script>

<style lang="scss" scoped>
.header-bar {
  @apply w-full center;
  background-color: aquamarine;
}
</style>
