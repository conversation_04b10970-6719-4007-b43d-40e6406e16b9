<template>
  <view class="tab-wrapper">
    <text
      v-for="(item, index) of tabs"
      :key="index"
      :class="['tab-item', item.value === curTab ? 'tab-item_actived' : '']"
      @click="changeHandle(item.value)"
    >
      {{ item.label }}
    </text>
  </view>
</template>
<script lang="ts" setup>
import { IProps } from './type'

defineOptions({ name: 'tabComp' })

const props = withDefaults(defineProps<IProps>(), {
  value: '',
  tabs: [],
})

const emits = defineEmits(['update:value'])

const curTab = computed({
  get() {
    return props.value
  },
  set(value) {
    emits('update:value', value)
  },
})

const changeHandle = (val) => {
  curTab.value = val
}
</script>

<style lang="scss" scoped>
.tab-wrapper {
  display: flex;
  flex-flow: row nowrap;
  align-items: center;
  justify-content: flex-start;
  border-radius: 8rpx;
  background: #fff;
  overflow: hidden;
  .tab-item {
    cursor: pointer;
    padding: 10rpx 24rpx;
    font-size: 24rpx;
    line-height: 32rpx;
    color: #333;
    border: 1px solid #dcdfe6;
    &.tab-item_actived {
      color: #fff;
      background: #0080ff;
      border: 1px solid #0080ff;
    }
  }
}
</style>
