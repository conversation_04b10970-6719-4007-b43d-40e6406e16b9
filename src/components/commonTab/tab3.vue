<template>
  <view class="tab-wrapper">
    <text
      v-for="(item, index) of tabs"
      :key="index"
      :class="['tab-item', item.value === curTab ? 'tab-item_actived' : '']"
      @click="changeHandle(item.value)"
    >
      {{ item.label }}
    </text>
  </view>
</template>
<script lang="ts" setup>
import { IProps } from './type'

defineOptions({ name: 'tabComp' })

const props = withDefaults(defineProps<IProps>(), {
  value: '',
  tabs: [],
})

const emits = defineEmits(['update:value'])

const curTab = computed({
  get() {
    return props.value
  },
  set(value) {
    emits('update:value', value)
  },
})

const changeHandle = (val) => {
  curTab.value = val
}
</script>

<style lang="scss" scoped>
.tab-wrapper {
  display: flex;
  flex-flow: row nowrap;
  align-items: center;
  justify-content: flex-start;
  gap: 0;
  border: 1px solid #0256ff;
  border-radius: 10rpx;
  background: #fff;
  overflow: hidden;
  .tab-item {
    flex: 1;
    padding: 14rpx 16rpx;
    font-size: 28rpx;
    line-height: 44rpx;
    color: #0256ff;
    border-right: 1px solid #0256ff;
    white-space: nowrap;
    &:last-of-type {
      border-right: none;
    }

    &.tab-item_actived {
      color: #fff;
      background: #0256ff;
    }
  }
}
</style>
