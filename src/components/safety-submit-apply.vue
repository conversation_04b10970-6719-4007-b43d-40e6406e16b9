<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: 'submit',
  },
}
</route>

<template>
  <view class="flex fixed left-0 bottom-0 w-100vw h-12.5 text-center font-size-4 leading-12.5"
    style="border-top: 0.0625rem solid #e6e6e6">
    <view class="flex-1 color-#969696 bg-white" @click="cancelHandler">取消</view>
    <view class="flex-1 color-white bg-#e6a23c" @click="submitHandler(0)" v-if="props.showDraft">
      保存草稿
    </view>
    <view class="flex-1 color-white" style="background-color: #0256ff" @click="submitHandler(1)" v-if="showSubmit">
      提交
    </view>
    <wd-message-box />
  </view>
</template>

<script lang="ts" setup>
import { useMessage } from 'wot-design-uni'

interface Props {
  showDraft?: boolean
  showSubmit?: boolean
  messageTitle?: string
}

const props = withDefaults(defineProps<Props>(), { showDraft: false, showSubmit: true })

const $emit = defineEmits(['submit', 'cancel'])

const message = useMessage()
function cancelHandler() {
  if (props.messageTitle) {
    message
      .confirm({
        title: props.messageTitle,
      })
      .then(() => {
        $emit('cancel')
      })
      .catch(() => {
        // // console.log('点击了取消按钮')
      })
  } else {
    $emit('cancel')
  }
}

function submitHandler(type) {
  $emit('submit', type)
}
</script>
