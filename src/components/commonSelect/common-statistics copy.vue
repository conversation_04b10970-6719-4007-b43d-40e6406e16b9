<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '统计部分',
  },
}
</route>
<template>
  <view class="infobox">
    <view class="titbefor"></view>
    <!-- 随机检查详情页显示 -->
    <view v-if="props.type === 0" class="box">
      <div class="box-itme">
        <view class="imgbox">
          <image class="img" src="../../static/pages/nav/rwzyh.png" />
        </view>
        <view style="margin-left: 0.625rem">
          <p style="font-size: 14px">任务总隐患数</p>
          <p style="font-size: 20px">{{ model.total || 0 }}</p>
        </view>
      </div>
      <div class="box-itme">
        <view class="imgbox">
          <image class="img" src="../../static/pages/nav/sbdyh.png" />
        </view>
        <view style="margin-left: 0.625rem">
          <p style="font-size: 14px">我上报的隐患</p>
          <p style="font-size: 20px">{{ model.disposedNum || 0 }}</p>
        </view>
      </div>
    </view>
    <!-- 隐患清单显示 -->
    <view v-if="props.type === 1" class="box">
      <div class="box-itme">
        <view class="imgbox">
          <image class="img" src="../../static/pages/nav/yzg.png" />
        </view>
        <view style="margin-left: 0.625rem">
          <p style="font-size: 14px">已整改</p>
          <p style="font-size: 20px">{{ model.disposedNum || 0 }}</p>
        </view>
      </div>
      <div class="box-itme">
        <view class="imgbox">
          <image class="img" src="../../static/pages/nav/sbdyh.png" />
        </view>
        <view style="margin-left: 0.625rem">
          <p style="font-size: 14px">待整改</p>
          <p style="font-size: 20px">{{ model.unDisposedNum || 0 }}</p>
        </view>
      </div>
    </view>
    <view class="box">
      <div class="box-itme2">
        <view style="box-sizing: border-box; padding: 0px 4px; text-align: center"
          v-for="(item, index) in levelStatisticsList" :key="index">
          <p style="font-size: 14px">{{ item.hazardLevelName }}</p>
          <p style="font-size: 20px; color: #e23b50" :style="item.hazardLevel === 1
            ? 'color: #e23b50'
            : item.hazardLevel === 2
              ? 'color: #f59a23'
              : item.hazardLevel === 3
                ? 'color: #bfbf00'
                : 'color: #76b90e'
            ">
            {{ item.total }}
          </p>
        </view>
      </div>
    </view>
  </view>
</template>

<script lang="ts" setup>
import {
  posthazardPlanTaskEventlevelStatisticsAPI,
  posthazardPlanTaskEventStatisticsAPI,
  posthazardRecordlevelStatisticsAPI,
  posthazardRecordStatisticsAPI,
} from './featch'
import { useUserStore } from '@/store'
const userStore = useUserStore()
const userinfo: any = userStore.userInfo ? userStore.userInfo : {}
const props = defineProps<{
  type: number
  taskState?: string
  taskId?: string
}>()
// // console.log(props.taskState, '======================aaaa')
/* // 隐患事件统计 */
const model = ref<any>({})
/* 隐患等级统计 */
const levelStatisticsList = ref<any>()

const fromdata = ref<any>({ unitId: userinfo.unitId })
const fromdatadanger = ref<any>({
  reformUserId: userinfo.id,
  unitId: userinfo.unitId,
  roleCodes: userinfo.roleCodes,
})

if (props.taskState === '1' || props.taskState === '2') {
  unhazardRecordStatistics()
  unhazardRecordStatistics(userinfo.id)
  GetunlevelStatistics()
} else {
  if (props.type === 1) {
    hazardRecordStatistics(fromdata.value)
    GetlevelStatistics(fromdatadanger.value)
  } else {
    // fromdata.value.randomCheckId = props.taskId

    // Vue.delete(fromdata, 'reformUserId')
    hazardRecordStatistics(fromdata.value)
    hazardRecordStatistics(fromdata.value, userinfo.id)
    GetlevelStatistics(fromdata.value)
    // // console.log(90)
  }
}
// props.taskState === '2'

function hazardRecordStatistics(info, e = null) {
  // // console.log(info, 22)
  posthazardRecordStatisticsAPI({ ...info, createBy: e })
    .then((res: any) => {
      if (e) {
        model.value.total = res.data.total
      } else {
        model.value.disposedNum = res.data.total
      }
    })
    .finally(() => { })
}
// props.taskState === '2'
function GetlevelStatistics(info) {
  // // console.log(info, 78)

  posthazardRecordlevelStatisticsAPI(info)
    .then((res: any) => {
      levelStatisticsList.value = res.data
    })
    .finally(() => { })
}
// 任务未完成请求的统计接口
function unhazardRecordStatistics(e = null) {
  posthazardPlanTaskEventStatisticsAPI({
    randomCheckId: props.taskId,
    unitId: userinfo.unitId,
    createBy: e,
  })
    .then((res: any) => {
      if (e) {
        model.value.total = res.data.total
      } else {
        model.value.disposedNum = res.data.total
      }
    })
    .finally(() => { })
}
function GetunlevelStatistics() {
  posthazardPlanTaskEventlevelStatisticsAPI({
    randomCheckId: props.taskId,
    unitId: userinfo.unitId,
    delFlag: 0,
  })
    .then((res: any) => {
      levelStatisticsList.value = res.data
    })
    .finally(() => { })
}
</script>

<style lang="scss" scoped>
.infobox {
  margin-bottom: 0.625rem;
}

.box {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  justify-content: space-between;

  .box-itme {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 10.25rem;
    height: 4.375rem;
    margin: 0.3125rem;
    background-color: rgba(188, 201, 218, 0.2);
    border-radius: 0.75rem;

    .imgbox {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 2.25rem;
      height: 2.25rem;

      .img {
        width: 100%;
        height: 100%;
      }
    }
  }

  .box-itme2 {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-around;
    width: 100%;
    height: 80px;
    padding: 10px 0px;
    //height: 3.4375rem;
    margin: 0.3125rem;
    background-color: rgba(188, 201, 218, 0.2);
    border-radius: 12px;
  }
}
</style>
