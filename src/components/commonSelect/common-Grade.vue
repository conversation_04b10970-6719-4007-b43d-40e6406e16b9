<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '隐患等级列表',
  },
}
</route>
<template>
  <wd-select-picker label="隐患等级" placeholder="请选择隐患等级" v-model="selvalue" :columns="gradeList" value-key="id"
    label-key="gradeName" align-right type="radio" required @confirm="handleConfirm"></wd-select-picker>
</template>

<script lang="ts" setup>
import { useUserStore } from '@/store'
import { posthazardGradeAPI } from './featch'
const props = defineProps<{
  selectvalue?: string
}>()
watch(
  () => props.selectvalue,
  (newValue) => {
    selvalue.value = props.selectvalue
  },
)

// import { posthazardEssentialFactorClassAPI } from '@/pages/hazardPhoto/featch'
const userStore = useUserStore()
const userinfo: any = userStore.userInfo ? userStore.userInfo : {}
const $emit = defineEmits(['getGrade'])
// 隐患分类列表
const gradeList = ref<any[]>()
// topUnitId
posthazardGradeAPI({ unitId: userinfo.topUnitId })
  .then((res) => {
    gradeList.value = res.data as any[]
  })
  .finally(() => { })
const selvalue = ref<string>('')
function handleConfirm({ value, selectedItems }) {
  $emit('getGrade', selectedItems)
  //   model.value.hazardCategoryName = selectedItems.label
}
onShow(() => {
  // // console.log(props.selectvalue, '========================================================')
  selvalue.value = props.selectvalue
  // // console.log(selvalue.value, '===============')
})
// function convertTocolumns(items) {
//   return items.map((item) => ({
//     label: item.gradeName,
//     value: item.id,
//   }))
// }
</script>

<style lang="scss" scoped></style>
