<template>
  <wd-cell required :title="title" title-width="100px" is-link @click="handleOpen">
    <view v-if="value" style="color: #000000d9">{{ value.hazardSourceName }}</view>
    <view v-else style="color: #bfbfbf">请选择{{ title }}</view>
  </wd-cell>
  <wd-popup
    v-if="show"
    z-index="999"
    v-model="show"
    position="bottom"
    lock-scroll
    :safe-area-inset-bottom="true"
    @close="handleClose"
    custom-style="height: 75vh;"
  >
    <view class="pop-title">
      {{ title }}
      <wd-icon class="pop-cancel" name="close" size="16px" @click="handleClose"></wd-icon>
    </view>
    <scroll-view style="height: 67vh" scroll-y="true">
      <view v-if="hazardSourcecolumns.length > 0">
        <wd-radio-group v-model="value" cell @change="onChange">
          <wd-radio v-for="(itme, index) in hazardSourcecolumns" :key="index" :value="itme">
            {{ itme.hazardSourceName }}
          </wd-radio>
        </wd-radio-group>
      </view>
      <view v-else style="text-align: center; color: #ccc">暂无数据</view>
    </scroll-view>
  </wd-popup>
</template>

<script lang="ts" setup>
import { posthazardSourceListAPI } from '@/pages/hazardPhoto/featch'

const props = defineProps<{
  title: {
    type: string
    default: ''
  }
  dataList?: {
    type: Array<any>
    default: () => []
  }
}>()

const Emits = defineEmits(['senddate'])
const show = ref(false)
const value = ref('')
const valueid = ref('')

// 隐患来源
const hazardSourcecolumns = ref<any>([])
hazardSourceList()
function hazardSourceList() {
  // console.log(unitIdval, '================调用获取隐患等级')
  posthazardSourceListAPI({})
    .then((res: any) => {
      hazardSourcecolumns.value = res.data
    })
    .finally(() => {
      // console.log(111)
    })
}

function handleOpen() {
  if (hazardSourcecolumns.value.length === 0) {
    uni.showToast({
      icon: 'none',
      title: '无可用选项，请联系管理员在参数配置中进行配置。',
    })
  } else {
    show.value = true
  }
}
function handleClose() {
  show.value = false
}
// 选择
function onChange(e: any) {
  console.log(e)
  Emits('senddate', e.value)
  handleClose()
}
// watch(
//     () => props.dataList,
//     (newVal, oldVal) => {
//         dataListNew.value = props.dataList
//         console.log(dataListNew.value)
//     },
// )
</script>

<style lang="scss" scoped>
.pop-title {
  position: relative;
  width: 100%;
  margin: 20px auto 10px;
  font-size: 16px;
  color: #000000;
  text-align: center;
  font-weight: bold;

  .pop-cancel {
    position: absolute;
    top: 0px;
    right: 20px;
    color: rgb(175, 175, 175);
  }
}
</style>
