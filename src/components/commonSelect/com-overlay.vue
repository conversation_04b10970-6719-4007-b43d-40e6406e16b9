<template>
  <wd-overlay style="z-index: 999;" :show="true" @click="closeimg">
    <view class="wrapper">
      <view class="block">
        <img :src="url" @load="onImageLoad" />
      </view>
    </view>
  </wd-overlay>
</template>
<script lang="ts" setup>
const props = defineProps<{
  imgurl?: string
}>()
// // console.log(props.imgurl)
const url = ref(props.imgurl)
const $emit = defineEmits(['closeimg'])
function closeimg() {
  $emit('closeimg', false)
}
function onImageLoad(e) {
  // 获取图片原始宽度和高度
  // // console.log(e)
  // const { width, height } = e.detail;
}
</script>

<style lang="scss" scoped>
.wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.block {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 80vh;
  // top: 5%;
  // background-color: #fff;
}

img {
  /* 高度自动调整以保持图片比例 */
  display: block;
  /* 最大宽度不超过父容器的90% */
  max-width: 90%;
  height: auto;
  max-height: 85vh;
  /* 使图片表现得像块级元素，可以设置宽度和高度 */
}
</style>
