<!--
 * @Author: 悦悦 <EMAIL>
 * @Date: 2024-10-22 09:54:56
 * @LastEditors: <EMAIL> <EMAIL>
 * @LastEditTime: 2024-12-19 22:33:14
 * @FilePath: /隐患排查app/src/components/commonSelect/common-Unit.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '隐患等级列表',
  },
}
</route>
<template>
  <wd-select-picker z-index="1000" label="隐患人员单位" placeholder="请选择隐患人员单位" v-model="value" :columns="gradeList"
    value-key="id" label-key="unitName" align-right type="radio" @confirm="handleConfirm"></wd-select-picker>
</template>

<script lang="ts" setup>
import { getAllUnit } from '@/pages/task/featch'
import { useUserStore } from '@/store'
const props = defineProps<{
  selectvalue?: string
  types?: string
  selectUint?: string
}>()
const types = ref<string>(props.types)
// import { posthazardEssentialFactorClassAPI } from '@/pages/hazardPhoto/featch'
const userStore = useUserStore()
const userinfo: any = userStore.userInfo ? userStore.userInfo : {}
const $emit = defineEmits(['getGrade'])
// 隐患分类列表
const gradeList = ref<any[]>()
// console.log('🚀 ~ props:', props)

getAllUnit({ orgCode: props.selectUint, pageSize: -1 })
  .then((res: any) => {
    gradeList.value = res.data.rows
  })
  .finally(() => { })
// if (types.value === '1') {
//   // topUnitId取值改为unitId
//   getAllUnit({ orgCode: userinfo.unitId, pageSize: -1 })
//     .then((res: any) => {
//       gradeList.value = res.data.rows
//     })
//     .finally(() => {})
// } else {
//   gradeList.value = uni.getStorageSync('unitList')
//   // console.log('🚀 ~ gradeList.value:', gradeList.value)
// }

const value = ref<string>(props.selectvalue)
function handleConfirm({ value, selectedItems }) {
  //   // console.log('🚀 ~ value:', value)
  //   // console.log('🚀 ~ selectedItems:', selectedItems)
  $emit('getGrade', selectedItems.id)
  //   model.value.hazardCategoryName = selectedItems.label
}

// function convertTocolumns(items) {
//   return items.map((item) => ({
//     label: item.gradeName,
//     value: item.id,
//   }))
// }
</script>

<style lang="scss" scoped></style>
