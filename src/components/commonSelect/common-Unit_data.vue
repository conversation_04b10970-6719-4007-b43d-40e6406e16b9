<!--
 * @Author: 悦悦 <EMAIL>
 * @Date: 2024-10-22 09:54:56
 * @LastEditors: fangweiwei <EMAIL>
 * @LastEditTime: 2025-01-14 22:31:47
 * @FilePath: /隐患排查app/src/components/commonSelect/common-Unit.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<!-- <route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '隐患等级列表',
  },
}
</route> -->
<template>
  <!-- <wd-select-picker label="隐患单位" placeholder="请选择隐患单位" v-model="value" :columns="gradeList" value-key="id"
    label-key="unitName" z-index="999" align-right type="radio" required @confirm="handleConfirm"></wd-select-picker> -->
  <!-- <wd-cell is-link @click="open()">
        <view v-if="valuetext" style="color: #000000d9">{{ valuetext }}</view>
        <view v-else style="color: #bfbfbf">请选择单位</view>
    </wd-cell> -->
  <view :class="['selcell', { disabled: isdisabled }]" @click="open()">
    <view v-if="valuetext" style="color: #000000d9" class="ellipsis">{{ valuetext }}</view>
    <view v-else style="color: #bfbfbf" class="ellipsis">请选择单位</view>
    <view>
      <wd-icon name="arrow-down" size="16px" v-if="!show"></wd-icon>
      <wd-icon name="arrow-up" size="16px" v-else></wd-icon>
    </view>
  </view>
  <wd-popup v-if="show" z-index="999" v-model="show" position="bottom" lock-scroll :safe-area-inset-bottom="true"
    @close="show = false" custom-style="height: 75vh;">
    <view class="pop-title">
      隐患单位
      <wd-icon class="pop-cancel" name="close" size="16px" @click="show = false"></wd-icon>
    </view>
    <wd-search v-model="searchValue" placeholder="公司名称模糊搜索" hide-cancel placeholder-left />
    <scroll-view style="height: 60vh" scroll-y="true">
      <DaTree v-if="treeData.length > 0" :data="treeData" labelField="text" valueField="id" defaultExpandAll
        :defaultCheckedKeys="value" @change="handleTreeChange" :filterValue="searchValue">
      </DaTree>
      <!-- <DaTree ref="zzjgref" v-if="treeData.length > 0" :data="treeData" labelField="text" :filterValue="searchValue"
        valueField="id" :defaultCheckedKeys="value" @change="handleTreeChange" :onlyRadioLeaf="true"></DaTree> -->
      <view v-else style="text-align: center; color: #ccc">暂无数据</view>
    </scroll-view>
  </wd-popup>
</template>

<script lang="ts" setup>
import { getOrgTree } from '@/pages/task/featch'
import DaTree from '@/components/da-tree/index.vue'
import { useUserStore } from '@/store'
// import { debounce } from '@/utils'
const props = defineProps<{
  selectvalue?: string
  types?: string
  selecttext?: string
}>()
const types = ref<string>(props.types)
// import { posthazardEssentialFactorClassAPI } from '@/pages/hazardPhoto/featch'
const userStore = useUserStore()
const userinfo: any = userStore.userInfo ? userStore.userInfo : {}
const $emit = defineEmits(['getUnit'])
const show = ref(false)
// 隐患分类列表
const treeData = ref<any[]>()
console.log('🚀 ~ props:', props.selecttext)
const value = ref<string>(props.selectvalue || '')
const valuetext = ref<string>(props.selecttext || '')
const searchValue = ref('')
const isdisabled = ref(false)
// console.log('🚀 ~ props:', props)
if (types.value === '1') {
  getOrgTree({
    orgCode: userinfo.unitId,
    unitStatus: '1',
  })
    .then((res: any) => {
      treeData.value = res.data && res.data.length ? filterDepartments(res.data) : []
      // console.log('🚀 ~ gradeList.value:', treeData.value)
      if (userinfo.unitOrgType === '1' && treeData.value[0].children.length === 0) {
        // 什么都不做
        isdisabled.value = true
      } else {
        isdisabled.value = false
      }
    })
    .finally(() => { })
} else {
  treeData.value =
    uni.getStorageSync('unitList').length > 0
      ? uni.getStorageSync('unitList').map((item: any) => {
        return { ...item, text: item.unitName }
      })
      : []
  console.log('🚀 ~ gradeList.value:', treeData.value)
  value.value = treeData.value.length > 0 && treeData.value[0].id
  valuetext.value = treeData.value.length > 0 && treeData.value[0].text
}
function open() {
  if (!isdisabled.value) {
    show.value = true
  }
}

// 过滤部门
function filterDepartments(data: any) {
  // 递归过滤函数
  function filterNodes(nodes: any) {
    return nodes
      .filter((node: any) => {
        // 如果当前节点是部门（orgType === '0'）且没有子节点，则过滤掉
        if (node.attributes.orgType === '0' && !node.children?.length) {
          return false
        }
        return true
      })
      .map((node: any) => {
        // 如果当前节点有子节点，递归处理子节点
        // node.disabled = node.attributes.erecordUnitId === '' || node.attributes.orgType !== '1'
        // if (node.disabled && node.attributes.orgType === '1') {
        //   node.text = node.text + '（暂未绑定）'
        // }
        if (node.children && node.children.length) {
          node.children = filterNodes(node.children)
        }
        return node
      })
  }

  // 对传入的数据进行过滤
  return filterNodes(data)
}

// 前端做模糊搜索
const treeDatash = ref([])
// 公司名称模糊搜索
// const keyWordsChange = debounce((e) => {
//   // if (e.value !== '') {
//   //   if (treeData.value.length > 0) {
//   //     treeData.value = treeData.value.flatMap((node) => searchTree(node, e.value))
//   //   }
//   // } else {
//   //   treeData.value = treeDatash.value
//   //   // tenantryList.value = tenantryListsh.value
//   // }
// }, 500)
//  前端做模糊查询
// function searchTree(node, query) {
//   let matches = []
//   if (node.text.includes(query)) {
//     matches.push(node)
//   }
//   if (node.children && node.children.length > 0) {
//     for (const child of node.children) {
//       matches = matches.concat(searchTree(child, query))
//     }
//   }
//   return matches
// }
// 选中事件
function handleTreeChange(allSelectedKeys, currentItem) {
  $emit('getUnit', currentItem.originItem)
  value.value = currentItem.originItem.id
  valuetext.value = currentItem.originItem.text
  show.value = false
  searchValue.value = ''
}
// $emit('findNodeById', findNodeById(treeData.value, userinfo.unitId))
</script>

<style lang="scss" scoped>
.selcell {
  font-size: 14px;
  border: 1px solid #ebebeb;
  background-color: white;
  border-radius: 20px;
  display: flex;
  padding: 4px 0.55rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.disabled {
  background-color: #cccccc63 !important;
  color: #cccccc63 !important;
}

::v-deep {
  .da-tree-item__checkbox.is--disabled {
    display: none;
  }

  .da-tree-item__label {
    font-size: 17px;
  }

  .da-tree-item.is-show {
    padding: 0.675rem 0.75rem;
  }
}

.pop-title {
  position: sticky;
  width: 100%;
  margin: 20px auto 10px;
  font-size: 16px;
  color: #000000;
  text-align: center;
  font-weight: bold;

  .pop-cancel {
    position: absolute;
    top: 0px;
    right: 20px;
    color: rgb(175, 175, 175);
  }
}
</style>
