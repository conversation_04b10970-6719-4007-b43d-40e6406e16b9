<!--
 * @Author: 悦悦 <EMAIL>
 * @Date: 2024-10-10 20:55:42
 * @LastEditors: Zhangyaling <EMAIL>
 * @LastEditTime: 2025-05-15 15:59:42
 * @FilePath: /隐患排查app/src/components/commonSelect/dorp-menu.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '下拉菜单',
  },
}
</route>
<template>
  <wd-drop-menu>
    <wd-drop-menu-item title="单位名称" ref="dropMenu">
      <wd-search placeholder-left @change="search" placeholder="单位名称" :hide-cancel="true" v-model="likeFieldValue" />
      <view style="width: 90%; margin: auto">
        <wd-radio-group v-if="UnitList.length > 0" v-model="unitvalue" @change="handleChange2">
          <scroll-view style="height: 30vh" scroll-y="true">
            <wd-radio v-for="(itme, index) in UnitList" :key="index" :value="itme.id">
              {{ itme.unitName }}
            </wd-radio>
          </scroll-view>
        </wd-radio-group>
        <view v-else class="flex items-center justify-center mt-[2%]">
          <view class="relative">
            <img src="@/static/images/no-data.png" width="237px" height="165px" />
            <text class="absolute bottom-[20px] left-[0] w-[237px] text-center color-[#969799]">
              暂无数据
            </text>
          </view>
        </view>
      </view>
    </wd-drop-menu-item>
    <wd-drop-menu-item title="等级" v-model="Gradevalue" value-key="id" label-key="gradeName" :options="GradeList"
      @change="handleChange1" />
    <wd-drop-menu-item title="整改状态" v-model="disposeStatevalue" value-key="id" label-key="dispose"
      :options="disposeStateList" @change="handleChange3" v-if="props.isShowdisposeState" />
  </wd-drop-menu>
</template>

<script lang="ts" setup>
import { debounce } from '@/utils'
import { postAllUnitAPI1, postgetUserEventUnitListAPI, posthazardGradeAPI } from './featch'
import { useUserStore } from '@/store'
const userStore = useUserStore()
const userinfo: any = userStore.userInfo ? userStore.userInfo : {}
const props = defineProps<{
  isShowdisposeState?: boolean
  isShowUnitvalue?: boolean
  orgRes?: string
}>()
const likeFieldValue = ref('')
hazardGrade()
// onLoad(() => {
console.log(props.orgRes, '===========props.orgRes')
if (props.orgRes === '1') {
  AllUnit()
} else {
  console.log('相关方/承租方')
  ByUserUnitList()
}

// })
const dropMenu = ref()
const $emit = defineEmits(['getGrade', 'getUnit', 'getDisposeState'])
// 下拉菜单
const GradeList = ref<any[]>([{ gradeName: '全部', id: '0000' }])
const Gradevalue = ref<string>('0000')

function hazardGrade() {
  // 调整获取隐患等级接口参数
  posthazardGradeAPI({
    unitId: userinfo.orgRes === '1' ? userinfo.topUnitId : userinfo.serverUnitId,
    pageSize: -1,
    delFlag: 0,
  })
    .then((res: any) => {
      GradeList.value.push(...res.data)
    })
    .finally(() => { })
}

const UnitList = ref<any[]>([{ unitName: '全部', id: '0000' }])
const unitvalue = ref<string>('0000')
const UnitListall = ref<any[]>([])
// orgCode
function AllUnit() {
  postAllUnitAPI1({ orgCode: userinfo.unitId, pageSize: -1 })
    .then((res: any) => {
      UnitList.value.push(...res.data.rows)
      UnitListall.value = UnitList.value
    })
    .finally(() => { })
}

const search = debounce(() => {
  UnitList.value = UnitListall.value.filter((itme) => {
    return itme.unitName.includes(likeFieldValue.value)
  })
}, 500)
// UnitList.value = []
// if (props.orgRes === '1') {
//   AllUnit()
// } else {
//   ByUserUnitList()
// }
// // /hazardRandomCheck/getUserEventUnitList
function ByUserUnitList() {
  postgetUserEventUnitListAPI({ userId: userinfo.id })
    .then((res: any) => {
      UnitList.value.push(...res.data.rows)
      UnitListall.value = UnitList.value
    })
    .finally(() => { })
}
// /hazardRandomCheck/getUserEventUnitList

const disposeStatevalue = ref<string>('0000')
const disposeStateList = ref<any[]>([
  { dispose: '全部', id: '0000' },
  { dispose: '待整改', id: '0' },
  { dispose: '已整改', id: '1' },
  { dispose: '整改中', id: '2' },
])

function handleChange1({ value, selectedItem }) {
  $emit('getGrade', selectedItem)
}
function handleChange2({ value, selectedItem }) {
  // console.log({ id: value }, '==========selectedItem')
  $emit('getUnit', { id: value })
  dropMenu.value.close()
}
function handleChange3({ value, selectedItem }) {
  $emit('getDisposeState', selectedItem)
}
</script>

<style lang="scss" scoped></style>
