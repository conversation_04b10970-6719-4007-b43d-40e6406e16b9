<template>
  <wd-cell required title="隐患分类" title-width="100px" is-link @click="show = true">
    <view v-if="value" style="color: #000000d9">{{ value }}</view>
    <view v-else style="color: #bfbfbf">请选择隐患分类</view>
  </wd-cell>
  <wd-popup
    v-if="show"
    z-index="999"
    v-model="show"
    position="bottom"
    lock-scroll
    :safe-area-inset-bottom="true"
    @close="handleClose"
    custom-style="height: 75vh;"
  >
    <view class="pop-title">
      隐患分类
      <wd-icon class="pop-cancel" name="close" size="16px" @click="handleClose"></wd-icon>
    </view>
    <scroll-view style="height: 67vh" scroll-y="true">
      <tree
        v-if="treeDatalist.length > 0"
        :selectid="value"
        :treeData="treeDatalist"
        @edit-node="handleEditNode"
      />
      <view v-else style="text-align: center; color: #ccc">暂无数据</view>
    </scroll-view>
  </wd-popup>
</template>

<script lang="ts" setup>
import tree from './tree.vue'
const props = defineProps<{
  treeData?: {
    type: Array
    default: () => []
  }
  mrhazardType?: {
    type: string
    default: ''
  }
  mrhazardTypeId?: {
    type: string
    default: ''
  }
}>()

const Emits = defineEmits(['sendEssen'])
const show = ref(false)
const value = ref('')
const valueid = ref('')

const treeDatalist = ref([])
function handleClose() {
  show.value = false
}
function handleEditNode(node) {
  console.log(node, 'nodenodenode')
  value.value = node.className
  Emits('sendEssen', node)
  show.value = false
}
// console.log(props.mrhazardTypeId, "props.mrhazardTypeId")
// console.log(props.treeData, "props.treeData")
// 默认赋值
valueid.value = props.mrhazardTypeId
value.value = props.mrhazardType
treeDatalist.value = props.treeData
initializeTreeData(treeDatalist.value, true, valueid.value)
// 处理树形数据
function initializeTreeData(nodes, expanded, id) {
  nodes.forEach((node) => {
    node.expanded = expanded
    node.selectid = ''
    node.showimg = false
    node.text = node.className
    if (node.id === id) {
      node.selectid = id
    }
    if (node.children && node.children.length > 0) {
      initializeTreeData(node.children, expanded, id) // 递归处理子节点
    }
  })
}
watch(
  () => props.treeData,
  (val) => {
    // // console.log(props.orgUnitId, '===props.orgUnitId')
    treeDatalist.value = props.treeData
    // console.log(props.treeData, "props.treeData------watch")
    initializeTreeData(treeDatalist.value, true, valueid.value)
  },
)
watch(
  () => props.mrhazardType,
  (val) => {
    value.value = props.mrhazardType
    initializeTreeData(treeDatalist.value, true, valueid.value)
  },
)
watch(
  () => props.mrhazardTypeId,
  (val) => {
    valueid.value = props.mrhazardTypeId
    // console.log(props.mrhazardTypeId, "props.mrhazardTypeId---watch")
    initializeTreeData(treeDatalist.value, true, valueid.value)
  },
)
</script>

<style lang="scss" scoped>
.pop-title {
  position: relative;
  width: 100%;
  margin: 20px auto 10px;
  font-size: 16px;
  color: #000000;
  text-align: center;
  font-weight: bold;

  .pop-cancel {
    position: absolute;
    top: 0px;
    right: 20px;
    color: rgb(175, 175, 175);
  }
}
</style>
