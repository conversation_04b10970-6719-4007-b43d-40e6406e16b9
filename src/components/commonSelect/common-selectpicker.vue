<template>
  <wd-cell :title="title" required />
  <view class="selectprincipal">
    <!-- <view class="required">
      {{ title }}
    </view> -->
    <view class="principalcontent">
      <view v-for="(itme, index) in selectedItem" :key="index" @click="delprincipal(itme.id)" class="principal_itme">
        <view>{{ itme.userName }}</view>
        <view><wd-icon name="error-fill" color="#0256FF"></wd-icon></view>
      </view>
      <wd-button icon="add" plain hairline @click="openPop"></wd-button>
      <wd-popup v-model="show" position="bottom" @close="handleClose">
        <view class="pop-title">
          请选择
          <wd-icon class="pop-cancel" name="close" size="16px" @click="handleClose"></wd-icon>
        </view>
        <wd-search v-model="keyWords" hide-cancel placeholder-left @change="keyWordsChange" />
        <view class="pop-main">
          <wd-checkbox-group v-model="selectList" cell>
            <view v-for="item in columns" :key="item.id">
              <wd-checkbox :modelValue="item.id">
                <text class="wd-select-picker__text-active">
                  {{ item.userName }}
                </text>
              </wd-checkbox>
            </view>
          </wd-checkbox-group>
        </view>
        <view class="pop-button" @click="submitPop">确认</view>
      </wd-popup>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { postOrgUserAPI } from './featch'
import { useUserStore } from '@/store'
const userStore = useUserStore()
const userinfo: any = userStore.userInfo ? userStore.userInfo : {}
const props = defineProps<{
  title?: string
  // 代表它是从隐患上报传过的unitID---隐患整改人
  status?: number
  orgUnitId?: string
  type?: number
}>()
const columns = ref<any[]>([])
const columnsSave = ref<any[]>([])
const orgCode = ref('')
const statu = ref()
onLoad((params) => {
  // // console.log(params, '===============================common-selectpicker')
  // // console.log(props, '===============================props')
  statu.value = props.status
  if (statu.value !== 1) {
    // 其他情况下取本身unitid
    orgCode.value = userinfo.topUnitId
    getUserList()
  }
  if (params.unitId) {
    orgCode.value = params.unitId
  }
})
function getUserList() {
  postOrgUserAPI({ orgCode: orgCode.value, needChildren: true, pageSize: -1 })
    .then((res: any) => {
      columns.value = res.data.rows
      columnsSave.value = res.data.rows
    })
    .finally(() => { })
}

const $emit = defineEmits(['send'])
const selectedItem = ref<any[]>([])
const value = ref<string[]>()
function handleConfirm(selectedItems) {
  selectedItem.value = selectedItems
  //   给model赋值
  $emit('send', selectedItem.value)
}
function delprincipal(selvalue: any) {
  selectedItem.value = selectedItem.value.filter((item) => item.id !== selvalue)
  $emit('send', selectedItem.value)
}
// function delprincipal(selvalue: any) {
//   // console.log(selvalue, selectedItem.value, 111)

//   selectedItem.value = selectedItem.value.filter((item) => item.id !== selvalue)
//   value.value = value.value.filter((item) => item !== selvalue)
//   // console.log(selectedItem.value, 888)

//   $emit('send', selectedItem.value)
// }
const show = ref(false)
const keyWords = ref('')
const selectList = ref([])
const openPop = () => {
  if (props.orgUnitId) {
    orgCode.value = props.orgUnitId
  }
  if (orgCode.value === '') {
    if (props.type === 1) {
      uni.showToast({
        title: '请先选择检查对象',
        icon: 'none',
      })
    } else {
      uni.showToast({
        title: '请先选择隐患单位',
        icon: 'none',
      })
    }
  } else {
    getUserList()
    if (selectedItem.value.length) {
      selectList.value = selectedItem.value.map((item) => item.id)
    } else {
      selectList.value = []
    }
    show.value = true
    keyWords.value = ''
    columns.value = columnsSave.value
  }
}
const handleClose = () => {
  show.value = false
  keyWords.value = ''
}
const keyWordsChange = (e) => {
  // // console.log('e', e)
  columns.value = columnsSave.value.filter((item) => item.userName.includes(e.value))
}

const submitPop = (e) => {
  const arr = []
  selectList.value.forEach((item1) => {
    const obj = columnsSave.value.find((itemK: any) => itemK.id === item1)
    arr.push(obj)
  })
  handleConfirm(arr)
  show.value = false
  keyWords.value = ''
}

watch(
  () => props.orgUnitId,
  (val) => {
    orgCode.value = props.orgUnitId
  },
)
</script>

<style lang="scss">
::v-deep {
  .wd-button.is-medium.is-round {
    min-width: 0 !important;
    margin-top: 0.8125rem;
    margin-left: 0.625rem;
  }
}

.required::before {
  // position: absolute;
  // left: 0;
  top: 2px;
  line-height: 1.1;
  color: #fa4350;
  content: '*';
}

.selectprincipal {
  padding: 0rem 0 1.3125rem 0;
  margin: 0 0 0 13px;
  font-size: 0.875rem;
  // border-top: 0.3px solid #ebebeb;
  border-bottom: 0.3px solid #ebebeb;

  .principalcontent {
    .principal_itme {
      display: flex;
      align-items: center;
      justify-content: space-around;
      float: left;
      width: 5.8125rem;
      height: 2.375rem;
      margin: 0.625rem;
      background-color: #f0f0f0;
      border-radius: 6px;
    }
  }
}

.pop-title {
  position: relative;
  width: 100%;
  margin: 20px auto 10px;
  font-size: 16px;
  color: rgb(75, 75, 75);
  text-align: center;

  .pop-cancel {
    position: absolute;
    top: 0px;
    right: 20px;
    color: rgb(175, 175, 175);
  }
}

.pop-main {
  width: 100%;
  height: 300px;
  margin: 10px auto;
  overflow: auto;
}

.pop-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 95%;
  height: 40px;
  margin: 20px auto;
  color: #fff;
  background-color: #4d80f0;
}
</style>
