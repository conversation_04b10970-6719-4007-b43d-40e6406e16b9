<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '统计部分',
  },
}
</route>
<template>
  <view class="infobox">
    <!--    <view class="titbefor"></view>-->
    <!-- 随机检查详情页显示 flag=01 -->
    <view v-if="props.type !== 1" class="box">
      <div class="box-itme">
        <view class="imgbox">
          <image class="img" src="../../static/pages/nav/rwzyh.png" />
        </view>
        <view style="margin-left: 0.625rem">
          <p style="font-size: 14px">任务总隐患数</p>
          <p style="font-size: 20px">{{ zongdisposedNum }}</p>
        </view>
      </div>
      <div class="box-itme">
        <view class="imgbox">
          <image class="img" src="../../static/pages/nav/sbdyh.png" />
        </view>
        <view style="margin-left: 0.625rem">
          <p style="font-size: 14px">我上报的隐患</p>
          <p style="font-size: 20px">{{ mydisposedNum }}</p>
        </view>
      </div>
    </view>
    <!-- 隐患清单显示 flag = 02 -->
    <view v-else class="box">
      <div class="box-itme" @click="Emits('handleAbarbeitung', 0)">
        <view class="imgbox">
          <image class="img" src="../../static/pages/nav/sbdyh.png" />
        </view>
        <view style="margin-left: 0.625rem">
          <p style="font-size: 14px">待整改</p>
          <!-- disposedNum -->
          <p style="font-size: 20px">{{ model.unDisposedNum || 0 }}</p>
        </view>
      </div>
      <div class="box-itme" @click="Emits('handleAbarbeitung', 1)">
        <view class="imgbox">
          <image class="img" src="../../static/pages/nav/yzg.png" />
        </view>
        <view style="margin-left: 0.625rem">
          <p style="font-size: 14px">已整改</p>
          <p style="font-size: 20px">{{ model.disposedNum || 0 }}</p>
        </view>
      </div>
    </view>
    <!--    <view class="box" v-if="levelStatisticsList.length > 0">
      <div class="box-itme2">
        <view class="box-itme2-value" v-for="(item, index) in levelStatisticsList" :key="item.hazardLevel + index">
          <p class="box-itme2-value-title">{{ item.hazardLevelName }}</p>
          <p class="box-itme2-value-context" :style="item.hazardLevel === 1
            ? 'color: #e23b50'
            : item.hazardLevel === 2
              ? 'color: #f59a23'
              : item.hazardLevel === 3
                ? 'color: #bfbf00'
                : 'color: #76b90e'
            ">
            {{ item.total }}
          </p>
        </view>
      </div>
    </view>
    <view style="text-align: center;" v-else>加载中....</view>-->
  </view>
</template>

<script lang="ts" setup>
import { defineExpose } from 'vue'
import {
  posthazardMegerlevelStatisticsAPI,
  posthazardMegerstatisticsAPI,
  posthazardPlanTaskEventlevelStatisticsAPI,
  posthazardPlanTaskEventStatisticsAPI,
  posthazardRecordlevelStatisticsAPI,
  posthazardRecordStatisticsAPI,
} from './featch'
const Emits = defineEmits(['handleAbarbeitung'])
const props = defineProps<{
  type: number // 0 随机检查 1 隐患事件
  isPlanTask?: boolean // 是否是未完成的任务
  createBy?: string
  taskId?: string
  unitId?: string
  topUnitId?: string
  reformUserId?: string
  roleCodes?: []
  hazardLevel?: string
  disposeState?: string
  checkUnitId?: string
}>()
/* // 隐患事件统计 */
const model = ref<any>({})
const model1 = ref<any>({})

const zongdisposedNum = ref<any>(0)
const mydisposedNum = ref<any>(0)

/* 隐患等级统计 */
const levelStatisticsList = ref<any>()
const paramas = ref<any>({
  businessId: '',
  flag: '',
  unitId: '',
  createBy: '',
  disposeState: '',
  checkUnitId: '',
  reformUserId: props.reformUserId,
  roleCodes: props.roleCodes,
  hazardLevel: props.hazardLevel,
  topUnitId: '',
  includeLower: 1,
})
const levelStatisticsList1 = ref<any>()
async function hazardMegerlevelStatistics() {
  // paramas.value.unitId = props.unitId
  // paramas.value.createBy = ''
  // paramas.value.topUnitId = props.topUnitId
  // paramas.value.roleCodes = props.roleCodes
  // paramas.value.reformUserId = props.reformUserId
  // console.log(props.topUnitId, "=====props.topUnitId")
  paramas.value.topUnitId = props.topUnitId
  paramas.value.createBy = ''
  // debugger
  // paramas.value.topUnitId = props.topUnitId
  levelStatisticsList.value = []
  uni.showLoading({
    mask: true,
  })
  try {
    console.log(paramas.value, '====================统计接口===paramas33333')
    const res = await posthazardMegerlevelStatisticsAPI(paramas.value)
    uni.hideLoading()
    levelStatisticsList1.value = res.data?.slice(0, 5) || []
  } catch (error) {
    uni.hideLoading()
  }
  // await posthazardMegerlevelStatisticsAPI(paramas.value)
  //   .then((res: any) => {
  //     levelStatisticsList1.value = res.data ? res.data.slice(0, 5) : []
  //     levelStatisticsList.value = []
  //   })
  //   .finally(() => { })
}

// posthazardMegerstatisticsAPI
async function hazardMegerstatistics() {
  // paramas.value.unitId = ''
  paramas.value.topUnitId = ''
  paramas.value.createBy = ''
  uni.showLoading({
    mask: true,
  })
  console.log(paramas.value, '====================统计接口===paramas1111')
  await posthazardMegerstatisticsAPI(paramas.value)
    .then((res: any) => {
      // // console.log(res)
      uni.hideLoading()
      model.value = res.data
      zongdisposedNum.value = res.data.total
    })
    .finally(() => {
      uni.hideLoading()
    })
  if (props.createBy) {
    paramas.value.createBy = props.createBy
    uni.showLoading({
      mask: true,
    })
    console.log(paramas.value, '====================统计接口===paramas22222')
    await posthazardMegerstatisticsAPI(paramas.value)
      .then((res: any) => {
        // // console.log(res)
        uni.hideLoading()
        model.value = res.data
        mydisposedNum.value = res.data.total
      })
      .finally(() => {
        uni.hideLoading()
      })
  }
}
watch(
  () => levelStatisticsList1.value, // 改为监听数据源
  (newVal) => {
    levelStatisticsList.value = newVal ? [...newVal] : []
    console.log('更新后的统计列表:', levelStatisticsList.value)
  },
)
watch(
  () => zongdisposedNum.value, // 改为监听数据源
  (newVal) => {
    console.log('更新后的统计列表:========', newVal)
    zongdisposedNum.value = newVal
  },
)
watch(
  () => mydisposedNum.value, // 改为监听数据源
  (newVal) => {
    console.log('更新后的统计列表:========', newVal)
    mydisposedNum.value = newVal
  },
)
const update = async () => {
  paramas.value.businessId = props.taskId
  paramas.value.unitId = props.unitId
  paramas.value.hazardLevel = props.hazardLevel
  paramas.value.disposeState = props.disposeState
  paramas.value.reformUserId = props.reformUserId
  paramas.value.checkUnitId = props.checkUnitId
  // // console.log(props.reformUserId, '============props.reformUserId')
  if (props.reformUserId !== '') {
    paramas.value.roleCodes = props.roleCodes
  } else {
    paramas.value.roleCodes = []
  }
  // paramas.value.createBy = props.createBy
  if (props.taskId !== '') {
    // // console.log(props.type, '=======props.type')
    if (props.type !== 0) {
      paramas.value.flag = '01' // 检查计划
    } else {
      paramas.value.flag = '02' // 计划任务
    }
  } else {
    paramas.value.flag = ''
    // paramas.value.topUnitId = props.topUnitId
  }
  await hazardMegerstatistics()
  await hazardMegerlevelStatistics()

  // if (props.isPlanTask) {
  //   unhazardRecordStatistics(props.taskId, props.unitId, props.createBy)
  //   GetunlevelStatistics(props.taskId, props.unitId)
  // } else {
  //   hazardRecordStatistics(props.taskId, props.unitId, props.createBy)
  //   GetlevelStatistics(props.taskId, props.unitId)
  // }
}

defineExpose({ update })
</script>

<style lang="scss" scoped>
.infobox {
  margin-bottom: 0.625rem;
}

.box {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  justify-content: space-between;

  .box-itme {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 10.25rem;
    height: 4.375rem;
    margin: 0.3125rem;
    background-color: rgba(188, 201, 218, 0.2);
    border-radius: 0.75rem;

    .imgbox {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 2.25rem;
      height: 2.25rem;

      .img {
        width: 100%;
        height: 100%;
      }
    }
  }

  .box-itme2 {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    width: 90%;
    margin: 10px auto;

    .box-itme2-value {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 50%;
      padding: 4px 0px;

      .box-itme2-value-title {
        width: 60%;
        font-size: 14px;
      }

      .box-itme2-value-context {
        width: 30%;
        font-size: 20px;
        color: #e23b50;
      }
    }
  }
}
</style>
