<template>
  <wd-cell :title="title" required />
  <view class="selectprincipal">
    <!-- <view class="required">
      {{ title }}
    </view> -->
    <view class="principalcontent">
      <view
        v-for="(itme, index) in selectedItem"
        :key="index"
        @click="delprincipal(itme.id)"
        class="principal_itme"
      >
        <view>
          {{ itme.userName || itme.name || '--' }}
          {{ itme.usertype === 1 ? '' : itme.usertype === 3 ? `(${getzhname()})` : '(承租方)' }}
        </view>
        <view><wd-icon name="error-fill" color="#0256FF"></wd-icon></view>
      </view>
    </view>
    <!-- hairline -->
    <wd-button icon="add" plain @click="openPop"></wd-button>
  </view>
  <wd-popup
    v-if="show"
    z-index="999"
    v-model="show"
    position="bottom"
    lock-scroll
    :safe-area-inset-bottom="true"
    @close="handleClose"
  >
    <view class="pop-title">
      整改人员
      <wd-icon class="pop-cancel" name="close" size="16px" @click="handleClose"></wd-icon>
    </view>
    <!-- <commonSelectUnit :selectUint="userinfo.topUnitId" :selectvalue="orgCode" @getGrade="changeUnit"></commonSelectUnit> -->
    <!-- 106功能 -->
    <commonSelectUnitNew
      :selectUint="props.orgUnitId"
      :outUnitid="props.outUnitid"
      :orgname="props.orgname"
      @getUnit="changeUnit"
    ></commonSelectUnitNew>
    <view style="padding: 15px 15px 5px 15px; font-weight: bold; font-size: 15px">选择人员</view>
    <wd-search
      v-model="commonModel.keyWord"
      placeholder="人员姓名模糊搜索"
      hide-cancel
      placeholder-left
      @change="keyWordsChange"
    />
    <!-- 106功能调研 -->
    <!-- <wd-button icon="add" plain hairline @click="treeshow = true">2222</wd-button> -->
    <!-- <wd-popup v-if="treeshow" v-model="treeshow" position="bottom">
          <view class="pop-title">
            请选择
            <wd-icon class="pop-cancel" name="close" size="16px" @click="treeshow = false"></wd-icon>
          </view>
          <tree :treeData="treeData" style="height: 600px" @edit-node="handleEditNode" />
        </wd-popup> -->
    <scroll-view
      style="height: 40vh"
      scroll-y="true"
      scroll-into-view="bottom"
      @scrolltolower="handleScrollToLower"
    >
      <view class="pop-main">
        <wd-checkbox-group v-if="columns.length > 0" v-model="selectList" cell>
          <view v-for="item in columns" :key="item.id">
            <wd-checkbox
              :modelValue="item.id"
              style="border-bottom: 1px solid #ebebeb; width: 90%; margin: auto"
            >
              <view style="display: flex; justify-content: space-between; width: 270px">
                <view
                  style="
                    width: 60%;
                    overflow: hidden;
                    overflow-wrap: normal;
                    text-overflow: ellipsis;
                  "
                >
                  {{ item.userName || item.name || '--' }}
                </view>
                <view style="text-align: left; width: 40%">
                  {{ item.userTelphone || item.phone || '--' }}
                </view>
              </view>
            </wd-checkbox>
          </view>
        </wd-checkbox-group>
        <view v-else style="text-align: center; color: #ccc">暂无数据</view>
      </view>
    </scroll-view>
    <!-- 106功能调研 -->
    <view style="padding: 4px 16px; border-top: 1px solid #ebebeb">
      <view>选择人员：</view>
      <scroll-view style="min-height: 10vh; max-height: 10vh" scroll-y="true">
        <view
          v-for="(itme, index) in selectedItem"
          :key="index"
          @click="delprincipal(itme.id)"
          class="principal_itme1"
        >
          <view style="padding-right: 6px">
            {{ itme.userName || itme.name || '--' }}
            {{ itme.usertype === 1 ? '' : itme.usertype === 3 ? `(${getzhname()})` : '(承租方)' }}
          </view>
          <view><wd-icon name="error-fill" color="#0256FF"></wd-icon></view>
        </view>
      </scroll-view>
    </view>
    <view class="popbom">
      <view
        class="pop-button1"
        style="border-top: 1px solid #ccc; color: #333"
        @click="handleClose"
      >
        取消
      </view>
      <view
        class="pop-button1"
        style="background-color: #0256ff; border-top: 1px solid #0256ff; color: white"
        @click="submitPop"
      >
        确认
      </view>
      <!-- <view class="pop-button" @click="submitPop">取消</view>
      <view class="pop-button" @click="submitPop">确认</view> -->
    </view>
  </wd-popup>
</template>

<script lang="ts" setup>
import {
  postOrgUserAPI,
  postperManagerAPI,
  postTenantryUsersByTenantryIdAPI,
  postTopLevelOrgCodeAPI,
} from './featch'
import { useUserStore } from '@/store'
import commonSelectUnit from '@/components/commonSelect/common-selectpicker-unit.vue'
import commonSelectUnitNew from './commonSelectUnit-new.vue'
import tree from './tree.vue'
const userStore = useUserStore()
const userinfo: any = userStore.userInfo ? userStore.userInfo : {}
const props = defineProps<{
  title?: string
  // 代表它是从隐患上报传过的unitID---隐患整改人
  status?: number
  orgUnitId?: string
  type?: number
  outUnitid?: string
  orgname?: string
}>()
function getzhname() {
  if (userinfo.zhId === 'ycsyqt') {
    return '承包商'
  } else {
    return '相关方'
  }
}
// // console.log(props.orgUnitId, '=====11111选择的隐患单位')

const columns = ref<any[]>([])

const columnsAll = ref<any[]>([])
const orgCode = ref('')
const statu = ref()
const total = ref(0)

const tabtext = ref(null)

const commonModel = ref({
  pageNo: 1,
  pageSize: 10,
  keyWord: '',
})
// 组织机构
const parameDataModel = ref({
  orgCode: '',
  needChildren: true,
  userName: commonModel.value.keyWord,
})
// 承租方
const TenantryModel = ref({
  likeParam: commonModel.value.keyWord,
  tenantryId: '',
})
// 相关方
const perManagerModel = ref({
  // keyWord: commonModel.value.keyWord,
  // type: '0',
  unitId: '',
  name: commonModel.value.keyWord,
})
const selectedItem = ref<any[]>([])
const sysCode = ref('')
onLoad((params) => {
  if (params.sysCode === 'safe-operation_app') {
    sysCode.value = params.sysCode
    selectedItem.value = params.personList
      ? JSON.parse(params.personList).map((item) => {
          return {
            reformType: 0,
            userMasterUnitId: item.unitId,
            userMasterUnitName: item.unitName,
            userUnitId: item.unitId,
            userUnitName: item.unitName,
            userName: item.name,
            reformUserId: item.id,
            reformUserName: item.name,
            usertype: 1,
            id: item.id,
            name: item.name,
          }
        })
      : []
    // console.log(selectedItem.value, 'selectedItem.value')
  }
  statu.value = props.status
  if (statu.value !== 1) {
    // 其他情况下取本身unitid
    orgCode.value = userinfo.topUnitId
    getUserList(orgCode.value)
  }
  if (params.unitId) {
    orgCode.value = params.unitId
  }
})
// 获取相关方人员信息
function getTenantryUsersByTenantryId(tenantryId) {
  TenantryModel.value.tenantryId = tenantryId
  postTenantryUsersByTenantryIdAPI({ ...TenantryModel.value, ...commonModel.value })
    .then((res: any) => {
      if (res.data.rows) {
        total.value = res.data.total
        columns.value = [...columns.value, ...res.data.rows]
        // columnsSave.value = [...columnsSave.value, ...res.data.rows]
        columns.value = columns.value.map((item) => {
          return {
            ...item,
            usertype: 2,
          }
        })
        columns.value.forEach((item) => {
          if (!columnsAll.value.some((item2) => item2.id === item.id)) {
            columnsAll.value.push(item)
          }
        })
      } else {
        columns.value = []
      }
    })
    .finally(() => {})
}
// 获取组织架构人员信息
function getUserList(orgCode) {
  parameDataModel.value.orgCode = orgCode
  postOrgUserAPI({ ...parameDataModel.value, ...commonModel.value })
    .then((res: any) => {
      // // console.log(res)
      if (res.data.rows) {
        total.value = res.data.total
        columns.value = [...columns.value, ...res.data.rows]
        columns.value = columns.value.map((item) => {
          return {
            ...item,
            usertype: 1,
          }
        })
        // columnsSave.value = [...columnsSave.value, ...res.data.rows]
        columns.value.forEach((item) => {
          if (!columnsAll.value.some((item2) => item2.id === item.id)) {
            columnsAll.value.push(item)
          }
        })
      } else {
        columns.value = []
      }
    })
    .finally(() => {})
}
// 获取承租方、承包商方人员
function getperManagerList(orgCode) {
  perManagerModel.value.unitId = orgCode
  postperManagerAPI({ ...perManagerModel.value, ...commonModel.value })
    .then((res: any) => {
      console.log(res)
      if (res.data.rows) {
        total.value = res.data.total
        columns.value = [...columns.value, ...res.data.rows]
        columns.value = columns.value.map((item) => {
          return {
            ...item,
            usertype: 3,
            unitId: item.orgCode,
            unitName: item.depart,
            userMasterUnitId: item.createOrgCode,
            userMasterUnitName: item.createOrgName,
          }
        })
        columns.value.forEach((item) => {
          if (!columnsAll.value.some((item2) => item2.id === item.id)) {
            columnsAll.value.push(item)
          }
        })
      } else {
        columns.value = []
      }
    })
    .finally(() => {})
}
// 分页
function handleScrollToLower() {
  if (total.value === columns.value.length) {
    return
  }
  console.log(tabtext.value, '===tabtext.value')
  setTimeout(() => {
    commonModel.value.pageNo++
    if (tabtext.value === 3) {
      getperManagerList(orgCode.value)
    } else if (tabtext.value === 2) {
      getTenantryUsersByTenantryId(orgCode.value)
    } else {
      getUserList(orgCode.value)
    }
  }, 500)
}

const $emit = defineEmits(['send'])

function handleConfirm(selectedItems) {
  console.log(selectedItems, 'selectedItems')
  selectedItem.value = selectedItems
  $emit('send', selectedItem.value)
}

const show = ref(false)
const selectList = ref([])
// 删除
function delprincipal(selvalue: any) {
  selectedItem.value = selectedItem.value.filter((item) => item.id !== selvalue)
  selectList.value = selectList.value.filter((item) => item !== selvalue)
  $emit('send', selectedItem.value)
}

const openPop = () => {
  commonModel.value.pageNo = 1
  if (props.orgUnitId) {
    orgCode.value = props.orgUnitId
  }
  if (orgCode.value === '') {
    if (props.type === 1) {
      uni.showToast({
        title: '请先选择检查对象',
        icon: 'none',
      })
    } else {
      uni.showToast({
        title: '请先选择隐患单位',
        icon: 'none',
      })
    }
  } else {
    if (tabtext.value === 3) {
      // console.log(111)
    } else {
      getUserList(orgCode.value)
    }

    if (selectedItem.value.length) {
      selectList.value = selectedItem.value.map((item) => item.id)
    } else {
      selectList.value = []
    }
    show.value = true
    // keyWords.value = ''
    // columns.value = columnsSave.value
  }
}

const changeUnit = (e) => {
  console.log(e)
  commonModel.value.pageNo = 1
  commonModel.value.keyWord = ''
  parameDataModel.value.userName = ''
  perManagerModel.value.name = ''
  TenantryModel.value.likeParam = ''
  columns.value = []
  orgCode.value = e.id
  tabtext.value = Number(e.tabi)
  if (e.tabi === '3') {
    getperManagerList(e.id)
  } else if (e.tabi === '2') {
    getTenantryUsersByTenantryId(e.id)
  } else {
    getUserList(e.id)
  }
}
const handleClose = () => {
  show.value = false
  commonModel.value.keyWord = ''
  parameDataModel.value.userName = ''
  perManagerModel.value.name = ''
  TenantryModel.value.likeParam = ''
  // TenantryModel.value.userName = ''
  columns.value = []
  selectedItem.value = []
  $emit('send', selectedItem.value)
  // keyWords.value = ''
}
// const keyWordsChange = (e) => {
//   // console.log('e', e)
//   columns.value = columnsSave.value.filter((item) => item.userName.includes(e.value))
// }
let timeoutId: ReturnType<typeof setTimeout> | null = null
function keyWordsChange(event) {
  // console.log(event)
  if (timeoutId !== null) {
    clearTimeout(timeoutId)
    timeoutId = null
  }
  timeoutId = setTimeout(() => {
    commonModel.value.pageNo = 1
    commonModel.value.keyWord = event.value
    columns.value = []
    // console.log(tabtext.value, "=====tabtext.value")
    if (tabtext.value === 3) {
      // // console.log('调用承租方人员接口')
      // TenantryModel.value.userName = event.value
      perManagerModel.value.name = event.value
      getperManagerList(orgCode.value)
    } else if (tabtext.value === 2) {
      TenantryModel.value.likeParam = event.value
      getTenantryUsersByTenantryId(orgCode.value)
    } else {
      parameDataModel.value.userName = event.value
      getUserList(orgCode.value)
    }
  }, 500)
}

watch(
  () => selectList.value,
  (val) => {
    // // console.log(selectList.value, '======selectList.value')
    // // console.log(tabtext.value, '======tabtext.value')
    const arr = []
    selectList.value.forEach((item1) => {
      const obj = columnsAll.value.find((itemK: any) => itemK.id === item1)
      arr.push(obj)
    })
    handleConfirm(sysCode.value === 'safe-operation_app' ? selectedItem.value : arr)
  },
)

const submitPop = (e) => {
  const arr = []
  // // console.log('selectList.value', selectList.value)
  selectList.value.forEach((item1) => {
    const obj = columnsAll.value.find((itemK: any) => itemK.id === item1)
    arr.push(obj)
  })

  handleConfirm(arr)

  show.value = false
  // TenantryModel.value.userName = ''
  parameDataModel.value.userName = ''
  perManagerModel.value.name = ''
  commonModel.value.keyWord = ''
  tabtext.value = 1
  columns.value = []
  // keyWords.value = ''
}

watch(
  () => props.orgUnitId,
  (val) => {
    // // console.log(props.orgUnitId, '===props.orgUnitId')
    orgCode.value = props.orgUnitId
  },
)
</script>

<style lang="scss">
::v-deep {
  .wd-checkbox.is-checked .wd-checkbox__shap {
    color: #0256ff;
    background: #0256ff;
    border-color: #0256ff;
  }

  .wd-button.is-medium.is-round {
    min-width: 0 !important;
    margin-top: 0.8125rem;
    margin-left: 0.625rem;
  }

  .wd-checkbox.is-cell-box {
    padding: 14px 0px;
  }
}

.required::before {
  // position: absolute;
  // left: 0;
  top: 2px;
  line-height: 1.1;
  color: #fa4350;
  content: '*';
}

.selectprincipal {
  // padding: 1.3125rem 0 1.3125rem 0;
  margin: 0rem 0 1.3125rem 13px;
  font-size: 0.875rem;
  // border-top: 0.3px solid #ebebeb;
  // border-bottom: 0.3px solid #ebebeb;
  // border: 1px solid red;
  display: flex;
  flex-direction: column;

  .principalcontent {
    z-index: 5;

    .principal_itme {
      display: flex;
      align-items: center;
      justify-content: space-around;
      float: left;
      margin: 6px;
      background-color: #f0f0f0;
      border-radius: 6px;
      padding: 8px 16px;
    }
  }

  .wd-popup {
    border-top-left-radius: 10px !important;
    border-top-right-radius: 10px !important;
  }
}

.wd-popup {
  border-top-left-radius: 10px !important;
  border-top-right-radius: 10px !important;
}

.principal_itme1 {
  display: flex;
  align-items: center;
  justify-content: space-around;
  float: left;
  margin: 6px;
  background-color: #f0f0f0;
  border-radius: 6px;
  padding: 8px 16px;
}

.pop-title {
  position: relative;
  width: 100%;
  margin: 20px auto 10px;
  font-size: 16px;
  color: #000000;
  text-align: center;
  font-weight: bold;

  .pop-cancel {
    position: absolute;
    top: 0px;
    right: 20px;
    color: rgb(175, 175, 175);
  }
}

.pop-main {
  width: 100%;
  height: 300px;
  margin: 10px auto;
  // overflow: auto;
}

.pop-button {
  display: flex;
  align-items: center;
  justify-content: center;
  // width: 95%;
  // height: 40px;
  margin: 10px auto;
  margin-bottom: 30px;
  color: #fff;
  background-color: #4d80f0;
  z-index: 99;
}

.popbom {
  display: flex;
  justify-content: space-around;
  align-items: center;
  z-index: 99;
  margin: 10px auto;
  margin-bottom: 0px;

  .pop-button1 {
    // border: 1px solid;
    width: 50%;
    text-align: center;
    padding: 8px;
  }
}
</style>
