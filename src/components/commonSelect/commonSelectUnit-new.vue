<template>
  <view>
    <view class="sel_input" @click="clickunit">
      <view class="mk">{{ tabtext || '组织架构' }}</view>
      <view class="uni">{{ unitName }}</view>
      <view class="icon"><wd-icon name="arrow-down" size="20px" color="#7f7f7f"></wd-icon></view>
    </view>
    <wd-popup v-show="show" v-model="show" position="bottom" custom-style="height: 80vh;">
      <view class="pop-title">
        整改人单位名称
        <wd-icon class="pop-cancel" name="close" size="16px" @click="show = false"></wd-icon>
      </view>
      <wd-tabs v-model="tabindex" @click="tabhandleClick">
        <wd-tab v-for="(item, index) in tab" :key="index" :title="item.name" :name="item.falg">
          <view class="content">
            <wd-search
              v-model="unitname"
              placeholder="公司名称模糊搜索"
              hide-cancel
              placeholder-left
              @change="keyWordsChange"
            />
            <scroll-view style="height: 60vh" scroll-y="true">
              <DaTree
                v-if="treeData.length > 0"
                :data="treeData"
                labelField="text"
                valueField="id"
                defaultExpandAll
                :defaultCheckedKeys="value"
                @change="handleTreeChange"
                @expand="handleExpandChange"
              ></DaTree>
              <view v-else style="text-align: center; color: #ccc">暂无数据</view>
            </scroll-view>
          </view>
        </wd-tab>
      </wd-tabs>
    </wd-popup>
  </view>
</template>
<script lang="ts" setup>
import { debounce } from '@/utils'
import {
  postAllUnitAPI1,
  postgetOrgTreeAPI,
  postqueryOrgTreeByTanzerAPI,
  postquerySystemListAPI,
  postTenantryListByUnitIdAPI,
  postTopLevelOrgCodeAPI,
} from './featch'
import DaTree from '../da-tree/index.vue'
import { useUserStore } from '@/store'
import { ref } from 'vue'

const userStore = useUserStore()
const userinfo: any = userStore.userInfo ? userStore.userInfo : {}
const props = defineProps<{
  // 组织架构默认查询topunitid
  selectUint?: string
  // 承租方，相关方-选择的检查对象单位id
  outUnitid?: string
  // 选择的单位--用作回显
  inputuniid?: string
  // 回显单位名称
  orgname?: string
}>()
const $emit = defineEmits(['getUnit'])

// 选择的单位名称
const unitName = ref(props.orgname)
// 选择的单位类型
const tabtext = ref('')
const show = ref(false)
const tab = ref([])
const unitname = ref('')
// 选中的单位
const value = ref<string>(props.selectUint || userinfo.unitId)
// 组织架构
const treeData = ref()
// 保存所有的组织数据-前端做模糊查询用
const treeDatash = ref([])
const tabindex = ref(0)
watch(
  () => show.value,
  (newValue, oldValue) => {
    if (show.value) {
      // 每次打开 默认展示第一个tab
      unitnameflag.value = '1'
      tabindex.value = 0
      showTabList()
      getOrgunit('1')
    }
  },
)

function getzhname() {
  if (userinfo.zhId === 'ycsyqt') {
    return '承包商'
  } else {
    return '相关方'
  }
}
// （1：组织内人员；2：承租方人; 3 - 相关方)
function showTabList() {
  tab.value = [{ name: '组织架构', falg: '1' }]
  postquerySystemListAPI({ zhId: userinfo.zhId || '' }).then((res: any) => {
    if (res.code === 'success') {
      res.data &&
        res.data.forEach((item) => {
          if (item.sysCode === 'inter_web') {
            tab.value.push({ name: getzhname(), falg: '3' })
          } else if (item.sysCode === 'tenant_web') {
            tab.value.push({ name: '承租方', falg: '2' })
          }
        })
    }
  })
  console.log(tab.value, 'tab.value')
}
const unitnameflag = ref('1')
function tabhandleClick(e) {
  unitname.value = ''
  tabindex.value = e.index
  unitnameflag.value = e.name
  getOrgunit(e.name)
}

function handleExpandChange(expand, currentItem) {
  console.log('handleTreeChange ==>', expand, currentItem)
}

function clickunit() {
  show.value = true
}

//  前端做模糊查询
function searchTree(node, query) {
  let matches = []
  if (node.text.includes(query)) {
    matches.push(node)
  }
  if (node.children && node.children.length > 0) {
    for (const child of node.children) {
      matches = matches.concat(searchTree(child, query))
    }
  }
  return matches
}

// 公司名称模糊搜索
const keyWordsChange = debounce((e) => {
  if (unitnameflag.value === '1') {
    if (e.value !== '') {
      if (treeData.value.length > 0) {
        treeData.value = treeDatash.value.flatMap((node) => searchTree(node, e.value))
      }
    } else {
      treeData.value = treeDatash.value
    }
  } else {
    getOrgunit(unitnameflag.value)
  }
}, 500)

// 获取组织架构单位
function getOrgunit(falg) {
  treeData.value = []
  uni.showLoading({
    title: '加载中',
    mask: true,
  })
  if (falg === '1') {
    postTopLevelOrgCodeAPI({ orgCode: props.selectUint })
      .then((_res: any) => {
        postgetOrgTreeAPI({ orgCode: _res.data.orgCode, orgName: unitname.value || '' }).then(
          (res: any) => {
            treeData.value = res.data
            treeDatash.value = res.data
            uni.hideLoading()
          },
        )
      })
      .catch(() => {
        uni.hideLoading()
      })
  } else if (falg === '2') {
    postTenantryListByUnitIdAPI({ unitId: props.outUnitid, likeParam: unitname.value || '' })
      .then((res: any) => {
        uni.hideLoading()
        treeData.value = res.data.map((item) => {
          return {
            ...item,
            text: item.tenantryName,
            id: item.tenantryId,
          }
        })
        treeDatash.value = treeData.value
      })
      .catch(() => {
        uni.hideLoading()
      })
  } else if (falg === '3') {
    postqueryOrgTreeByTanzerAPI({
      // orgCode非承包商和相关方人员登录，不传值
      orgCode: userinfo.orgRes !== '1' ? props.selectUint : '',
      needChildUnit: 1,
      needself: 1,
      type: 2,
      keyWord: unitname.value || '',
    })
      .then((res: any) => {
        treeData.value = res.data
        treeDatash.value = res.data
        uni.hideLoading()
      })
      .catch(() => {
        uni.hideLoading()
      })
  }
}
function handleTreeChange(allSelectedKeys, currentItem) {
  console.log('handleTreeChange ==>', currentItem)
  console.log(tabindex.value, 'allSelectedKeys')
  const retinfo = {
    id: currentItem.key,
    unittext: currentItem.label,
    tabtext:
      unitnameflag.value === '1' ? '组织架构' : unitnameflag.value === '3' ? getzhname() : '承租方',
    tabi: tabindex.value,
  }
  value.value = currentItem.key
  unitName.value = retinfo.unittext
  tabtext.value = retinfo.tabtext
  $emit('getUnit', retinfo)
  show.value = false
}
</script>
<style lang="scss" scoped>
::v-deep {
  .is-active {
    color: #1057f0;
  }

  .wd-cell__left {
    align-items: center;
  }

  .wd-cell__title {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .da-tree-item__label {
    font-size: 16px;
  }

  .da-tree-item.is-show {
    padding: 0.575rem 0.75rem;
  }
}

.pop-title {
  position: relative;
  width: 100%;
  margin: 20px auto 10px;
  font-size: 16px;
  color: #000000;
  text-align: center;
  font-weight: bold;

  .pop-cancel {
    position: absolute;
    top: 0px;
    right: 20px;
    color: rgb(175, 175, 175);
  }
}

.sel_input {
  display: flex;
  justify-content: flex-start;
  border: 1px solid #ebebeb;
  width: 95%;
  margin: auto;
  border-radius: 30px;
  align-items: center;

  .mk {
    background-color: #e6eeff;
    color: #0857f3;
    min-width: 23%;
    border-top-left-radius: 30px;
    border-bottom-left-radius: 30px;
    padding: 4px 0px;
    text-align: center;
  }

  .uni {
    width: 67%;
    min-width: 60%;
    padding: 0px 8px;
    color: #464646;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    // border: 1px solid;
  }
}
</style>
