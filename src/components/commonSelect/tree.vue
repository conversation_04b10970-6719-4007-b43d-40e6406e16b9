<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '树形',
  },
}
</route>
<template>
    <view class="tree">
        <view v-for="(node, index) in treeData" :key="index">
            <view>
                <wd-row style="border-radius: 10px" :style="node.selectid === node.id ? 'background-color: #f7f7f7;' : 'background-color:#ffffff;'
                    ">
                    <wd-col :span="20">
                        <view style="padding: 3px; display: flex; align-items: center; justify-content: flex-start"
                            @click="toggleNode($event, node)">
                            <view v-if="node.children && node.children.length > 0">
                                <wd-icon v-if="node.expanded" name="minus-rectangle" size="22px"
                                    color="#3f7df3"></wd-icon>
                                <wd-icon v-else name="add-rectangle" size="22px" color="#3f7df3"></wd-icon>
                            </view>
                            <!-- 占位符 -->
                            <view v-else style="font-size: 22px; color: white">1</view>
                            <image v-if="node.showimg" :src="cicon" style="width: 16px; height: 16px" />
                            <span style="
                  overflow: hidden;
                  text-overflow: ellipsis;
                  white-space: nowrap;
                  font-size: 16px;
                ">
                                {{ node.text }}
                            </span>
                        </view>
                    </wd-col>
                    <wd-col :span="4">
                        <view style="padding: 3px 6px; text-align: end"
                            :style="node.children && node.children.length > 0 ? '' : 'padding-bottom: 3px;'">
                            <wd-icon v-if="node.selectid === node.id" name="check-circle-filled" size="22px"
                                @click.stop="selNode($event, node)" color="#0256ff"></wd-icon>
                            <wd-icon v-else name="circle1" size="22px" @click.stop="selNode($event, node)"
                                color="#c3c2c2"></wd-icon>
                        </view>
                    </wd-col>
                </wd-row>
                <view v-if="node.expanded" class="children">
                    <Tree :treeData="node.children" @edit-node="(childNode) => $emit('edit-node', childNode)"
                        @delete-node="(childNode) => $emit('delete-node', childNode)" />
                </view>
            </view>
        </view>
    </view>
</template>

<script lang="ts" setup>
import { defineExpose } from 'vue'
import cicon from '@/static/pages/nav/cicon.png'
// import business from '@/static/pages/nav/business.png'
// import section from '@/static/pages/nav/section.png'
// import supervise from '@/static/pages/nav/supervise.png'
// const imgMap = { business, section, supervise }

defineOptions({
    name: 'Tree',
})

const props = defineProps({
    treeData: {
        type: Array,
        default: () => [],
    },
    expandAll: {
        type: Boolean,
        default: true,
    },
    selectid: {
        type: String,
        default: '',
    },
})
const Emits = defineEmits(['edit-node'])

// function initializeTreeData(nodes, expanded, id) {
//     nodes.forEach((node) => {
//         node.expanded = expanded
//         node.selectid = ''
//         if (node.id === id) {
//             node.selectid = id
//         }

//         if (node.children && node.children.length > 0) {
//             initializeTreeData(node.children, expanded, id) // 递归处理子节点
//         }
//     })
// }
function toggleNode(event, node) {
    event.stopPropagation() // 阻止事件冒泡
    node.expanded = !node.expanded // 切换节点展开状态
}
function selNode(event, node) {
    // selectidval.value = node.id
    event.stopPropagation()
    Emits('edit-node', node) // 触发父组件的 edit-node 事件，并传递当前节点
}


// // console.log(props.selectid, '=======unitid1111')
// // console.log(props.expandAll, '=======props.expandAll111')
// // console.log(props.selectid, '=======unitid')
// initializeTreeData(props.treeData, props.expandAll, '')

// const update = () => {
//     console.log(props.selectid, '=======unitid')
//     console.log(props.expandAll, '=======props.expandAll')
//     initializeTreeData(props.treeData, props.expandAll, props.selectid)
// }
// defineExpose({ update })
// export default {
//     name: 'Tree',
//     props: {
//         treeData: {
//             type: Array,
//             default: () => [],
//         },
//         expandAll: {
//             type: Boolean,
//             default: true,
//         },
//         selectid: {
//             type: String,
//             default: '',
//         },
//     },
//     data() {
//         return {
//             init: false,
//             objSelect: {
//                 selectidval: '',
//             }
//         }
//     },
//     watch: {
//         treeData: {
//             immediate: true,
//             handler(newData) {
//                 if (!this.init) {
//                     this.initializeTreeData(newData, this.expandAll)
//                     this.init = true
//                 }
//             },
//         },
//     },
//     methods: {
//         initializeTreeData(nodes, expanded) {
//             nodes.forEach((node) => {
//                 this.$set(node, 'expanded', expanded) // 使用 $set 确保响应式
//                 if (node.children && node.children.length > 0) {
//                     this.initializeTreeData(node.children, expanded) // 递归处理子节点
//                 }
//             })
//         },
//         toggleNode(event, node) {
//             event.stopPropagation() // 阻止事件冒泡
//             node.expanded = !node.expanded // 切换节点展开状态
//         },
//         selNode(event, node) {
//             // console.log(this.objSelect, '======')
//             this.initializeTreeData(newData, this.expandAll)
//             this.$set(this.objSelect, 'selectidval', node.id)
//             event.stopPropagation()
//             this.$emit('edit-node', node) // 触发父组件的 edit-node 事件，并传递当前节点
//         },
//     },
// }
</script>

<style scoped>
.pop-title {
    position: relative;
    width: 100%;
    margin: 20px auto 10px;
    font-size: 16px;
    color: rgb(75, 75, 75);
    text-align: center;
}

.tree {
    /* min-height: 300px; */
    padding-left: 15px;
    /* padding-right: 15px; */
}

.children {
    padding-left: 15px;
}

.tree-node {
    display: flex;
    align-items: center;
}

.action-button {
    cursor: pointer;
    margin-left: 10px;
    color: #409eff;
    margin-right: 10px;
}

.edit-button {
    float: right;
}

.delete-button {
    float: right;
}
</style>
