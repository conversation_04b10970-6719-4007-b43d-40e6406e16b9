<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '隐患分类选择框',
  },
}
</route>
<template>
  <wd-select-picker label="隐患分类" v-model="value" :columns="EssentialFactor" align-right type="radio"
    @confirm="handleConfirm" value-key="id" label-key="className" required></wd-select-picker>
</template>

<script lang="ts" setup>
import { posthazardEssentialFactorClassAPI } from '@/pages/hazardPhoto/featch'
import { useUserStore } from '@/store'
const userStore = useUserStore()
const userinfo: any = userStore.userInfo ? userStore.userInfo : {}
const props = defineProps<{
  selectvalue?: string
  essentialFactorId?: string
}>()
const $emit = defineEmits(['getEssentialFactor', 'child-method'])
// // console.log(props.essentialFactorId, '================props.essentialFactorId')
// 隐患分类列表
const EssentialFactor = ref<any[]>()
const gethazardEssentialFactorClass = () => {
  posthazardEssentialFactorClassAPI({
    essentialFactorId: props.essentialFactorId,
    zhId: userinfo.zhId,
  })
    .then((res: any) => {
      EssentialFactor.value = res.data
    })
    .finally(() => {
      // // console.log(111)
    })
}
// if (props.essentialFactorId !== '' && props.essentialFactorId !== undefined) {
//   gethazardEssentialFactorClass()
// }
defineExpose({ gethazardEssentialFactorClass })
// $emit('child-method', gethazardEssentialFactorClass)
const value = ref<string>(props.selectvalue)

function handleConfirm({ value, selectedItems }) {
  $emit('getEssentialFactor', selectedItems)
}

// function convertTocolumns(items) {
//   return items.map((item) => ({
//     label: item.className,
//     value: item.id,
//   }))
// }
</script>

<style lang="scss" scoped></style>
