<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '隐患描述',
  },
}
</route>
<template>
  <wd-cell title="隐患描述" required :value="hazardDescribe" is-link clickable @click="handlebank" />
</template>

<script lang="ts" setup>
const $emit = defineEmits(['gethazardDescribe'])
const hazardDescribe = ref('从隐患库中选择')
const props = defineProps<{
  unitId?: string
}>()

onShow(() => {
  if (uni.getStorageSync('FactorClassItem')) {
    // hazardDescribe.value = uni.getStorageSync('FactorClassItem').inspectionDescribe
  }
})
// 从隐患库中选择隐患检查
function handlebank() {
  // if (props.unitId) {
  // // console.log(props.unitId, 'unitId')
  uni.navigateTo({
    url: `/pages/dangerbank/index?type=1&unitId=${props.unitId || ''}`,
  })
  // } else {
  //   uni.showToast({
  //     icon: 'none',
  //     title: '请先选择隐患单位',
  //   })
  // }
}
</script>

<style lang="scss" scoped></style>
