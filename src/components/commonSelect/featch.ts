import { $api } from '@/api'
import { http } from '@/utils/http'

/** post 请求 获取等级列表 hazardGrade/list   */
export const posthazardGradeAPI = (parameters: any) => {
  return http.post($api.type.hazard + '/hazardGrade/list', parameters)
}
/** post 请求 获取配置等级列表 hazardGrade/list   */
export const posthazardGradeListAPI = (parameters: any) => {
  return http.post($api.type.hazard + '/hazardGrade/listPage', parameters)
}
/** post 请求 获取单位列表 hazardGrade/list   */
export const postAllUnitAPI1 = (parameters: any) => {
  return http.post($api.type.hazard + '/ehsUpms/getAllUnit', parameters)
}

/** post 请求 隐患事件统计  /hazardRecord/statistics  */
export const posthazardRecordStatisticsAPI = (Parameters: any) => {
  return http.post($api.type.hazard + '/hazardRecord/statistics', Parameters)
}
/** post 请求 /hazardRecord/levelStatistics  */
export const posthazardRecordlevelStatisticsAPI = (Parameters: any) => {
  return http.post($api.type.hazard + '/hazardRecord/levelStatistics', Parameters)
}
/** post 获取机构用户列表 /ehsUpms/getOrgUser */
export const postOrgUserAPI = (Parameters: any) => {
  return http.post($api.type.hazard + '/ehsUpms/getOrgUser', Parameters)
}

/** post 请求  隐患事件统计  /hazardPlanTaskEvent/statistics */
export const posthazardPlanTaskEventStatisticsAPI = (Parameters: any) => {
  return http.post($api.type.hazard + '/hazardPlanTaskEvent/statistics', Parameters)
}
/** post 请求 /hazardPlanTaskEvent/levelStatistics */
export const posthazardPlanTaskEventlevelStatisticsAPI = (Parameters: any) => {
  return http.post($api.type.hazard + '/hazardPlanTaskEvent/levelStatistics', Parameters)
}

/** post 请求  隐患事件统计  /hazardMeger/levelStatistics */
export const posthazardMegerlevelStatisticsAPI = (Parameters: any) => {
  return http.post($api.type.hazard + '/hazardMeger/levelStatistics', Parameters)
}
/** post 请求 等级统计//hazardMeger/statistics */
export const posthazardMegerstatisticsAPI = (Parameters: any) => {
  return http.post($api.type.hazard + '/hazardMeger/statistics', Parameters)
}

// 获取组织架构单位
// ehsUpms/getOrgTree?orgCode=bbeda7195cea46e8a5a63d5ee1b178db
export const postgetOrgTreeAPI = (Parameters: any) => {
  return http.post(
    $api.type.hazard +
      '/ehsUpms/getOrgTree?orgCode=' +
      Parameters.orgCode +
      '&orgName=' +
      Parameters.orgName,
    Parameters,
  )
}
// 承租方
// tenantInfo/getTenantryListByUnitId
export const postTenantryListByUnitIdAPI = (Parameters: any) => {
  return http.get(
    $api.type.hazard +
      '/tenantInfo/getTenantryListByUnitId?unitId=' +
      Parameters.unitId +
      '&likeParam=' +
      Parameters.likeParam,
  )
}
// 获取承租方人员
// tenantInfo/getTenantryUsersByTenantryId?tenantryId=01c713a735a74b7595dfcafb640af469&likeParam=&pageNo=1&pageSize=10
export const postTenantryUsersByTenantryIdAPI = (Parameters: any) => {
  return http.get(
    $api.type.hazard +
      '/tenantInfo/getTenantryUsersByTenantryId?tenantryId=' +
      Parameters.tenantryId +
      '&likeParam=' +
      Parameters.likeParam +
      '&pageNo=' +
      Parameters.pageNo +
      '&pageSize=' +
      Parameters.pageSize +
      '&userName=' +
      Parameters.userName,
  )
}

// 获取相关方人员接口
// api/v3/edu-inter-server
// edu-inter-server/perManager/pageList

// keyWord: ""
// pageNo: 1
// pageSize: 10
// type: "0"
// unitId:"7f0624b79f8940bb98d6a0340bcb1a10"
// https://test-bw.gsafetycloud.com/api/v3/edu-app-server/api/workbench/staff/queryList?pageNo=1&pageSize=20&sysCode=web&isBlack=0&unitId=0f933752734046ec9c68be38f3473348
export const postperManagerAPI = (Parameters: any) => {
  // api/v3/edu-app-server/api/workbench/staff/queryList
  return http.get(
    $api.type.eduapp +
      '/api/workbench/staff/queryList?pageNo=' +
      Parameters.pageNo +
      '&pageSize=' +
      Parameters.pageSize +
      '&sysCode=web&isBlack=0&unitId=' +
      Parameters.unitId +
      '&name=' +
      Parameters.name,
  )
  // return http.post('/api/v3/edu-inter-server/perManager/pageList', Parameters)
}

export const postgetUserEventUnitListAPI = (Parameters: any) => {
  return http.post(
    $api.type.hazard + '/hazardRandomCheck/getUserEventUnitList?userId=' + Parameters.userId,
    Parameters,
  )
}

// 获取集团id
export const postTopLevelOrgCodeAPI = (Parameters: any) => {
  return http.post(
    $api.type.hazard + '/ehsUpms/getTopLevelOrgCode?orgCode=' + Parameters.orgCode,
    Parameters,
  )
}

// 判断是否存在承租方和相关方
export const postquerySystemListAPI = (params: any) => {
  console.log(params, 'params')
  return http.get($api.type.platform + `/workbench/msg/querySystemListByZhid?zhId=${params.zhId}`)
}
// 获取承租方/承包商
export const postqueryOrgTreeByTanzerAPI = (Parameters: any) => {
  return http.post(
    $api.type.train +
      '/org/queryOrgTreeByTanzer?needChildUnit=' +
      Parameters.needChildUnit +
      '&needself=' +
      Parameters.needself +
      '&type=' +
      Parameters.type +
      '&keyWord=' +
      Parameters.keyWord +
      '&orgCode=' +
      Parameters.orgCode,
  )
}
// https://agjp.tanzervas.com/aqsc/v1/api/v1/train-server/org/queryOrgTreeByTanzer?needChildUnit=1&needself=1&type=1
