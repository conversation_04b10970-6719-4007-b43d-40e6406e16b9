# 1.4.2

新增

1. 新增`filterValue`属性，支持通过此关键词来搜索并筛选树结构的内容

# 1.4.1

修复

1. 修复单选 onlyRadioLeaf 时末级节点无法选中的 bug

# 1.4.0

版本调整

建议更新，但需要注意，异步数据的时候，后台需返回 leaf 字段来判断是否末项数据

1. **调整数据项格式，新增 `leaf` 字段，来判断是否为末节点**
2. **调整数据项格式，新增 `sort` 字段，来排序节点位置**
3. **注意：异步加载数据，当为末项的时候，需要服务端数据返回 `leaf` 字段**
4. 新增 `alwaysFirstLoad` ，即异步数据总会在第一次展开节点时，拉取一次后台数据，来比对是否一致
5. 拆分 `field` 属性，**注意： 1.5.0 版本后将移除 `field` 属性**
6. 新增 `labelField` 同 `field.label`，指定节点对象中某个属性为**标签**字段，默认`label`
7. 新增 `valueField` 同 `field.key`，指定节点对象中某个属性为**值**字段，默认`value`
8. 新增 `childrenField` 同 `field.children`，指定节点对象中某个属性为**子树节点**字段，默认`children`
9. 新增 `disabledField` 同 `field.disabled`，指定节点对象中某个属性为**禁用**字段，默认`disabled`
10. 新增 `appendField` 同 `field.append`，指定节点对象中某个属性为**副标签**字段，默认`append`
11. 新增 `leafField` 同 `field.label`，指定节点对象中某个属性为**末级节点**字段，默认`leaf`
12. 新增 `sortField` 同 `field.label`，指定节点对象中某个属性为**排序**字段，默认`sort`
13. 新增 `isLeafFn` ，用来自定义控制数据项的末项
14. 更多的项目示例
15. 支持单选取消选中
16. 修复节点展开时可能存在的 bug
17. 修复节点选择可能存在的 bug
18. 调整为子节点默认继承父节点禁用属性
19. `setExpandedKeys` 添加参数一为 `all` 即可支持一键展开/收起全部节点
20. 其它更多优化

# 1.3.4

优化

1. 优化图标字体命名

# 1.3.3

优化

1. 新增方法调用
   > - 新增`getUncheckedKeys`，返回未选的 key
   > - 新增`getUncheckedNodes`，返回未选的节点
   > - 新增`getUnexpandedKeys`，返回未展开的 key
   > - 新增`getUnexpandedNodes`，返回未展开的节点
2. 优化示例项目

# 1.3.2

修复

1. 修复在 APP 真机环境中的报错

# 1.3.1

修复

1. 修复方法`setExpandedKeys`没联动展开上级父子节点

# 1.3.0

优化

1. `field`新增字段 `append` 用于在标签后面显示小提示
2. 新增支持点击标签也能选中节点
3. 方法`setExpandedKeys`支持加载动态数据
4. 修复父节点禁用，则不能展开及图标展开显示
5. 修复动态加载数据时，末级节点的 `children` 为 `null` 时仍显示展开图标

# 1.2.6

新增

1. 新增支持主题换色
2. 支持单选的`onlyRadioLeaf`为`true`时可点父节点展开/收起
3. 优化`expandChecked`调整为不展开无子节点的节点

# 1.2.5

新增

1. 新增 `expandChecked`，控制选择时是否展开当前已选的所有下级节点

# 1.2.4

修复

1. 修复动态数据展开状态异常问题

# 1.2.3

新增

1. 新增 `checkedDisabled`，是否渲染禁用值
2. 新增 `packDisabledkey`，是否返回已禁用并选中的 key
3. 修复选择父级时，子级已禁用但仍被选中的问题

# 1.2.2

优化

1. 调整动态数据载入处理方式
2. 修复节点数据因动态数据引起的状态异常
3. 修复初始节点数据默认选中

# 1.2.1

修复

1. 修复切换`选中状态`被重复选中问题
2. 修复动态数据引起的重复选择问题

# 1.2.0

新增

1. 新增方法调用
   > - 新增`setCheckedKeys`，方法设置指定 key 的节点选中状态
   > - 新增`setExpandedKeys`，方法设置指定 key 的节点展开状态
2. 修复小程序重复插槽一直刷报错问题
3. 优化展开时，会展开子级所以下级节点

# 1.1.1

新增

1. 新增`data`的`disabled`，支持节点禁用状态
2. 新增`field`的`disabled`，可自定`disabled`字段值

# 1.1.0

新增

1. 新增`loadMode`、`loadApi`，支持展开时加载异步数据
2. 新增方法调用
   > - 新增`getCheckedKeys`，方法返回已选的 key
   > - 新增`getHalfCheckedKeys`，方法返回半选的 key
   > - 新增`getExpandedKeys`，方法返回已展开的 key
   > - 新增`getCheckedNodes`，方法返回已选的节点
   > - 新增`getHalfCheckedNodes`，方法返回半选的节点
   > - 新增`getExpandedNodes`，方法返回已展开的节点
3. 对代码进行重构，更易于后期拓展
4. 此次更新后，页面多个的 DaTee 组件间的数据不再关联

# 1.0.6

新增

1. 新增`checkStrictly`，多选模式下选中时是否父子不关联

# 1.0.5

修复

1. 修复多选时已选数据重复问题

# 1.0.4

修复

1. 修复 `change` 事件回调数据的问题

# 1.0.3

优化

1. 优化文档及示例说明

# 1.0.2

新增

1. 新增 `onlyRadioLeaf` ，单选时只允许选中末级
2. 优化默认展开及默认选择的展开问题

# 1.0.1

新增

1. 支持展开/收起回调事件`@expand`

# 1.0.0

初始版本 1.0.0，基于 Vue3 进行开发，支持单选、多选，兼容各大平台

1. 支持单选
2. 支持多选
