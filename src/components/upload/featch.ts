/*
 * @Author: <EMAIL> <EMAIL>
 * @Date: 2024-12-18 11:19:44
 * @LastEditors: <EMAIL> <EMAIL>
 * @LastEditTime: 2024-12-18 17:05:55
 * @FilePath: /hazard-mgr/src/components/upload/featch.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { $api } from '@/api'
import { http } from '@/utils/http'

/** post 请求 /hazardRandomCheck/page */
export const postFileDeleteAPI = (fileurl: string) => {
  return http.post($api.type.hazard + '/file/delete?url=' + fileurl)
}

export const getWatermarkBizData = (coordType: any, longitude: any, latitude: any, orgCode) => {
  return http.get(
    $api.type.platform +
      `/foundation/getWatermarkBizData?coordType=${coordType}&longitude=${longitude}&latitude=${latitude}&orgCode=${orgCode}`,
  )
}
