<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '上传按钮',
  },
}
</route>
<template>
  <view style="display: flex; flex-wrap: wrap; align-items: center; padding: 0 1rem 1rem 1rem">
    <view class="fileList" v-for="(item, index) in data" :key="index">
      <!-- <wd-img style="width: 100%; height: 90%" :src="VITE_PREVIEW_BASEURL + item.fileUrl" :enable-preview="true" /> -->
      <wd-img style="width: 100%; height: 100%" :src="getFileURL(item.fileUrl, true)"
        @click="previewImage(getFileURL(item.fileUrl))" />
      <wd-button style="margin-top: -0.625rem; font-size: 12px" @click="deleteImg(data, item)" type="text">
        删除
      </wd-button>
    </view>
    <wd-loading v-if="showloading" />
    <view @click="chooseFileUpload" class="upload_btn" v-if="data.length < imgslen">
      <wd-icon name="fill-camera" size="1.5rem" style="color: #b8b8b8"></wd-icon>
      <p>添加图片</p>
    </view>
  </view>
  <comOverlay ref="overlay" v-if="imageshow" :imgurl="imgurl" @closeimg="closeimg"></comOverlay>
</template>

<script lang="ts" setup>
import { postFileDeleteAPI, getWatermarkBizData } from './featch'
import { PermissionRes } from '@/hooks/useCamera'
import { useAppStore, usefileConfigStore, useUserStore } from '@/store'
// import ImageCompressor from 'image-compressor.js'
import { getCurrentPosition, getFileURL } from '@/utils'

import { compressFile } from '@/utils/dictData'
// import ImageCompressor from 'image-compressor.js'
// import axios from 'axios'
import axios from 'axios'
import FormData from 'form-data'
import comOverlay from '../commonSelect/com-overlay.vue'
// import fs from 'fs'
// import { getCurrentPosition } from '@/utils'
// const position = await getCurrentPosition()
// console.log(position, 'position=========')
const userStore = useUserStore()
const userInfo: any = userStore.userInfo ? userStore.userInfo : {}
const props = defineProps<{
  imglen?: number
  imginfo?: []
}>()
const VITE_UPLOAD_BASEURL = import.meta.env.VITE_UPLOAD_WATERMARK_BASEURL
// const VITE_PREVIEW_BASEURL = import.meta.env.VITE_PREVIEW_BASEURL
// const VITE_UPLOAD_BASEURL = import.meta.env.VITE_UPLOAD_BASEURL
const VITE_SERVER_BASEURL = import.meta.env.VITE_SERVER_BASEURL
const VITE_APP_PROXY_PREFIX = import.meta.env.VITE_APP_PROXY_PREFIX
// const VITE_UPLOAD_BASEURL = import.meta.env.VITE_UPLOAD_BASEURL
// const usefileStore = usefileConfigStore()

// const VITE_PREVIEW_BASEURL = ref()
const currentEnvInfo = useAppStore().appEnv
// if (!currentEnvInfo.wychApp) {
//   VITE_PREVIEW_BASEURL.value = usefileStore.fileUrl.fileUrlPrefixMiniProgram + '/'
// } else {
//   VITE_PREVIEW_BASEURL.value = usefileStore.fileUrl.fileUrlPrefix + '/'
// }
// getbaseurl()
// // eslint-disable-next-line camelcase
// function getbaseurl() {
//   const dynamicsUrl = location.origin
//   console.log(dynamicsUrl, '=======dynamicsUrl')
//   console.log(location.protocol, '=======location.protocol')
//   if (location.protocol === 'https:') {
//     console.log(dynamicsUrl + import.meta.env.VITE_PREVIEW_BASEURL_dev, '=======https=图片前缀')
//     return (VITE_PREVIEW_BASEURL.value = dynamicsUrl + import.meta.env.VITE_PREVIEW_BASEURL_dev)
//     // return (VITE_PREVIEW_BASEURL.value = import.meta.env.VITE_PREVIEW_BASEURL)
//   } else {
//     console.log(import.meta.env.VITE_PREVIEW_BASEURL, '=======https=走默认配置')
//     return (VITE_PREVIEW_BASEURL.value = import.meta.env.VITE_PREVIEW_BASEURL)
//   }
// }
const showloading = ref(false)
// 返回对象数组
const data = ref<any[]>([])
// const imgsrc = ''
// const text = ref('上传文件')
const load = ref(false)
const { checkCamera } = useCamera()
const imgslen = props.imglen ? props.imglen : 3
function chooseFileUpload() {
  if (data.value.length >= imgslen) {
    uni.showToast({
      icon: 'none',
      title: '最多上传' + imgslen + '张',
    })
  }
  if (currentEnvInfo.osAndroid ?? false) {
    checkCamera()
      .then((res) => {
        if (res === PermissionRes.PASS) {
          useUpload()
        } else if (res === PermissionRes.REJECTED) {
          uni
            .showToast({
              title: '请前往设置中打开权限',
            })
            .then(() => {
              // uni.navigateBack()
            })
        } else if (res === PermissionRes.TOOPEN) {
          uni
            .showToast({
              title: '请重新进入应用，并打开相机权限',
            })
            .then(() => {
              // uni.navigateBack()
            })
        } else {
          uni
            .showToast({
              title: '未知权限结果，请联系管理员',
            })
            .then(() => {
              // uni.navigateBack()
            })
        }
      })
      .catch((e) => {
        useUpload()
      })
  } else {
    console.log('current is h5 web')
    useUpload()
  }
  // useUpload()
}

const $emit = defineEmits(['getFilelist', 'getFileObjList', 'deleteImg'])
// 返回数组
const filelist = ref<string[]>([])

const fileobjlist = ref<string[]>([])

const fileInfo = ref({
  address: 'string',
  dateTime: 'string',
  logoImageUrl: 'string',
  projectName: 'string',
  sourceImageUrl: 'string',
  unitName: 'string',
  userName: 'string',
  waterTemplateName: 'string',
  weather: 'string',
  workContent: 'string',
})
const getWatermark = async () => {
  console.log(currentEnvInfo, '获取水印')
  let position = {}
  if (currentEnvInfo.wychApp) {
    try {
      const { data } = await window.waltz.call({
        // 或者通过 window.waltz.call
        module: 'WZLocation', // 模块名，对应下表的Module
        handlerName: 'getLoactionInfo', // 方法名，对应下表的HandlerName
      })
      console.log(data, '获取的经纬度===data')
      if (+data.code === 10000) {
        // 成功回调的业务代码
        const resultObject = data.body // 接口返回的业务实参对象
        console.log(resultObject, '获取的经纬度')
        if (resultObject) {
          // 坐标系转换
          const pPoint = new GISShare.SMap.Geometry.Point(
            resultObject.longitude,
            resultObject.latitude,
          )
          GISShare.SMap.Fitting.FittingHelper.Fit(
            pPoint, // 目标点
            GISShare.SMap.SpatialReference.GeographicCoordinateSystemStyle.eGCS_WGS_1984_GCJ02, // 当前空间参考
            GISShare.SMap.SpatialReference.GeographicCoordinateSystemStyle.eGCS_WGS_1984_BD09LL, // 目标空间参考
          )
          console.log(pPoint.getX() + ',' + pPoint.getY())
          const _lon = pPoint.getX() || resultObject.longitude
          const _lat = pPoint.getY() || resultObject.latitude

          position = { longitude: _lon, latitude: _lat }
        }
      }
    } catch (error) {
      console.log(error, '获取经纬度失败')
      position = { longitude: 0, latitude: 0 }
      uni.showToast({
        title: 'GPS定位信号弱',
        icon: 'none',
      })
    }
  } else {
    position = await getCurrentPosition()
  }
  console.log(position, "=======position")
  const info = {
    coordType: 'bd09ll',
    longitude: position.longitude,
    latitude: position.latitude,
    orgCode: userInfo.orgCode,
  }
  console.log(info)
  // coordType: any, longitude: any, latitude: any, orgCode
  const res = await getWatermarkBizData(info.coordType, info.longitude, info.latitude, info.orgCode)
  // console.log('res---->:', res)
  if (res.code === 'success') {
    fileInfo.value = res.data
  }
}
onLoad(async () => {
  getWatermark()
  // const position = await getCurrentPosition()
  // console.log(position, 'position=========')
})
// 上传多张照片
const compressFileList = ref()
function useUpload() {
  console.log('调用相机')
  uni.chooseImage({
    count: imgslen,
    sizeType: ['compressed'],
    sourceType: ['album', 'camera'],
    async success(res: any) {
      console.log(res)
      showloading.value = true
      // uni.showLoading({
      //   title: '上传中...',
      //   icon: 'none',
      //   mask: true,
      // })
      for (let i = 0; i < res.tempFiles.length; i++) {
        const formData = new FormData()
        const size = res.tempFiles[i].size / 1024 / 1024 // 计算文件大小（单位：M）
        const extension = res.tempFiles[i].type
        const arr = ['image/jpeg', 'image/png', 'image/webp', 'image/jpg'] // 允许的图片格式列表\\
        console.log(size, 'size====')
        if (size > 10) {
          showloading.value = false
          console.log(size, '图片大小不能超过10M')
          uni.showToast({ title: '图片大小不能超过10M', icon: 'none' })
        } else if (extension && !arr.includes(extension)) {
          showloading.value = false
          uni.showToast({ title: '图片格式仅支持JPG、PNG、JPEG', icon: 'none' })
        } else {
          compressFileList.value = await compressFile(res.tempFiles[i])
          // formData.append('files', res.tempFiles[i])
          formData.append('address', fileInfo.value.address)
          formData.append('dateTime', fileInfo.value.dateTime)
          formData.append('logoImageUrl', fileInfo.value.logoImageUrl)
          // formData.append('projectName', fileInfo.value.projectName)
          formData.append('watermarkProjectName', fileInfo.value.projectName)
          formData.append('sourceImageUrl', fileInfo.value.sourceImageUrl)
          formData.append('unitName', fileInfo.value.unitName)
          formData.append('userName', fileInfo.value.userName)
          formData.append('waterTemplateName', fileInfo.value.waterTemplateName)
          formData.append('weather', fileInfo.value.weather)
          formData.append('workContent', '隐患排查')
          // console.log('formData:', formData.get('dateTime'))
          // uploadFile(formData, res.tempFilePaths[i])
          console.log(compressFileList.value, '压缩后的文件')
          formData.append('files', compressFileList.value)
          // uploadFile(formData, res.tempFilePaths[i])
          uploadFile1(formData)
        }
      }

      // for (let i = 0; i < res.tempFiles.length; i++) {
      //   formData.append('files', res.tempFiles[i])
      //   uploadFile(formData, res.tempFilePaths[i])
      // }
    },
    fail: function (chooseImageError) {
      // console.log(chooseImageError)
      // uni.hideLoading()
      showloading.value = false
      uni.showToast({
        title: chooseImageError,
      })
    },
  })
}

async function uploadFile1(formData) {
  // const url =
  //   VITE_SERVER_BASEURL + VITE_APP_PROXY_PREFIX + '/ehs-clnt-hazard-service' + VITE_UPLOAD_BASEURL
  // console.log(url)
  const apiClient = axios.create({
    baseURL: VITE_SERVER_BASEURL, // 基础URL
    timeout: 60000,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  })
  // /file/watermarkUploads
  apiClient
    .post(VITE_APP_PROXY_PREFIX + 'ehs-clnt-hazard-service' + VITE_UPLOAD_BASEURL, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
        Accept: '*/*',
      },
    })
    .then((response) => {
      console.log('文件上传成功:', response.data)
      if (response.data.code === 'success') {
        const upfiles = response.data
        if (upfiles.data.length > 0) {
          // const urlstr = upfiles.data[0].fileUrl
          // const lastindex = urlstr.lastIndexOf('.')
          // const _res = urlstr.substring(lastindex + 1)
          // upfiles.data[0]._fileUrl = `${urlstr.slice(0, urlstr.length - (_res.length + 1))}_160X160${urlstr.slice(urlstr.length - (_res.length + 1), urlstr.length)}`
          if (data.value.length >= imgslen) {
            uni.showToast({
              title: '最多上传' + imgslen + '张',
            })
            return
          } else {
            data.value.push(upfiles.data[0])
            filelist.value.push(upfiles.data[0].fileUrl)
            $emit('getFilelist', filelist)
            $emit('getFileObjList', data.value)
          }
        }
        // uni.hideLoading()
        showloading.value = false
      } else {
        // uni.hideLoading()
        showloading.value = false
        uni.showToast({
          icon: 'none',
          title: '上传失败',
        })
      }
    })
    .catch((error) => {
      console.error('文件上传失败:', error)
      showloading.value = false
      // uni.hideLoading()
      uni.showToast({
        icon: 'none',
        title: error.message,
      })
    })
    .finally(() => {
      // uni.hideLoading()
      showloading.value = false
    })
}
const overlay = ref(null)
const imageshow = ref(false)
const imgurl = ref('')
const previewImage = (url) => {
  imageshow.value = true
  imgurl.value = url
}
// 关闭查看图片
function closeimg(val) {
  imageshow.value = val
}
// 上传图片
function uploadFile(formData, tempFilePath) {
  uni.uploadFile({
    url: VITE_UPLOAD_BASEURL,
    filePath: tempFilePath,
    name: 'files',
    formData: {
      address: fileInfo.value.address,
      dateTime: fileInfo.value.dateTime,
      logoImageUrl: fileInfo.value.logoImageUrl,
      projectName: fileInfo.value.projectName,
      sourceImageUrl: fileInfo.value.sourceImageUrl,
      unitName: fileInfo.value.unitName,
      userName: fileInfo.value.userName,
      waterTemplateName: fileInfo.value.waterTemplateName,
      weather: fileInfo.value.weather,
      workContent: fileInfo.value.workContent,
    },
    success: (uploadFileRes) => {
      if (uploadFileRes.data) {
        const upfiles = JSON.parse(uploadFileRes.data)
        if (upfiles.data.length > 0) {
          // const urlstr = upfiles.data[0].fileUrl
          // const lastindex = urlstr.lastIndexOf('.')
          // const _res = urlstr.substring(lastindex + 1)
          // // (_res.length + 1)
          // upfiles.data[0]._fileUrl = `${urlstr.slice(0, urlstr.length - (_res.length + 1))}_160X160${urlstr.slice(urlstr.length - (_res.length + 1), urlstr.length)}`
          if (data.value.length >= 10) {
            uni.showToast({
              title: '最多上传10张',
            })
            return
          } else {
            data.value.push(upfiles.data[0])
            filelist.value.push(upfiles.data[0].fileUrl)
            $emit('getFilelist', filelist)
            $emit('getFileObjList', data.value)
          }
        }
        uni.hideLoading()
      } else {
        uni.hideLoading()
        uni.showToast({
          icon: 'none',
          title: '网络错误',
        })
      }
    },
    fail: (uploadFileError) => {
      console.log(uploadFileError)
      uni.hideLoading()
      uni.showToast({
        icon: 'none',
        title: '请求超时,请检查你的网络',
      })
    },
    complete: function () {
      uni.hideLoading()
    },
  })
  // uploadTask.onProgressUpdate((res) => {
  //   console.log('上传进度' + res.progress)
  //   console.log('已经上传的数据长度' + res.totalBytesSent)
  //   console.log('预期需要上传的数据总长度' + res.totalBytesExpectedToSend)
  //   // 测试条件，取消上传任务。
  //   // if (res.progress > 50) {
  //   //   uploadTask.abort();
  //   // }
  // })
}

function handledel(event) {
  // eslint-disable-next-line no-unused-expressions
  console.log(event)
  // const filepath = event.fileUrl.split(VITE_PREVIEW_BASEURL)[1]
  filelist.value = data.value.map((item) => item.fileUrl)
  postFileDeleteAPI(event.fileUrl)
  deleteItem(event.fileUrl)
  console.log(filelist)
  $emit('getFilelist', filelist)
}
function deleteItem(itemToDelete) {
  data.value = data.value.filter((item) => item.fileUrl !== itemToDelete)
  JSON.stringify(data.value)
  console.log(JSON.stringify(data.value), '================删除后')
  $emit('getFileObjList', JSON.parse(JSON.stringify(data.value)))
}

function deleteImg(datainfo, item) {
  // console.log(JSON.stringify(data) + '=========组件图片列表===删除方法============')
  // console.log(JSON.stringify(item) + '=========组件删除方法============')
  // 图片回显列表
  const datalist = JSON.parse(JSON.stringify(datainfo))
  // 点击要删除的图片
  const delitem = JSON.parse(JSON.stringify(item))

  // 删除
  // console.log(
  //   datalist.filter((info) => info.fileUrl !== delitem.fileUrl),
  //   '======',
  // )
  data.value = datalist.filter((info) => info.fileUrl !== delitem.fileUrl)
  filelist.value = data.value.map((info) => info.fileUrl)
  // console.log(data.value)
  postFileDeleteAPI(delitem.fileUrl)
  $emit('getFileObjList', JSON.parse(JSON.stringify(data.value)))
  $emit('getFilelist', JSON.stringify(filelist.value))
  // $emit('deleteImg', value)
}

// function compressFile(file) {
//   return new Promise((resolve, reject) => {
//     // eslint-disable-next-line no-new
//     new ImageCompressor(file, {
//       quality: 0.2,
//       success(result) {
//         resolve(result) // result is a Blob object
//       },
//       error(e) {
//         reject(e)
//       },
//     })
//   })
// }
// this.$emit('deleteImg', value)
// const deleteImg = (event) => {
//   console.log(event, '=========删除图片====================')
// }
watch(
  () => props.imginfo,
  (newVal) => {
    if (newVal) {
      data.value = newVal
      // console.log(data.value, '=========组件图片列表===========')
      // data.value = [
      //   {
      //     id: '8d4479f940584e3391d64c8f1873218a',
      //     randomCheckEventId: '8abd227e89e84e95b8bcb1d4207b0ece',
      //     fileType: 'jpeg',
      //     fileName: '日期',
      //     saveName: 'dcb5df05-6470-45fa-b8d5-cb9b8d20f85f20250206135139.jpeg',
      //     fileUrl:
      //       'hazard/files/2025-02-06/dcb5df05-6470-45fa-b8d5-cb9b8d20f85f20250206135139.jpeg',
      //     createTime: '2025-02-06 13:53:33',
      //     saveType: 'minio',
      //     fileUrlWithBucket:
      //       '/ehs/hazard/files/2025-02-06/dcb5df05-6470-45fa-b8d5-cb9b8d20f85f20250206135139.jpeg',
      //   },
      //   {
      //     id: '8d4479f940584e3391d64c8f1873218a',
      //     randomCheckEventId: '8abd227e89e84e95b8bcb1d4207b0ece',
      //     fileType: 'jpeg',
      //     fileName: '日期',
      //     saveName: 'dcb5df05-6470-45fa-b8d5-cb9b8d20f85f20250206135139.jpeg',
      //     fileUrl:
      //       'hazard/files/2025-02-06/dcb5df05-6470-45fa-b8d5-cb9b8d20f85f20250206135139.jpeg',
      //     createTime: '2025-02-06 13:53:33',
      //     saveType: 'minio',
      //     fileUrlWithBucket:
      //       '/ehs/hazard/files/2025-02-06/dcb5df05-6470-45fa-b8d5-cb9b8d20f85f20250206135139.jpeg',
      //   },
      //   {
      //     id: '8d4479f940584e3391d64c8f1873218a',
      //     randomCheckEventId: '8abd227e89e84e95b8bcb1d4207b0ece',
      //     fileType: 'jpeg',
      //     fileName: '日期',
      //     saveName: 'dcb5df05-6470-45fa-b8d5-cb9b8d20f85f20250206135139.jpeg',
      //     fileUrl:
      //       'hazard/files/2025-02-06/dcb5df05-6470-45fa-b8d5-cb9b8d20f85f20250206135139.jpeg',
      //     createTime: '2025-02-06 13:53:33',
      //     saveType: 'minio',
      //     fileUrlWithBucket:
      //       '/ehs/hazard/files/2025-02-06/dcb5df05-6470-45fa-b8d5-cb9b8d20f85f20250206135139.jpeg',
      //   },
      //   {
      //     id: '8d4479f940584e3391d64c8f1873218a',
      //     randomCheckEventId: '8abd227e89e84e95b8bcb1d4207b0ece',
      //     fileType: 'jpeg',
      //     fileName: '日期',
      //     saveName: 'dcb5df05-6470-45fa-b8d5-cb9b8d20f85f20250206135139.jpeg',
      //     fileUrl:
      //       'hazard/files/2025-02-06/dcb5df05-6470-45fa-b8d5-cb9b8d20f85f20250206135139.jpeg',
      //     createTime: '2025-02-06 13:53:33',
      //     saveType: 'minio',
      //     fileUrlWithBucket:
      //       '/ehs/hazard/files/2025-02-06/dcb5df05-6470-45fa-b8d5-cb9b8d20f85f20250206135139.jpeg',
      //   },
      //   {
      //     id: '8d4479f940584e3391d64c8f1873218a',
      //     randomCheckEventId: '8abd227e89e84e95b8bcb1d4207b0ece',
      //     fileType: 'jpeg',
      //     fileName: '日期',
      //     saveName: 'dcb5df05-6470-45fa-b8d5-cb9b8d20f85f20250206135139.jpeg',
      //     fileUrl:
      //       'hazard/files/2025-02-06/dcb5df05-6470-45fa-b8d5-cb9b8d20f85f20250206135139.jpeg',
      //     createTime: '2025-02-06 13:53:33',
      //     saveType: 'minio',
      //     fileUrlWithBucket:
      //       '/ehs/hazard/files/2025-02-06/dcb5df05-6470-45fa-b8d5-cb9b8d20f85f20250206135139.jpeg',
      //   },
      // ]
      if (data.value.length > 0) {
        // data.value = data.value.map((item) => {
        //   // const lastindex = item.fileUrl.lastIndexOf('.')
        //   // const _res = item.fileUrl.substring(lastindex + 1)
        //   // (_res.length + 1)
        //   return {
        //     ...item,
        //     // _fileUrl: `${item.fileUrl.slice(0, item.fileUrl.length - (_res.length + 1))}_160X160${item.fileUrl.slice(item.fileUrl.length - (_res.length + 1), item.fileUrl.length)}`,
        //   }
        // })
        // console.log(data.value, '=========组件图片列表===========')
        filelist.value = data.value.map((item) => item.fileUrl)
      }
    }
  },
  { immediate: true },
)

// defineExpose({ deleteImg })
</script>

<style lang="scss" scoped>
.upload_btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  // justify-content: flex-end;
  justify-content: flex-start;
  width: 4.125rem;
  height: 4.125rem;
  padding: 0 0.2rem 0.7rem 0.2rem;
  margin: 1rem;
  // text-align: center;
  background-color: rgba(238, 238, 238, 0.91);
  border-radius: 0.375rem;

  p {
    font-size: 0.75rem;
    color: #b8b8b8;
  }
}

.fileList {
  width: 4.125rem;
  height: 4.125rem;
  margin: 1rem;
  text-align: center;
  border-radius: 0.375rem;
}
</style>
