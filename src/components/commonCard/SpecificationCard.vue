<template>
  <view class="spe-card">
    <view class="title" v-if="title">{{ title }}</view>
    <view class="content-box">
      <text v-for="(item, index) of content" :key="index" class="content-item">
        {{ item }}
      </text>
    </view>
  </view>
</template>
<script lang="ts" setup>
import { ISpeProps } from './type'

defineOptions({ name: 'SpeCardComp' })

const props = withDefaults(defineProps<ISpeProps>(), {
  title: '',
  content: [],
})
</script>

<style lang="scss" scoped>
.spe-card {
  @apply relative w-full text-[#0761bb];
  padding: 20rpx 24rpx 38rpx 24rpx;
  background: linear-gradient(90deg, #e0f0ff 2%, #edf6ff 100%);
  border-radius: 8rpx;

  &::after {
    content: '';
    width: 200rpx;
    height: 200rpx;
    position: absolute;
    bottom: 0;
    right: 0;
    background: url('./assets/bg.png') 0 0 no-repeat;
    background-size: 100% 100%;
  }

  .title {
    @apply leading-normal;
    font-size: 32rpx;
    font-weight: 700;
  }

  .content-box {
    margin-top: 10rpx;
  }

  .content-item {
    display: block;
    text-indent: 2em;
    font-size: 28rpx;
    line-height: 1.5em;
  }
}
</style>
