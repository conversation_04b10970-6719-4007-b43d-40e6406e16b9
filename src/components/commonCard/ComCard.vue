<template>
  <view class="com-card">
    <slot name="bg">
      <view class="bg-box" v-if="bgImg">
        <wd-img :width="bgWidth" :height="bgHeight" :src="bgImg" />
      </view>
    </slot>
    <view class="card-header">
      <slot name="title">
        <view class="header-title">
          <wd-icon v-if="icon" :size="iconSize" :name="icon" />
          <text>{{ title }}</text>
        </view>
      </slot>
      <view class="header-btn">
        <slot name="headerBtn"></slot>
      </view>
    </view>
    <view class="card-main">
      <slot></slot>
    </view>
  </view>
</template>
<script lang="ts" setup>
import { IProps } from './type'

defineOptions({ name: 'ComCardComp' })

const props = withDefaults(defineProps<IProps>(), {
  title: '',
  icon: undefined,
  iconSize: '48rpx',
  bgImg: undefined,
  bgWidth: 100,
  bgHeight: 100,
})
</script>

<style lang="scss" scoped>
.com-card {
  @apply relative w-full;
  border-radius: 16rpx;
  padding: 26rpx 24rpx;
  margin-top: 40rpx;
  background: linear-gradient(180deg, #eff3ff 0%, #ffffff 30%, #ffffff 100%);
  box-shadow: 0 8rpx 30rpx 0 rgba(0, 0, 0, 0.05);

  .bg-box {
    @apply absolute top-0 right-0;
    z-index: 0;
    pointer-events: none;
  }

  .card-header {
    @apply flex flex-row flex-nowrap items-center justify-between leading-none;
  }
  .header-title {
    @apply text-[#000] flex flex-row flex-nowrap items-center;
    font-size: 32rpx;
    gap: 20rpx;
    font-weight: 700;
  }

  .header-btn {
    @apply text-[#0256FF] cursor-pointer;
    font-size: 28rpx;
  }
}
</style>
