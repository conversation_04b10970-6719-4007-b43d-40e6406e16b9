<template>
  <view class="empty-wrapper">
    <text v-if="text">{{ text }}</text>
  </view>
</template>
<script lang="ts" setup>
defineOptions({ name: 'emptyComp' })

interface IProps {
  text?: string
}

const props = withDefaults(defineProps<IProps>(), {
  text: '',
})
</script>

<style lang="scss" scoped>
.empty-wrapper {
  display: flex;
  align-items: flex-end;
  justify-content: center;
  color: #6f6f6f;
  width: 50%;
  max-width: 300rpx;
  height: 300rpx;
  margin: 20rpx auto;
  background: url('@/static/images/no-data.png') center/100% no-repeat;
}
</style>
