// 全局要用的类型放到这里

type IResData<T> = {
  code: number | string
  msg: string
  data: T
}

// uni.uploadFile文件上传参数
type IUniUploadFileOptions = {
  file?: File
  files?: UniApp.UploadFileOptionFiles[]
  filePath?: string
  name?: string
  formData?: any
}

type IUserInfo = {
  nickname?: string
  avatar?: string
  /** 微信的 openid，非微信没有这个字段 */
  openid?: string
  token?: string
  unitId: string
  [props: string]: any
}

enum TestEnum {
  A = 'a',
  B = 'b',
}

interface IAPPInfo {
  top?: number
  bottom?: number
  left?: number
  right?: number
  width?: number
  height?: number
}

interface IAPPEnv {
  plus?: boolean
  miniprogram?: boolean
  osAndroid?: boolean
  osIos?: boolean
  wychApp?: boolean
}

interface IUrlQuery {
  safetyArea: IAPPInfo
  ticket: string
  [prop: string]: any
}

interface Window {
  $_ScriptInjector: any
  $receiveData: (data: any) => void
}

interface Uni {
  webView: any
  postMessage: any
}
