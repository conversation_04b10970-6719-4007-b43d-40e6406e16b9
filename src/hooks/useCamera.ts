export enum PermissionRes {
  PASS,
  REJECTED,
  TOOPEN,
  UNKNOWN,
}

/**
 * 检测相机
 */
export function useCamera() {
  const checkCamera = (): Promise<PermissionRes> => {
    return new Promise((resolve, reject) => {
      const platformValue = uni.getSystemInfoSync().platform
      // 检测当前运行的平台
      if (platformValue === 'android') {
        // App环境，使用plus.runtime.openURL打开链接
        try {
          plus.android.requestPermissions(
            ['android.permission.CAMERA'],
            (e) => {
              if (e.deniedAlways.length > 0) {
                // 权限被永久拒绝
                // 弹出提示框解释为何需要权限，引导用户打开设置页面开启
                // console.log('权限被永久拒绝' + e.deniedAlways.toString())
                resolve(PermissionRes.REJECTED)
              }
              if (e.deniedPresent.length > 0) {
                // 权限被临时拒绝
                // 弹出提示框解释为何需要权限，可再次调用plus.android.requestPermissions申请权限
                // console.log('权限被临时拒绝' + e.deniedPresent.toString())
                resolve(PermissionRes.TOOPEN)
              }
              if (e.granted.length > 0) {
                // 权限被允许
                // console.log('权限被允许' + e.granted.toString())
                resolve(PermissionRes.PASS)
              }
              resolve(PermissionRes.UNKNOWN)
            },
            (err) => {
              // console.log('Request Permissions error:' + JSON.stringify(err))
              reject(new Error('get errored permission'))
            },
          )
        } catch (err) {
          console.error(err)
          reject(new Error('request permission errored'))
        }
      } else {
        reject(new Error('暂不支持该平台'))
      }
    })
  }
  // 返回这个函数，以便可以从组件中调用
  return { checkCamera }
}
