import { createSSRApp } from 'vue'
import App from './App.vue'
import store from './store'
import { routeInterceptor, requestInterceptor, prototypeInterceptor } from './interceptors'
import 'virtual:uno.css'
import '@/style/index.scss'
import '@/uni_scripts/uni.webview'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import TlbsMap from 'tlbs-map-vue'
export function createApp() {
  const app = createSSRApp(App)
  app.use(store)
  app.use(routeInterceptor)
  app.use(requestInterceptor)
  app.use(prototypeInterceptor)
  app.use(ElementPlus)
  app.use(TlbsMap)
  return {
    app,
  }
}
