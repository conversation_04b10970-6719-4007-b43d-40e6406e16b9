<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '作业选项卡',
  },
}
</route>

<template>
  <view class="w-full p-3.75 pb-2.5 bg-white box-border" @click="navigateTo">
    <view class="flex flex-justify-between font-size-4 font-900 mb-3.75">
      <view class="color-black">增加压力表</view>
      <view class="color-#FFA945">动火作业</view>
    </view>
    <view class="w-full p-3.75 box-border" style="background-color: #ebeef5">
      <view class="color-black font-size-4 mb-4">特级动火作业</view>
      <view class="color-black font-size-3.5 mb-4">2024-08-15 15:46~2024-08-15 15:46</view>
      <view class="flex flex-justify-between font-size-3.5 mb-4">
        <span class="color-#7F7F7F">作业负责人</span>
        <span class="color-#33333">张三</span>
      </view>
      <view class="flex flex-justify-between font-size-3.5 mb-4">
        <span class="color-#7F7F7F">作业监护人</span>
        <span class="color-#33333">李四</span>
      </view>
      <view class="flex flex-justify-between font-size-3.5 mb-4">
        <span class="color-#7F7F7F">作业进度</span>
        <span class="color-#33333">风险评估</span>
      </view>
      <view class="flex flex-justify-between font-size-3.5 mb-2">
        <span class="color-#7F7F7F">待处理人</span>
        <span class="color-#33333">张思德</span>
      </view>
      <view class="flex flex-justify-end">
        <span><wd-button size="small">风险评估</wd-button></span>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
function navigateTo() {
  uni.navigateTo({
    url: '/pages-safety/danger/index',
  })
}
</script>
<style lang="scss" scoped>
::v-deep {
  .is-primary {
    background: #0256ff !important;
  }
}
</style>
