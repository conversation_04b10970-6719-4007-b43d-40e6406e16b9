<route lang="json5">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '作业列表',
  },
}
</route>

<template>
  <view class="h-100vh flex flex-col overflow-hidden" style="background: #f9f9f9">
    <!-- navbar -->
    <SafetyNavbar :title="title" :searchShow="true">
      <wd-search placeholder-left placeholder="搜索作业内容" :hide-cancel="true" />
    </SafetyNavbar>
    <!-- 下拉菜单 -->

    <wd-drop-menu>
      <wd-drop-menu-item v-model="value1" :options="option1" @change="handleChange1" />
      <wd-drop-menu-item v-model="value2" :options="option2" @change="handleChange2" />
    </wd-drop-menu>
    <view class="flex-1 h-full overflow-scroll borderBox">
      <WorkTab class="mb-4"></WorkTab>
      <WorkTab class="mb-4"></WorkTab>
    </view>
  </view>
</template>

<script lang="ts" setup>
import SafetyNavbar from '../components/safety-navbar.vue'
import WorkTab from './components/work-tab.vue'

// 页面标题
const title = ref('')
onLoad((params) => {
  title.value = params.title
})
// 下拉菜单
const value1 = ref<number>(0)
const value2 = ref<number>(0)

const option1 = ref<Record<string, any>>([
  { label: '全部商品', value: 0 },
  { label: '新款商品', value: 1 },
  { label: '活动商品', value: 2 },
])
const option2 = ref<Record<string, any>>([
  { label: '综合', value: 0 },
  { label: '销量', value: 1 },
  { label: '上架时间', value: 2 },
])

function handleChange1({ value }) {
  // console.log(value)
}
function handleChange2({ value }) {
  // console.log(value)
}
</script>
<style lang="scss">
::v-deep {
  .wd-search {
    background: none !important;
    border: none !important;
  }
}

.borderBox {
  border-top: 0.0625rem solid #ebebeb;
}
</style>
