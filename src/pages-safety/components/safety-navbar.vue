<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '作业选项卡',
  },
}
</route>

<template>
  <view>
    <view class="bgc pt-11 fixed w-full overflow-hidden z-10">
      <wd-navbar v-bind="$attrs" @click-left="handleClickLeft" :bordered="false" left-arrow
        custom-style="background-color: transparent !important;"></wd-navbar>
      <slot></slot>
    </view>
    <view class="h-22 w-full"></view>
    <view class="h-12.5 w-full" v-if="searchShow"></view>
  </view>
</template>

<script lang="ts" setup>
// 控制右边btn显示
const props = withDefaults(defineProps<{ rightBtn?: boolean; searchShow?: boolean }>(), {
  rightBtn: false,
  searchShow: false,
})

// console.log(props.rightBtn, props.searchShow)

function handleClickLeft() {
  uni.navigateBack()
}
</script>
<style lang="scss" scoped>
.bgc {
  background-image: url('@/static/safety/image/work_bg.png');
  background-size: 100%, 100%;
}

::v-deep {
  .wd-navbar__left--hover {
    background: none !important;
  }

  .wd-icon-arrow-left {
    color: white;
  }

  .wd-navbar__title {
    font-size: 1.25rem;
    font-weight: 500;
    color: white;
  }
}
</style>
