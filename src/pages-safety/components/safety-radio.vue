<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '单选框',
  },
}
</route>

<template>
  <view>
    <view class="radio flex text-center">
      <view
        class="radios-item w-30 h-8 leading-8 font-size-3"
        :class="value == e.id ? 'radioActive' : ''"
        v-for="e in data"
        :key="e.id"
        @click="change(e.id)"
      >
        {{ e.param }}
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
interface RadioType {
  id: number
  param: string
}
defineProps({
  // 当前选中的id
  value: {
    type: Number,
    default: 1,
  },

  data: {
    type: Array<RadioType>,
    default: [],
  },
})

const $emit = defineEmits(['change'])

function change(id) {
  $emit('change', id)
}
</script>

<style lang="scss" scoped>
.radio {
  border-left: 0.0625rem solid #ddd;
}

.radios-item {
  background-color: #fff;
  border: 0.0625rem solid #ddd;
  border-left: none;
}

.radioActive {
  color: white;
  background-color: #147df0;
  border-color: #147df0;
}
</style>
