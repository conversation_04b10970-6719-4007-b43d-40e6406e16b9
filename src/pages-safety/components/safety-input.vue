<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: 'label',
  },
}
</route>

<template>
  <view class="">
    <wd-picker :columns="columns" v-model="value" @confirm="handleConfirm" />
  </view>
</template>

<script lang="ts" setup>
const columns = ref(['选项1', '选项2', '选项3', '选项4', '选项5', '选项6', '选项7'])
const value = ref('')

function handleConfirm({ value }) {
  value.value = value
}
</script>

<style lang="scss" scoped>
::v-deep {
  .wd-picker__cell {
    // padding: 0;
    border: 0.0625rem solid #ddd;
  }
}
</style>
