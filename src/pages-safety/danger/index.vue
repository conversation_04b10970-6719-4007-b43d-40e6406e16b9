<route lang="json5">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '风险评估',
  },
}
</route>

<template>
  <view>
    <SafetyNavbar title="风险评估"></SafetyNavbar>
    <Safety-tabs :swipeable="true" :slidable-num="4" :tabsData="dangerTabs"></Safety-tabs>
  </view>
</template>

<script lang="ts" setup>
import SafetyNavbar from '../components/safety-navbar.vue'
import SafetyTabs from '../components/safety-tabs.vue'

const dangerTabs = ref([
  {
    id: 1,
    title: '风险辨识',
  },
  {
    id: 2,
    title: '安全措施',
  },
])
</script>

<style lang="scss" scoped></style>
