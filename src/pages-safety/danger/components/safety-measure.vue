<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '管控措施',
  },
}
</route>

<template>
  <view class="px-4">
    <view
      class="py-3 pb-0 color-#333 font-size-3.5 border-x-0 border-t-0 border-b-1 border-solid border-slate-300"
    >
      <view class="text-wrap mb-4">
        1.动火设备内部构件清洗干净，蒸汽吹扫或水洗、置换合格，达到动火条件
      </view>
      <view class="flex">
        <span class="color-#1480f7 flex-1 text-center h-8 leading-8">编辑</span>
        <span class="color-#d9001b flex-1 text-center h-8 leading-8">删除</span>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup></script>

<style lang="scss" scoped></style>
