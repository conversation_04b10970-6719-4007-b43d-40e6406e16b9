<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '风险辨识',
  },
}
</route>

<template>
  <view class="identify px-4">
    <SafetyLabel :labelText="'风险辨识方式'" class="py-3"></SafetyLabel>
    <SafetyRadio :value="index" :data="data" @change="handleChange"></SafetyRadio>
    <SafetyLabel :labelText="'风险辨识'" class="py-3"></SafetyLabel>
    <SafetyTabList :list="tabList"></SafetyTabList>

    <wd-row>
      <wd-col :span="8">
        <view><wd-button icon="add" plain hairline>添加风险点</wd-button></view>
      </wd-col>
    </wd-row>
    <SafetyLabel :labelText="'风险设备备注信息'" class="py-3"></SafetyLabel>
    <SafetyTextarea></SafetyTextarea>
    <SafetyLabel :labelText="'是否需要检测气体'" class="py-3"></SafetyLabel>
    <SafetyRadio :value="gasIndex" :data="gasTest" @change="gasChange"></SafetyRadio>
    <SafetyLabel :labelText="'气体检测人'" class="py-3"></SafetyLabel>
    <SafetyInput></SafetyInput>
    <SafetyLabel :labelText="'气体检测频次'" class="py-3"></SafetyLabel>
    <SafetyRadio :value="frequency" :data="gasFrequency" @change="gasFrequencyChange"></SafetyRadio>
  </view>
</template>

<script lang="ts" setup>
import SafetyInput from '@/pages-safety/components/safety-input.vue'
import SafetyLabel from '@/pages-safety/components/safety-label.vue'
import SafetyRadio from '@/pages-safety/components/safety-radio.vue'
import SafetyTabList from '@/pages-safety/components/safety-tab-list.vue'
import SafetyTextarea from '@/pages-safety/components/safety-textarea.vue'

// 风险辨识方式
const index = ref<number>(1)
const data = ref([
  {
    id: 1,
    param: '自定义风险辨识',
  },
  {
    id: 0,
    param: '关联已辨识风险点',
  },
])
function handleChange(event) {
  index.value = event
}
// 风险辨识
const tabList = ref([
  {
    id: 0,
    tabName: '物体打击',
  },
  {
    id: 1,
    tabName: '机械伤害',
  },
  {
    id: 2,
    tabName: '起重伤害',
  },
  {
    id: 3,
    tabName: '车辆伤害',
  },
])

// 是否检测气体
const gasIndex = ref(1)
const gasTest = ref([
  {
    id: 1,
    param: '是',
  },
  {
    id: 0,
    param: '否',
  },
])
function gasChange(event) {
  gasIndex.value = event
}
// 气体检测频次
const frequency = ref(0)
const gasFrequency = ref([
  {
    id: 0,
    param: '不重复',
  },
  {
    id: 1,
    param: '重复',
  },
])
function gasFrequencyChange(event) {
  frequency.value = event
}
</script>

<style lang="scss" scoped>
* {
  color: #333;
}
</style>
