<!-- 使用 type="home" 属性设置首页，其他页面不需要设置，默认为page；推荐使用json5，更强大，且允许注释 -->
<route lang="json5" type="home">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '安全作业管理',
  },
}
</route>
<template>
  <view
    class="overflow-scroll px-4 pb-10"
    style="background: linear-gradient(180deg, #dee8fe 0%, rgba(226, 235, 254, 0.26) 100%), #ffffff"
  >
    <view class="font-size-5 text-center font-600 my-5 pt-safe">安全作业管理</view>
    <image class="w-85.75 h-40.25 mb-4" :src="banner" mode="scaleToFill" />
    <view class="overflow-hidden">
      <workManage :data="myAgencyData"></workManage>
      <workManage :data="workManageData"></workManage>
    </view>
  </view>
</template>

<script lang="ts" setup>
import workManage from './components/work-manage.vue'
import banner from '@/static/safety/image/banner.png'
// 引入img
import fxpg from '@/static/safety/nav/fxpg.png'
import aqcs from '@/static/safety/nav/aqcs.png'
import pxkh from '@/static/safety/nav/pxkh.png'
import zysp from '@/static/safety/nav/zysp.png'
import rlsb from '@/static/safety/nav/rlsb.png'
import gcxj from '@/static/safety/nav/gcxj.png'
import xcss from '@/static/safety/nav/xcss.png'
import yssp from '@/static/safety/nav/yssp.png'
import yhcl from '@/static/safety/nav/yhcl.png'

import cgzy from '@/static/safety/nav/cgzy.png'
import dhzy from '@/static/safety/nav/dhzy.png'
import lsydzy from '@/static/safety/nav/lsydzy.png'
import gczy from '@/static/safety/nav/gczy.png'
import dzzy from '@/static/safety/nav/dzzy.png'
import dtzy from '@/static/safety/nav/dtzy.png'
import mbcczy from '@/static/safety/nav/mbcczy.png'
import dlzy from '@/static/safety/nav/dlzy.png'
import sxkjzy from '@/static/safety/nav/sxkjzy.png'

defineOptions({
  name: 'SafetyWork',
})

const myAgencyData = ref({
  title: '我的代办',
  list: [
    {
      id: 1,
      navName: '风险评估',
      img: fxpg,
    },
    {
      id: 2,
      navName: '安全措施',
      img: aqcs,
    },
    {
      id: 3,
      navName: '培训考核',
      img: pxkh,
    },
    {
      id: 4,
      navName: '作业审批',
      img: zysp,
    },
    {
      id: 5,
      navName: '人脸识别',
      img: rlsb,
    },
    {
      id: 6,
      navName: '过程巡检',
      img: gcxj,
    },
    {
      id: 7,
      navName: '现场实施',
      img: xcss,
    },
    {
      id: 8,
      navName: '验收审批',
      img: yssp,
    },
    {
      id: 9,
      navName: '隐患处理',
      img: yhcl,
    },
  ],
})
const workManageData = ref({
  title: '作业管理',
  list: [
    {
      id: 1,
      navName: '常规作业',
      img: cgzy,
    },
    {
      id: 2,
      navName: '动火作业',
      img: dhzy,
    },
    {
      id: 3,
      navName: '临时用电作业',
      img: lsydzy,
    },
    {
      id: 4,
      navName: '高处作业',
      img: gczy,
    },
    {
      id: 5,
      navName: '吊装作业',
      img: dzzy,
    },
    {
      id: 6,
      navName: '动土作业',
      img: dtzy,
    },
    {
      id: 7,
      navName: '盲板抽堵作业',
      img: mbcczy,
    },
    {
      id: 8,
      navName: '断路作业',
      img: dlzy,
    },
    {
      id: 9,
      navName: '受限空间作业',
      img: sxkjzy,
    },
  ],
})
</script>

<style lang="scss" scoped>
* {
  color: #333;
}

.main-title-color {
  color: #d14328;
}
</style>
