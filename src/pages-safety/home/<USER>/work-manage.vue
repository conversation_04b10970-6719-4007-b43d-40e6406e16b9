<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '搜索',
  },
}
</route>
<template>
  <view class="bg-white rounded-2 overflow-hidden mb-4">
    <view class="mx-4 mt-4 mb-5 font-size-4 font-600 color-#323233">
      <span class="inline-block w-0.75 h-3.5 rounded-0.375 mr-2 tipblock"></span>
      <text>{{ data.title }}</text>
    </view>
    <!--  -->
    <wd-row>
      <wd-col
        :span="6"
        class="text-center color-#484A4D"
        v-for="e in data.list"
        :key="e.id"
        @click="navigateTo(e.navName)"
      >
        <view>
          <image class="w-12.25 h-12.25 mb-1.5" :src="e.img" mode="scaleToFill" />
          <view class="font-size-3 mb-4">{{ e.navName }}</view>
        </view>
      </wd-col>
    </wd-row>
    <!--  -->

    <!--  -->
  </view>
</template>
<script lang="ts" setup>
interface SafetyWorkNavType {
  data: {
    title: string
    list: Array<{
      id: number
      navName: string
      img: string
    }>
  }
}
defineProps<SafetyWorkNavType>()

function navigateTo(event) {
  uni.navigateTo({
    url: `/pages-safety/work/index?title=${event}`,
  })
}
</script>
<style lang="scss">
.tipblock {
  background: #527cff;
}
</style>
