import SockJS from 'sockjs-client'
import { Client as Stomp } from '@stomp/stompjs'

interface SocketOptions {
  topic: string
  headers?: Record<string, any>
}

const BASE_URL = import.meta.env.VITE_SERVER_BASEURL
const SOCKET_URL = `${BASE_URL}/api/v3/bw-svc-message-ws-service/ws`

class WsSocket {
  private readonly socket: any
  private readonly stomp: any
  private isClose: boolean = false
  private connectCount: number = 0
  private timer: any
  private readonly CONNECT_LIMIT: number = 100
  private readonly options: SocketOptions
  public onMessage: (data: any) => void = () => {}

  constructor(options: SocketOptions) {
    this.options = options
    this.socket = new SockJS(SOCKET_URL, { transports: ['websocket'] })
    this.stomp = new Stomp({
      webSocketFactory: () => this.socket,
      debug: (str) => console.log(str),
    })
    this.stomp.onConnect = () => this.subscribeToTopic()
    this.stomp.onStompError = (error: any) => this.handleConnectionError(error)

    this.initializeStomp()
    this.setupSocketCloseHandler()
    this.connect()
  }

  private initializeStomp() {
    this.stomp.heartbeatIncoming = 1 // 客户端希望服务端发送的心跳
    this.stomp.heartbeatOutgoing = 1 // 客户端希望服务端发送的心跳
  }

  private setupSocketCloseHandler() {
    this.socket.onclose = () => {
      if (this.isClose) {
        this.connectCount = 0
        return
      }
      if (this.connectCount < this.CONNECT_LIMIT) {
        this.reconnect()
      } else {
        this.connectCount = 0
        this.isClose = true
      }
    }
  }

  private connect() {
    this.stomp.activate()
  }

  private subscribeToTopic() {
    this.stomp.subscribe(
      this.options.topic,
      (response: any) => this.handleMessage(response),
      (error: any) => this.handleSubscriptionError(error),
    )
  }

  private handleMessage(response: any) {
    const body = JSON.parse(response.body)
    // Object.keys(body).forEach((key) => (body[key] = body[key].toString()))
    console.log('Received message:', body)
    this.onMessage(body)
  }

  public sendMsg(message: any) {
    try {
      if (this.stomp) {
        this.stomp.publish({
          destination: this.options.topic,
          body: JSON.stringify(message),
        })
      } else {
        console.error('WebSocket未连接，无法发送消息')
      }
    } catch (e) {
      console.error('发送消息失败:', e)
    }
  }

  private handleSubscriptionError(error: any) {
    console.error('Subscription error:', error)
    setTimeout(() => this.connect(), 120000)
  }

  private handleConnectionError(error: any) {
    console.error('Connection error:', error)
    this.stomp.deactivate()
    setTimeout(() => this.connect(), 5000)
  }

  private reconnect() {
    if (!this.timer) {
      this.timer = setTimeout(() => {
        this.connect()
        this.connectCount++
      }, 3000)
    }
  }

  public destroy() {
    this.stomp.deactivate()
    this.isClose = true
    if (this.timer) {
      clearTimeout(this.timer)
      this.timer = null
    }
  }
}

export default WsSocket
