import helMicro from 'hel-micro'
import * as Vue from 'vue'

helMicro.bindVueRuntime({ Vue })

export const UniNavbar = ref()
export async function callRemoteMethod(method, params) {
  const basePrefix = import.meta.env.VITE_BASE_PREFIX
  // const basePrefix = ''
  const lib = await helMicro.preFetchLib('@tanzerfe/common-comps', {
    async getSubAppAndItsVersionFn() {
      const res = await fetch(
        `${basePrefix}/hel_module/@tanzerfe/common-comps/hel_dist/hel-meta.json?t=${Date.now()}`,
      )
      const meta = await res.json()
      return meta
    },
    hook: {
      beforeAppendAssetNode(passCtx) {
        const { url, setAssetUrl } = passCtx
        const localResourceUrl = url.replace(
          /https:\/\/unpkg.com\/@tanzerfe\/common-comps@\d+\.\d+\.\d+/g,
          `${basePrefix}/hel_module/@tanzerfe/common-comps`,
        )
        setAssetUrl(localResourceUrl)
      },
    },
    skip404Sniff: true,
  })
  UniNavbar.value = lib.UniNavbar
  return lib
}
