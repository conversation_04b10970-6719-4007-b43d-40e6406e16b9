import { useUserStore, useAppStore, useAppReceiveStore, usefileConfigStore } from '@/store'
import { currRoute, getUrlObj, sleep } from '@/utils/index'

export function wvSubscribe() {
  // console.log(window.navigator.userAgent, '取设备浏览器信息')
  const u = decodeURIComponent(window.location.href)
  const paramData: any = parseQueryString(u)
  // console.log(paramData, 'paramData=======')
  if (paramData.location && paramData.location !== 'undefined') {
    uni.setStorageSync('locationCustom', JSON.parse(paramData.location))
  }
  uni.setStorageSync('FromParentParam', paramData)
  const appStore = useAppStore()
  const receiveStore = useAppReceiveStore()

  // 监听UniAppJSBridgeReady
  document.addEventListener('UniAppJSBridgeReady', function () {
    // 设置当前环境
    // uni.webView.getEnv(function (res) {
    //   // console.log('onLaunch>当前环境：' + JSON.stringify(res))
    //   appStore.setAppEnv(res)
    // })
  })

  // 全局监听外部应用消息
  window.$receiveData = function (data) {
    receiveStore.setAppReceiveData(data)
  }
}

export async function beforeEnter() {
  const userStore = useUserStore()
  const appStore = useAppStore()
  const fileConfigStore = usefileConfigStore()
  // 解决下面path初始获取不到的问题
  const _herf = window.location.href
  // alert(_herf)
  // console.log(_herf)
  const { path, query } = getUrlObj(_herf)
  // const { path, query } = currRoute()
  // const { safetyArea, ticket, syscode, appEnv } = urlParamToJson(query)
  // // 设置当前环境
  // appStore.setAppEnv({
  //   plus: appEnv === 'app',
  //   miniprogram: appEnv === 'mp-weixin',
  // })
  const { redirect, safetyArea, ticket, syscode, appEnv, osName } = urlParamToJson(query) // 设置当前环境
  // (redirect
  if (redirect) return
  appStore.setAppEnv({
    plus: appEnv === 'app',
    miniprogram: appEnv === 'mp-weixin',
    wychApp: appEnv === 'wychApp',
    osAndroid: osName === 'android',
    osIos: osName === 'ios',
  })

  if (JSON.stringify(safetyArea) !== '{}') {
    appStore.setAppInfo(safetyArea)
  }
  // 设置用户ticket
  if (ticket) {
    userStore.setUserTicket(ticket)
    // 3. 根据userTicket获取token
    try {
      // alert(JSON.stringify({ sysCode: syscode, token: ticket }))
      const _res = await userStore.getUserInfo({ sysCode: syscode, token: ticket })
      // console.log('getToken', _res.code)
      if (+_res.code === 200) {
        userStore.setUserInfo(_res.data)
        const $data = await fileConfigStore.getfileConfig()
        // console.log($data, '====$data====')
        fileConfigStore.setfileConfig($data.data)
        // console.log(fileConfigStore.fileUrl, '====fileConfigStore.fileUrl====')
        // if ($data.code === 'success') {
        //   fileConfigStore.setfileConfig($data.data.fileUrlPrefix)
        // }
        // uni.setStorageSync('userinfo', _res)
        // // console.log('beforeEnter========userInfo>>>>>>>>>', JSON.stringify(userStore.userInfo))
        // file/getFileConfig
      } else {
        uni.navigateTo({ url: '/pages/error/error' })
      }
    } catch (error) {
      // console.log('getToken>>>err', error)
      uni.navigateTo({ url: '/pages/error/error' })
    }
    /* if (!userStore.isLogined) {
    } */
  } else {
    // 处理从 safe-operation_app 系统跳转过来的场景
    if (syscode === 'safe-operation_app') {
      try {
        // 尝试从本地存储获取已有的用户信息id
        const storedUserInfo = uni.getStorageSync('userInfoObj')
        console.log('storedUserInfo', storedUserInfo, storedUserInfo.data.id)
        if (storedUserInfo && storedUserInfo.data.id) {
          // 调用checkSysPower获取token
          let token = ''
          const respone = await userStore.checkSysPower({ syscode: syscode, id: storedUserInfo.data.id })
          if (respone.code === 'success') {
            token = respone.data.token
          }
          const _res = await userStore.getUserInfo({ sysCode: syscode, token })
          if (+_res.code === 200) {
            userStore.setUserInfo(_res.data)
            const $data = await fileConfigStore.getfileConfig()
            fileConfigStore.setfileConfig($data.data)
          } else {
            console.log('Token可能已过期，需要重新登录')
          }
        }
      } catch (error) {
        console.log('safe-operation_app场景用户信息获取失败:', error)
      }
    } else {
      // uni.navigateTo({ url: '/pages/error/error' })
    }
  }
}

function urlParamToJson(query) {
  const _kw = 'safety_'
  const json: IUrlQuery = {
    safetyArea: {},
    ticket: '',
  }
  for (const key in query) {
    if (Object.prototype.hasOwnProperty.call(query, key)) {
      const value = query[key]
      if (key.includes(_kw)) {
        json.safetyArea[key.replace(_kw, '')] = value
      } else {
        json[key] = value
      }
    }
  }
  // console.log(JSON.stringify(json))
  return json
}
