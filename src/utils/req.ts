import { useUserStore } from '@/store'
const baseUrl = import.meta.env.VITE_SERVER_BASEURL
const VITE_UPLOAD_BASEURL = import.meta.env.VITE_UPLOAD_BASEURL
export function upfile(key, params, baseServe = '/file/upload') {
  const userInfo = useUserStore()
  uni.showLoading({
    title: '加载中',
    mask: true, // 是否显示透明蒙层，防止触摸穿透
  })
  const { filePath } = params
  if (filePath) {
    delete params.filePath
  }
  return new Promise((resolve, reject) => {
    // wx.
    // console.log(params)
    uni.uploadFile({
      // url: baseUrl + baseServe + key, // 仅为示例，非真实的接口地址
      // filePath,
      // name: 'file',
      url: VITE_UPLOAD_BASEURL,
      filePath,
      name: 'files',
      header: {
        // #ifdef MP-WEIXIN
        'Content-Type': 'application/x-www-form-urlencoded',
        // #endif
        token: userInfo.userInfo?.token,
      },
      success: (uploadFileRes) => {
        uni.hideLoading()
        resolve(JSON.parse(uploadFileRes.data))
      },
      fail: (err) => {
        uni.hideLoading()
        reject(err)
      },
    })
  })
}
