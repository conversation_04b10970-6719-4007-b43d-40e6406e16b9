import { pages, subPackages } from '@/pages.json'
import { useAppStore, usefileConfigStore } from '@/store'
import dayjs from 'dayjs'
export const formattedDate = (date) => dayjs(date).format('YYYY-MM-DD')
const getLastPage = () => {
  // getCurrentPages() 至少有1个元素，所以不再额外判断
  // const lastPage = getCurrentPages().at(-1)
  // 上面那个在低版本安卓中打包回报错，所以改用下面这个【虽然我加了src/interceptions/prototype.ts，但依然报错】
  const pages = getCurrentPages()
  return pages[pages.length - 1]
}
export function formatNumber(num) {
  if (!num) {
    return 0
  }
  const strNum = num.toString()
  const parts = strNum.split('.')
  if (parts[1] && parts[1].length > 0) {
    return num.toFixed(2) // 展示两位小数
  } else {
    return num // 展示传过来的整数
  }
}
/** 判断当前页面是否是tabbar页  */
// export const getIsTabbar = () => {
//   if (!tabBar) {
//     return false
//   }
//   if (!tabBar.list.length) {
//     // 通常有tabBar的话，list不能有空，且至少有2个元素，这里其实不用处理
//     return false
//   }
//   const lastPage = getLastPage()
//   const currPath = lastPage.route
//   return !!tabBar.list.find((e) => e.pagePath === currPath)
// }

/**
 * 获取当前页面路由的 path 路径和 redirectPath 路径
 * path 如 ‘/pages/login/index’
 * redirectPath 如 ‘/pages/demo/base/route-interceptor’
 */
export const currRoute = () => {
  const lastPage = getLastPage()
  const currRoute = (lastPage as any).$page
  // // console.log('lastPage.$page:', currRoute)
  // // console.log('lastPage.$page.fullpath:', currRoute.fullPath)
  // // console.log('lastPage.$page.options:', currRoute.options)
  // // console.log('lastPage.options:', (lastPage as any).options)
  // 经过多端测试，只有 fullPath 靠谱，其他都不靠谱
  const { fullPath } = currRoute as { fullPath: string }
  // // console.log(fullPath)
  // eg: /pages/login/index?redirect=%2Fpages%2Fdemo%2Fbase%2Froute-interceptor (小程序)
  // eg: /pages/login/index?redirect=%2Fpages%2Froute-interceptor%2Findex%3Fname%3Dfeige%26age%3D30(h5)
  return getUrlObj(fullPath)
}

const ensureDecodeURIComponent = (url: string) => {
  if (url?.startsWith('%')) {
    return ensureDecodeURIComponent(decodeURIComponent(url))
  }
  return url
}
/**
 * 解析 url 得到 path 和 query
 * 比如输入url: /pages/login/index?redirect=%2Fpages%2Fdemo%2Fbase%2Froute-interceptor
 * 输出: {path: /pages/login/index, query: {redirect: /pages/demo/base/route-interceptor}}
 */
export const getUrlObj = (url: string) => {
  url = url.replace('#/', '')
  let [path, queryStr] = url.split('?')
  queryStr = decodeURIComponent(queryStr)

  if (!queryStr) {
    return {
      path,
      query: {},
    }
  }
  const query: Record<string, string> = {}
  queryStr.split('&').forEach((item) => {
    const [key, value] = item.split('=')
    // console.log('====>>', value)
    query[key] = ensureDecodeURIComponent(value) // 这里需要统一 decodeURIComponent 一下，可以兼容h5和微信y
  })
  return { path, query }
}
/**
 * 得到所有的需要登录的pages，包括主包和分包的
 * 这里设计得通用一点，可以传递key作为判断依据，默认是 needLogin, 与 route-block 配对使用
 * 如果没有传 key，则表示所有的pages，如果传递了 key, 则表示通过 key 过滤
 */
export const getAllPages = (key = 'needLogin') => {
  // 这里处理主包
  const mainPages = [
    ...pages
      .filter((page) => !key || page[key])
      .map((page) => ({
        ...page,
        path: `/${page.path}`,
      })),
  ]
  // 这里处理分包
  const subPages: any[] = []
  subPackages.forEach((subPageObj) => {
    // // console.log(subPageObj)
    const { root } = subPageObj

    subPageObj.pages
      .filter((page) => !key || page[key])
      .forEach((page: { path: string } & Record<string, any>) => {
        subPages.push({
          ...page,
          path: `/${root}/${page.path}`,
        })
      })
  })
  const result = [...mainPages, ...subPages]
  // // console.log(`getAllPages by ${key} result: `, result)
  return result
}

export const debounce = (fn, delay = 500) => {
  let timer: ReturnType<typeof setTimeout>
  return (...args: any[]) => {
    if (timer) clearTimeout(timer)
    timer = setTimeout(() => {
      fn(...args)
    }, delay)
  }
}

/**
 * 得到所有的需要登录的pages，包括主包和分包的
 * 只得到 path 数组
 */
export const getNeedLoginPages = (): string[] => getAllPages('needLogin').map((page) => page.path)

/**
 * 得到所有的需要登录的pages，包括主包和分包的
 * 只得到 path 数组
 */
export const needLoginPages: string[] = getAllPages('needLogin').map((page) => page.path)

/**
 * 延迟函数
 * @param t 毫秒
 */
export function sleep(t: number) {
  return new Promise((resolve) => setTimeout(resolve, t))
}
export function showToast(icon: any, title: string) {
  uni.showToast({
    icon: icon || 'none',
    title: title || '未知错误~',
  })
}

// 获取当前定位
export function getCurrentPosition() {
  return new Promise((resolve, reject) => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const { latitude, longitude } = position.coords
          const pPoint = new GISShare.SMap.Geometry.Point(longitude, latitude)
          GISShare.SMap.Fitting.FittingHelper.Fit(
            pPoint, // 目标点
            GISShare.SMap.SpatialReference.GeographicCoordinateSystemStyle.eGCS_WGS_1984_WGS84, // 当前空间参考
            GISShare.SMap.SpatialReference.GeographicCoordinateSystemStyle.eGCS_WGS_1984_BD09LL, // 目标空间参考
          )
          console.log(pPoint.getX() + ',' + pPoint.getY(), '===================1111')
          const _lon = pPoint.getX() || longitude
          const _lat = pPoint.getY() || latitude
          // position = { longitude: _lon, latitude: _lat }
          resolve({ latitude: _lat, longitude: _lon })
        },
        (error) => {
          reject(error)
        },
      )
    } else {
      reject(new Error('浏览器不支持geolocation'))
    }
  })
}

const isAbsoluteURL = (url: string) => {
  const absoluteURLRegex = /^https?:\/\//
  return absoluteURLRegex.test(url)
}
const imageExtension = /\.(jpeg|jpg|png|gif|bmp|tiff|webp)$/i
export const getFileURL = (filePath: string, preview = false) => {
  let fileBaseUrl // 文件基础路径
  if (!filePath) return ''
  // 如果是绝对路径，就直接使用
  if (isAbsoluteURL(filePath)) return filePath
  const isImg = filePath.match(imageExtension)
  // 兼容本地路径拼接 默认测试环境
  if (location.origin.includes('localhost')) {
    fileBaseUrl =
      filePath.includes('/img1') || filePath.startsWith('/img1') || filePath.startsWith('img1')
        ? 'https://test-bw.gsafetycloud.com'
        : 'https://test-bw.gsafetycloud.com/ehs/'
  } else {
    // const tongming = localStorage.getItem('filePrefix').split('/').pop()
    const usefileStore = usefileConfigStore()
    const tongming = ref()
    const currentEnvInfo = useAppStore().appEnv
    if (!currentEnvInfo.wychApp) {
      tongming.value = usefileStore.fileUrl.fileUrlPrefixMiniProgram
    } else {
      tongming.value = usefileStore.fileUrl.fileUrlPrefix
    }

    const isTmStart =
      filePath.startsWith(tongming.value) || filePath.startsWith('/' + tongming.value)
    // 线上环境路径拼接
    fileBaseUrl =
      filePath.includes('/img1') ||
      filePath.startsWith('/img1') ||
      filePath.startsWith('img1') ||
      isTmStart
        ? tongming.value.substring(0, tongming.value.lastIndexOf('/'))
        : tongming.value + '/'
  }
  console.log(
    fileBaseUrl + (preview && isImg ? replaceWithThumbnailPath(filePath, '_160X160') : filePath),
  )
  return (
    fileBaseUrl + (preview && isImg ? replaceWithThumbnailPath(filePath, '_160X160') : filePath)
  )
}

function replaceWithThumbnailPath(originalImagePath: string, thumbnailSuffix: string) {
  const originalExtension = originalImagePath.match(imageExtension)
  if (!originalExtension) {
    throw new Error('原图路径没有有效的图片扩展名')
  }
  // 如果缩略图后缀包含文件扩展名（例如 "_80X160.jpg"），则直接使用它替换原图的扩展名
  // 否则，将缩略图后缀添加到原图的扩展名之前
  if (thumbnailSuffix.includes('.')) {
    // 缩略图后缀包含点，表示它有自己的扩展名，直接替换原图的扩展名
    return originalImagePath.replace(imageExtension, thumbnailSuffix)
  } else {
    // 缩略图后缀不包含点，表示它是一个参数或简单的后缀，添加到原图的扩展名之前
    return originalImagePath.replace(imageExtension, `${thumbnailSuffix}${originalExtension[0]}`)
  }
}

// export function getDateRange(days) {
//   const today = dayjs().endOf('day') // 获取今日并设置到23:59:59
//   const startDay = today.subtract(days, 'day').startOf('day') // 计算起始日并设置到00:00:00

//   return [startDay.format('YYYY-MM-DD'), today.format('YYYY-MM-DD')]
// }

export function getDateRange(days, type: 'year' | 'month' | 'day' = 'day') {
  // 当前时间锚点
  const now = dayjs()

  // 计算结束时间（包含当天）
  const endDate = type === 'day' ? now.endOf('day') : now.endOf(type)

  // 计算开始时间
  const startDate = endDate
    .subtract(days - 1, type) // 调整为包含起始日
    .startOf(type)

  return [startDate.format('YYYY-MM-DD'), endDate.format('YYYY-MM-DD')]
}

// 类型校验增强版
export function dateToTimestamp(dateString: string) {
  const date = dayjs(dateString, 'YYYY-MM-DD', true) // 严格模式
  return date.isValid() ? date.valueOf() : null
}

export function timestampToDate(timestamp: number) {
  // 参数校验
  if (timestamp < 0 || timestamp.toString().length !== 13) {
    throw new Error('无效时间戳')
  }

  // 返回格式化结果
  return dayjs(timestamp).format('YYYY-MM-DD')
}
