/*
 * @Author: fan<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2024-12-23 15:28:45
 * @LastEditors: Zhangyaling <EMAIL>
 * @LastEditTime: 2025-04-27 14:13:57
 * @FilePath: \hazard-mgr\src\utils\dictData.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import ImageCompressor from 'image-compressor.js'
/**
 * 压缩图片
 */
export function compressFile(file: any) {
  return new Promise((resolve, reject) => {
    const options: any = {
      success(result: any) {
        // 将压缩后的 Blob 转换为 File 对象（如果组件支持Blob则不用这一步）
        const compressedFile = new File([result], file.name, {
          type: file.type,
          lastModified: Date.now(),
        })
        return resolve(compressedFile)
      },
      error(e: any) {
        return reject(e)
      },
    }
    // 2-5mb
    if (file.size > 2 * 1024 * 1024 && file.size <= 5 * 1024 * 1024) {
      options.quality = 0.75 // 压缩质量
      options.convertSize = false // 不进行图像尺寸的调整
      options.checkOrientation = false // 图片翻转，默认为false
    }
    // 5-7mb
    if (file.size > 5 * 1024 * 1024 && file.size <= 7 * 1024 * 1024) {
      options.quality = 0.45 // 压缩质量
      options.convertSize = false // 不进行图像尺寸的调整
      options.checkOrientation = false // 图片翻转，默认为false
    }
    // 7-10mb
    if (file.size > 7 * 1024 * 1024) {
      options.quality = 0.3 // 压缩质量
      options.convertSize = false // 不进行图像尺寸的调整
      options.checkOrientation = false // 图片翻转，默认为false
    }
    // // 1-3MB
    // if (file.size > 1 * 1024 * 1024 && file.size <= 3 * 1024 * 1024) {
    //   options.quality = 0.3 // 压缩质量
    //   options.convertSize = false // 不进行图像尺寸的调整
    //   options.checkOrientation = false // 图片翻转，默认为false
    // }
    // // 3-4MB
    // if (file.size > 3 * 1024 * 1024 && file.size <= 4 * 1024 * 1024) {
    //   options.quality = 0.25 // 压缩质量
    //   options.convertSize = false // 不进行图像尺寸的调整
    //   options.checkOrientation = false // 图片翻转，默认为false
    // }
    // // 5-6MB
    // if (file.size > 5 * 1024 * 1024 && file.size <= 6 * 1024 * 1024) {
    //   options.quality = 0.2 // 压缩质量
    //   options.convertSize = false // 不进行图像尺寸的调整
    //   options.checkOrientation = false // 图片翻转，默认为false
    // }
    // // 6-7MB
    // if (file.size > 6 * 1024 * 1024 && file.size <= 7 * 1024 * 1024) {
    //   options.quality = 0.15 // 压缩质量
    //   options.convertSize = false // 不进行图像尺寸的调整
    //   options.checkOrientation = false // 图片翻转，默认为false
    // }
    // // 7-9MB && file.size <= 15 * 1024 * 1024
    // if (file.size > 7 * 1024 * 1024) {
    //   options.quality = 0.1 // 压缩质量
    //   options.convertSize = false // 不进行图像尺寸的调整
    //   options.checkOrientation = false // 图片翻转，默认为false
    // }
    // eslint-disable-next-line no-new
    new ImageCompressor(file, options)
  })
}
