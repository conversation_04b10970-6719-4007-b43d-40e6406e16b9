<route lang="json5" type="page">
{
  style: {
    navigationStyle: 'custom',
  },
}
</route>

<template>
  <view class="page-wrapper flex-col-box">
    <view class="mb-4 flex-col-box">
      <image class="mb-2.5" :src="errImg"></image>
      <text class="textr-4xl color-[#969799]">页面打开异常！</text>
    </view>

    <view class="mt-[80px]">
      <wd-button block @click="toApp">返回</wd-button>
    </view>
  </view>
</template>

<script lang="ts" setup>
import errImg from './assets/page_err.png'

defineOptions({ name: 'ErrorPage' })

function toApp() {
  uni.webView.switchTab({
    url: '/pages/homepage/index',
  })
}
</script>

<style lang="scss" scoped>
.flex-col-box {
  @apply flex flex-col items-center justify-center;
}
.page-wrapper {
  @apply bg-white overflow-hidden px-[20px];
  width: 100vw;
  height: 100vh;
}
</style>
