import { http } from '@/utils/http'
import { $api } from '@/api'
// 当前单位是否是最底层的业务单位
export const isCheckUnitId = (id: string) => {
  return http.post($api.type.hazard + '/hazardPlanStatistic/checkUnitIdIsBusinessFlag?unitId=' + id)
}
// 任务完成情况
export const tastPage = (parms) => {
  return http.post($api.type.hazard + '/hazardPlanStatistic/taskExecuteDetailPage', parms)
}

export const tastStatistic = (parms) => {
  return http.post($api.type.hazard + '/hazardPlanStatistic/taskAllChildStatistic', parms)
}

// /hazardPlanStatistic/taskAllStatistic
// 任务总数统计
export const taskAllStatisticAPI = (Parameters: any) => {
  return http.post($api.type.hazard + '/hazardPlanStatistic/taskAllStatistic', Parameters)
}
export const yhzgcqyjtj = (Parameters: any) => {
  return http.post('ehs-api-hazard-service' + '/statisReport/yhzgcqyjtj', Parameters)
}
export const tjbbbtzd = (Parameters: any) => {
  return http.post(
    'ehs-api-hazard-service' +
      `/statisReport/tjbbbtzdforApp?unitId=${Parameters.unitId}&type=${Parameters.type}`,
  )
}
export const dwfxyhqktj = (Parameters: any) => {
  return http.post('ehs-api-hazard-service' + '/statisReport/dwfxyhqktj', Parameters)
}
export const yhflqktj = (Parameters: any) => {
  return http.post('ehs-api-hazard-service' + '/statisReport/yhflqktj', Parameters)
}
export const yhzgcqyjlb = (Parameters: any) => {
  return http.post('ehs-api-hazard-service' + '/statisReport/yhzgcqyjlb', Parameters)
}
// /ehsUpms/getOrgDetailInfo //获取机构详情
export const getOrgDetailInfoAPI = (Parameters: any) => {
  return http.post(
    $api.type.hazard + '/ehsUpms/getOrgDetailInfo?orgCode=' + Parameters.orgCode,
    Parameters,
  )
}
export const dwfxyhqklb = (Parameters: any) => {
  return http.post('ehs-api-hazard-service' + '/statisReport/dwfxyhqklb', Parameters)
}
export const dyhflqklb = (Parameters: any) => {
  return http.post('ehs-api-hazard-service' + '/statisReport/dyhflqklb', Parameters)
}
