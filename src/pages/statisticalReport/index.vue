<route lang="json5">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '统计报表',
  },
}
</route>
<template>
  <z-paging ref="paging" @query="init">
    <template #top>
      <SafetyNavbar title="统计报表"></SafetyNavbar>
      <view class="flex flex-col">
        <wd-tabs class="w4-tabs" v-model="tab">
          <!-- <block v-for="item in tabList" :key="item"> -->
          <wd-tab v-for="item in tabList" :key="item" :title="item"></wd-tab>
          <!-- </block> -->
        </wd-tabs>
      </view>
    </template>
    <view class="searchfrom2" v-if="tab === 1">
      <view
        v-for="(item, index) in btnArr"
        :key="index"
        @click="btnClick(index)"
        :class="zindex === index ? 'active' : 'unitlist'"
      >
        {{ item }}
      </view>
    </view>
    <view class="searchfrom">
      <view class="unitlist">
        <commonUnitdata
          ref="commonUnitdataref"
          :types="'1'"
          @getUnit="getUnit"
          :selectvalue="formData.unitId"
          :selecttext="unitname"
        ></commonUnitdata>
      </view>
      <view class="search-box">
        <text
          :class="[filterType === 'custom' ? 'active' : '', 'customBtn']"
          @click="filterTypeHandle('custom')"
        >
          自定义
        </text>
        <text
          :class="[filterType === 'short' ? 'active' : '', 'shortBtn']"
          @click="filterTypeHandle('short')"
        >
          {{ curShortLabel }}
        </text>
      </view>
    </view>
    <view class="content" v-if="tab === 0">
      <dataview ref="dataviewref"></dataview>
      <task-state ref="taskStateRef" :formData="toRaw(formData)" />
    </view>
    <view class="content" v-if="tab === 1">
      <dataview2 ref="dataviewref" :index="zindex"></dataview2>
      <task-state2 ref="taskStateRef" :index="zindex" :text="text" :formData="toRaw(formData)" />
    </view>
    <view class="content" v-if="tab === 2">
      <dataview3 ref="dataviewref"></dataview3>
      <task-state3 ref="taskStateRef" :formData="toRaw(formData)" />
    </view>
    <!-- <view v-if="tab === 1 || tab === 2" class="flex items-center justify-center mt-[30%]">
      <view class="relative">
        <img src="@/static/images/no-data.png" width="237px" height="165px" />
        <text class="absolute bottom-[20px] left-[0] w-[237px] text-center color-[#969799]">
          快马加鞭开发中，敬请期待
        </text>
      </view>
    </view> -->
  </z-paging>
  <!-- 日期控件 -->
  <wd-popup
    v-model="calendarPopup"
    position="bottom"
    :safe-area-inset-bottom="true"
    @close="calendarPopup = false"
  >
    <view class="pop-title">日期范围选择</view>
    <wd-calendar-view
      type="daterange"
      :max-range="365"
      :min-date="mindate"
      :max-date="new Date().getTime()"
      v-model="customDate"
    />
    <view class="popbom">
      <view
        class="pop-button"
        style="border-top: 1px solid #ccc; color: #333"
        @click="calendarClose"
      >
        取消
      </view>
      <view
        class="pop-button"
        style="background-color: #0256ff; border-top: 1px solid #0256ff; color: white"
        @click="calendarComfirm"
      >
        确认
      </view>
    </view>
  </wd-popup>

  <wd-popup
    v-model="shortPopup"
    position="bottom"
    :safe-area-inset-bottom="true"
    @close="shortPopup = false"
    custom-style="height: 30vh;"
  >
    <view class="pop-title">
      日期类型选择
      <wd-icon class="pop-cancel" name="close" size="16px" @click="shortPopup = false"></wd-icon>
    </view>
    <wd-radio-group v-model="curShort" @change="shortHandle" cell>
      <wd-radio v-for="itme in shortList" :key="itme.value" :value="itme.value">
        {{ itme.label }}
      </wd-radio>
    </wd-radio-group>
  </wd-popup>
</template>

<script setup lang="ts">
import { useUserStore } from '@/store'
import SafetyNavbar from '../components/safety-navbar.vue'
import CustomTabs from '../components/custom-Tabs.vue'
import dataview from './components/dataview.vue'
import taskState from './components/taskState.vue'
import dataview2 from './components/dataview2.vue'
import taskState2 from './components/taskState2.vue'
import { getDateRange, dateToTimestamp, timestampToDate } from '@/utils/index'
import dataview3 from './components/dataview3.vue'
import { FilterService } from './filterService'
import taskState3 from './components/taskState3.vue'
// import commonUnit from '@/components/commonSelect/common-Unit.vue'
import commonUnitdata from '@/components/commonSelect/common-Unit_data.vue'
import { getOrgTree } from '../task/featch'
import { getOrgDetailInfoAPI } from './featch'
import { type } from 'os'
const customDate = ref([])
const userStore = useUserStore()
const userinfo: any = userStore.userInfo ? userStore.userInfo : {}
const tab = ref(0)
const tabList = ref(['检查任务完成情况', '隐患整改情况', '隐患整改超期预警'])
const dataviewref = ref()
const taskStateRef = ref()
const commonUnitdataref = ref()
const show = ref(false)
const show1 = ref(false)
const ishover = ref(false)
const paging = ref()
const formData = ref({
  unitId: '',
  levelCode: '',
  startTime: '',
  endTime: '',
  chooseUnitId: '',
})
function calendarClose() {
  customDate.value = []
  calendarPopup.value = false
}
const shortHandle = ({ value }) => {
  curShort.value = value
  FilterService.filterFrom.type = value
  const [startTime, endTime] = getDateRange(value, 'day')
  const isOneDay = value === 1 || value === 2
  dateRangeStr.value = isOneDay ? `${startTime}` : `${startTime}~${endTime}`
  FilterService.filterFrom.value = {
    startTime: isOneDay ? `${startTime} 00:00:00` : `${startTime} 00:00:00`,
    endTime: isOneDay ? `${startTime} 23:59:59` : `${endTime} 23:59:59`,
  }
  formData.value.startTime = `${startTime} 00:00:00`
  formData.value.endTime = `${endTime} 23:59:59`
  dataviewref.value.getData(formData.value, zindex.value)
  taskStateRef.value.init()
  shortPopup.value = false
}
const unitname = ref(userinfo.unitName)
const datavalue = ref([])
// 最小选择时间设置为 当前时间往前推12个月
const mindate = computed(() => {
  const currentDate = new Date()
  const pastDate = new Date(currentDate)
  pastDate.setFullYear(pastDate.getFullYear() - 3)
  // pastDate.setMonth(pastDate.getMonth() - 12)
  const timestamp = pastDate.getTime()
  return timestamp
})

function calendarComfirm() {
  if (!customDate.value[0]) {
    uni.showToast({
      title: '请选择开始时间',
      icon: 'none',
    })
    return
  }
  if (!customDate.value[1]) {
    uni.showToast({
      title: '结束时间不能为空',
      icon: 'none',
    })
    return
  }
  const startTime = timestampToDate(customDate.value[0])
  const endTime = timestampToDate(customDate.value[1])

  FilterService.filterFrom.value = {
    startTime: `${startTime} 00:00:00`,
    endTime: `${endTime} 23:59:59`,
  }
  calendarPopup.value = false
}

// 改变单位
function getUnit(val) {
  unitname.value = val.text
  formData.value.unitId = val.id
  formData.value.levelCode = val.levelCode
  dataviewref.value.getData(formData.value, zindex.value)
  taskStateRef.value.init()
}
// 关闭选择时间
function handleClose() {
  datavalue.value = []
  show.value = false
}
function submitPop() {
  if (!datavalue.value[0]) {
    uni.showToast({
      title: '请选择开始时间',
      icon: 'none',
    })
    return
  }
  if (!datavalue.value[1]) {
    uni.showToast({
      title: '结束时间不能为空',
      icon: 'none',
    })
    return
  }
  formData.value.startTime = getdatetype(datavalue.value[0])
  formData.value.endTime = getdatetype(datavalue.value[1])
  show.value = false
  dataviewref.value.getData(formData.value, zindex.value)
  taskStateRef.value.init()
}
const btnArr = ref(['单位发现隐患情况', '隐患分类情况'])
const zindex = ref(0)
const text = ref('')
const btnClick = (i) => {
  text.value = btnArr[i]
  zindex.value = i
  taskStateRef.value.init()
  dataviewref.value.getData(formData.value, i)
}
const idx = ref(1)
const value1 = ref<number>(1)
const shortList = [
  {
    label: '今日',
    value: 1,
  },
  {
    label: '昨日',
    value: 2,
  },
  {
    label: '近1周',
    value: 7,
  },
  {
    label: '近1个月',
    value: 30,
  },
  {
    label: '近3个月',
    value: 90,
  },
]
function handleChange1(val) {
  // getdate(val.value)
  dataviewref.value.getData(formData.value)
  taskStateRef.value.init()
  show1.value = false
}
const curShort = ref(1)
const curShortLabel = computed(() => {
  const _filter = shortList.filter((item) => item.value === curShort.value)
  return _filter[0].label
})
const filterType = ref('short')
const shortPopup = ref(false)
const calendarPopup = ref(false)
const filterTypeHandle = (type: 'short' | 'custom') => {
  filterType.value = type
  if (type === 'custom') {
    calendarPopup.value = true
  }
  if (type === 'short') {
    shortPopup.value = true
  }
}
// 获取默认时间
// function getdate(type) {
//   console.log(type)
//   //   const today = new Date() // 获取当前时间
//   //   if (type === 0) {
//   //     const yesterday = new Date(today) // 创建一个新的Date对象，初始值为今天的日期时间
//   //     yesterday.setDate(today.getDate() - 1) // 将日期设置为昨天
//   //     const year = yesterday.getFullYear() // 获取昨天的年份
//   //     const month = String(yesterday.getMonth() + 1).padStart(2, '0') // 获取昨天的月份（月份从0开始，需要+1）
//   //     const day = String(yesterday.getDate()).padStart(2, '0') //
//   //     formData.value.startTime = `${year}-${month}-${day}`
//   //     formData.value.endTime = `${year}-${month}-${day}`
//   //   } else if (type === 1) {
//   //     const startOfWeek = new Date(today) // 创建一个新的Date对象，初始值为今天的日期时间
//   //     startOfWeek.setDate(today.getDate() - today.getDay() + 1) // 将日期设置为本周的第一天
//   //     const year = startOfWeek.getFullYear() // 获取本周的年份
//   //     const month = String(startOfWeek.getMonth() + 1).padStart(2, '0') // 获取本周的月份（月份从0开始，需要+1）
//   //     const day = String(startOfWeek.getDate()).padStart(2, '0') // 获取本周的日期
//   //     formData.value.startTime = `${year}-${month}-${day}`
//   //     formData.value.endTime = getdatetype(today)
//   //   } else if (type === 2) {
//   //     const startOfMonth = new Date(today) // 创建一个新的Date对象，初始值为今天的日期时间
//   //     const year = startOfMonth.getFullYear() // 获取本月的年份
//   //     const month = String(startOfMonth.getMonth() + 1).padStart(2, '0')
//   //     // const day = today.setDate(1)
//   //     formData.value.startTime = `${year}-${month}-01`
//   //     formData.value.endTime = getdatetype(today)
//   //   }
//   const now = new Date() // 当前时间
//   let startTime, endTime
//   switch (type) {
//     case 0:
//       startTime = new Date(now)
//       startTime.setHours(0, 0, 0, 0) // 设置为当天的 00:00:00.000
//       endTime = new Date(now)
//       endTime.setHours(23, 59, 59, 999) // 设置为当天的 23:59:59.999
//       break
//     case 1: // 昨日
//       startTime = new Date(now)
//       startTime.setDate(now.getDate() - 1) // 前一天
//       startTime.setHours(0, 0, 0, 0) // 设置为当天的 00:00:00.000
//       endTime = new Date(startTime)
//       endTime.setHours(23, 59, 59, 999) // 设置为当天的 23:59:59.999
//       break

//     case 2: // 本周
//       startTime = new Date(now)
//       startTime.setDate(now.getDate() - now.getDay() + (now.getDay() === 0 ? -6 : 1)) // 本周一
//       startTime.setHours(0, 0, 0, 0) // 设置为当天的 00:00:00.000
//       endTime = new Date(now) // 本周的时间截止到当前日期
//       // endTime = new Date(startTime);
//       // endTime.setDate(startTime.getDate() + 6); // 本周日
//       endTime.setHours(23, 59, 59, 999) // 设置为当天的 23:59:59.999
//       break

//     case 3: // 本月
//       startTime = new Date(now)
//       startTime.setDate(1) // 本月第一天
//       startTime.setHours(0, 0, 0, 0) // 设置为当天的 00:00:00.000
//       endTime = new Date(now) // 本月的时间截止到当前日期
//       // endTime.setMonth(now.getMonth() + 1, 0); // 本月最后一天
//       endTime.setHours(23, 59, 59, 999) // 设置为当天的 23:59:59.999
//       break
//     case 4:
//       startTime = new Date(now)
//       startTime.setMonth(now.getMonth() - 3) // 三个月前
//       startTime.setHours(0, 0, 0, 0) // 设置为当天的 00:00:00.000
//       endTime = new Date(now)
//       endTime.setHours(23, 59, 59, 999) // 设置为当天的 23:59:59.999
//       break
//     default:
//       throw new Error('Invalid type. Type must be 1, 2, or 3.')
//   }
//   const formatDate = (date) => {
//     const year = date.getFullYear()
//     const month = String(date.getMonth() + 1).padStart(2, '0') // 月份补零
//     const day = String(date.getDate()).padStart(2, '0') // 日期补零
//     return `${year}/${month}/${day}`
//   }
//   formData.value.startTime = formatDate(startTime)
//   formData.value.endTime = formatDate(endTime)
//   // return {
//   //   startTime: formatDate(startTime),
//   //   endTime: formatDate(endTime),
//   // }
// }
// 获取时间
function getdatetype(date) {
  const today = new Date(date) // 获取时间
  const year = today.getFullYear() // 获取年份
  const month = String(today.getMonth() + 1).padStart(2, '0') // 获取月份（月份从0开始，需要+1）
  const day = String(today.getDate()).padStart(2, '0')
  return `${year}-${month}-${day}`
}
onLoad((params) => {
  console.log(params, 'params')
  tab.value = params?.tab ? +params?.tab : 0
  if (params?.subTab) {
    nextTick(() => {
      btnClick(+params?.subTab)
    })
  }
  value1.value = params.type
  unitname.value = userinfo.unitName
  formData.value.unitId = userinfo.unitId

  nextTick(() => {
    dataviewref.value.getData(formData.value, zindex.value)
    taskStateRef.value.init()
    // init()
    paging.value.reload()
  })
})
// onShow(() => {
//   nextTick(() => {
//     paging.value.reload()
//   })
// })
const dateRangeStr = ref('')
async function init() {
  console.log('🚀 ~ -----init:')
  unitname.value = userinfo.unitName
  formData.value.unitId = userinfo.unitId
  shortHandle({ value: 1 })
  // getdate(0)
  // 初始化获取当前登录人单位levelCode
  await getOrgDetailInfoAPI({ orgCode: userinfo.unitId }).then((res: any) => {
    // console.log('🚀 ~ res:', res)s
    if (res.code === '200') {
      formData.value.levelCode = res.data.levelCode
      console.log('🚀 ~ res.data.levelCode:', formData.value)
      dataviewref.value.getData(formData.value, zindex.value)
      taskStateRef.value.init()
    }
  })
}

// 切换tab
watch(
  () => tab.value,
  (newVal, oldVal) => {
    nextTick(() => {
      // console.log('🚀 ~ -----watch:', formData.value)
      dataviewref.value.getData(formData.value, zindex.value)
      taskStateRef.value.init()
    })
  },
)

// function findNodeById(tree, id) {
//   console.log('🚀 ~ id:', id)
//   for (const node of tree) {
//     if (node.id === id) {
//       return node // 找到匹配的ID，返回该节点
//     } else if (node.children) {
//       // 如果当前节点有子节点，递归搜索子节点
//       const found = findNodeById(node.children, id)
//       if (found) {
//         return found // 如果在子节点中找到，返回该节点
//       }
//     }
//   }
//   return null // 如果没有找到，返回null
// }
</script>
<style lang="scss" scoped>
::v-deep {
  .navbar {
    position: unset !important;
  }

  .wd-month-panel__title {
    font-size: 18px;
  }

  .wd-tabs__line {
    width: 85px;
    background-color: #0256ff;
    bottom: 0;
  }

  .is-active {
    color: #0256ff;
  }

  uni-page-wrapper {
    background-color: #f5f7fa !important;
    // overflow: auto !important;
  }

  .wd-drop-menu__list {
    background-color: #ffffff00;
  }

  .wd-drop-menu__item {
    height: 32px;
    line-height: 32px;
  }
}

.hover {
  background-color: rgba(2, 86, 255, 0.1) !important;
  color: #0256ff;
}

.seldatatime {
  width: 50%;
  text-align: center;
  height: 32px;
  line-height: 32px;
  border-top-left-radius: 20px;
  border-bottom-left-radius: 20px;
  font-size: 14px;
  background-color: white;
}

.seldatatime1 {
  width: 50%;
  background-color: white;
  border-bottom-right-radius: 20px;
  border-top-right-radius: 20px;
  font-size: 14px;
  height: 32px;
  line-height: 32px;
  text-align: center;
}

.searchfrom {
  padding: 0 12px;
  display: flex;
  margin: 5px 0 10px 0;
  justify-content: space-between;

  .unitlist {
    width: 50%;
  }

  .unitlist1 {
    border: 1px solid #ebebeb;
    border-radius: 20px;
    width: 40%;
    display: flex;
    justify-content: space-around;
    align-items: center;
  }
}
.searchfrom2 {
  padding: 0 12px;
  display: flex;
  margin: 5px 0 10px 0;
  justify-content: space-between;

  .unitlist {
    width: 50%;
    text-align: center;
    border: 1px solid rgb(215, 211, 211);
    color: rgb(176, 175, 175);
  }
  .active {
    color: white;
    width: 50%;
    text-align: center;
    background-color: #029aff;
  }
}

.popbom {
  display: flex;
  justify-content: space-around;
  align-items: center;
  z-index: 99;
  margin: 10px auto;
  margin-bottom: 0px;

  .pop-button1 {
    // border: 1px solid;
    width: 50%;
    text-align: center;
    padding: 8px;
  }
}

.pop-title {
  position: sticky;
  width: 100%;
  margin: 20px auto 10px;
  font-size: 16px;
  color: #000000;
  text-align: center;
  font-weight: bold;

  .pop-cancel {
    position: absolute;
    top: 0px;
    right: 20px;
    color: rgb(175, 175, 175);
  }
}
.search-box {
  display: flex;
  flex-flow: row nowrap;
  align-items: center;
  gap: 0;
  border: 1px solid #ebebeb;
  border-radius: 32rpx;
  background-color: #fff;
  font-size: 28rpx;
  line-height: 28rpx;
  overflow: hidden;
  .customBtn {
    padding: 16rpx 32rpx;
  }
  .shortBtn {
    padding: 16rpx 32rpx;
  }
  .active {
    background-color: rgba(2, 86, 255, 0.1) !important;
    color: #0256ff;
  }
}
</style>
