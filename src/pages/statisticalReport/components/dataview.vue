<template>
  <wd-card>
    <template #title>
      <view class="title">
        <view style="color: #000000; font-size: 16px">数据概览</view>
        <view class="title-tip">统计时间：{{ modelfrom.startTime }}-{{ modelfrom.endTime }}</view>
      </view>
    </template>
    <wd-row :gutter="10">
      <wd-col :span="8">
        <view class="numvalue">{{ taskAllStatistic?.taskAllNum || '0' }}</view>
        <view class="numkey">检查任务总数</view>
      </wd-col>
      <wd-col :span="8">
        <view class="numvalue">{{ taskAllStatistic?.taskUnNum || '0' }}</view>
        <view class="numkey">未完成检查任务数</view>
      </wd-col>
      <wd-col :span="8">
        <view class="numvalue">{{ taskAllStatistic?.taskEdNum || '0' }}</view>
        <view class="numkey">已完成检查任务数</view>
      </wd-col>
    </wd-row>
    <wd-row :gutter="10">
      <wd-col :span="8">
        <view class="numvalue">{{ taskAllStatistic?.taskTimeEdNum || '0' }}</view>
        <view class="numkey">按期已完成检查任务数</view>
      </wd-col>
      <wd-col :span="8">
        <view class="numvalue">{{ taskAllStatistic?.taskTimeOutNum || '0' }}</view>
        <view class="numkey">逾期已完成检查任务数</view>
      </wd-col>
      <wd-col :span="8">
        <view class="numvaluered">{{ formatNumber(taskAllStatistic?.taskEdAvg) }}%</view>
        <view class="numkey">检查任务完成率</view>
      </wd-col>
    </wd-row>
    <wd-row :gutter="10">
      <wd-col :span="8">
        <view class="numvaluered">{{ formatNumber(taskAllStatistic?.taskTimeEdAvg) }}%</view>
        <view class="numkey">检查任务按期完成率</view>
      </wd-col>
    </wd-row>
  </wd-card>
</template>
<script setup lang="ts">
import { taskAllStatisticAPI } from '../featch'
import { formatNumber } from '@/utils/index'
import dayjs from 'dayjs'
const modelfrom = ref({ startTime: '', endTime: '' })
const taskAllStatistic = ref()
const getData = (data) => {
  modelfrom.value.startTime = dayjs(data.startTime).format('YYYY-MM-DD')
  modelfrom.value.endTime = dayjs(data.endTime).format('YYYY-MM-DD')
  taskAllStatisticAPI(data).then((res: any) => {
    if (res.code === 'success') {
      taskAllStatistic.value = res.data
    } else {
      uni.showToast({
        icon: 'none',
        title: res.message,
      })
    }
  })
}
defineExpose({ getData })
</script>
<style lang="scss" scoped>
.wd-col {
  text-align: center;
}

// .content {
//     padding-top: 0px;
// }
.content,
.title {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
}

.content {
  justify-content: flex-start;
}

.title {
  justify-content: space-between;
}

.title-tip {
  color: #9e9e9e;
  font-size: 14px;
}

.numkey {
  color: #000000;
  font-size: 12px;
  font-weight: 400;
}

.numvalue {
  color: #000000;
  font-size: 18px;
  font-weight: 600;
  margin: 8px 0;
}

.numvaluered {
  color: #ff5000;
  font-size: 18px;
  font-weight: 600;
  margin: 8px 0;
}
</style>
