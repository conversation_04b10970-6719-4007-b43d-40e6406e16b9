<!--
 * @Author: xginger <EMAIL>
 * @Date: 2025-06-05 16:10:48
 * @LastEditors: xginger <EMAIL>
 * @LastEditTime: 2025-06-10 23:36:22
 * @FilePath: \hazard-mgr\src\pages\statisticalReport\components\taskState2.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<!--
 * @Author: fangwei<PERSON> <EMAIL>
 * @Date: 2025-03-17 14:26:55
 * @LastEditors: xginger <EMAIL>
 * @LastEditTime: 2025-06-10 18:09:33
 * @FilePath: \hazard-mgr\src\pages\dataanalysis\components\taskState.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <view class="w-full p-[15px] pt-0">
    <view class="w-full bg-[#fff] rounded-[4px] px-[12px] pb-[20px]">
      <text class="flex pt-[16px] text-[16px] text-[#000] font-bold truncate">
        {{ index == 0 ? '单位发现隐患情况' : '隐患分类情况' }}
      </text>

      <!-- filter 部门、监管单位内容-->
      <view class="flex justify-between">
        <wd-sort-button
          v-model="filter.yhzs"
          title="隐患总数"
          @change="({ value }) => handleSortChange('yhzs', value)"
        />
        <wd-sort-button
          v-model="filter.wzgs"
          title="未整改隐患数"
          @change="({ value }) => handleSortChange('wzgs', value)"
        />
        <wd-sort-button
          v-model="filter.yhzgl"
          title="隐患整改率"
          @change="({ value }) => handleSortChange('yhzgl', value)"
        />
      </view>
      <!-- 暂无数据 -->
      <view class="flex items-center justify-center mt-[30%]" v-if="!pageList.length">
        <view class="relative">
          <img src="@/static/images/no-data.png" width="237px" height="165px" />
          <text class="absolute bottom-[20px] left-[0] w-[237px] text-center color-[#969799]">
            暂无数据
          </text>
        </view>
      </view>
      <!-- 业务单位内容 -->

      <!-- 部门、监管单位内容 -->
      <view>
        <view
          class="bg-[#FAFAFA] rounded-[4px] w-full relative mb-[10px]"
          v-for="(item, index) in pageList"
          :key="index"
        >
          <text class="flex pt-[18px] px-[12px] text-[16px] text-[#0256FF] font-bold truncate">
            {{ item.unitName || item.className }}
          </text>
          <view
            class="px-[12px] ease-in-out duration-300 overflow-hidden"
            :class="{ 'max-h-[76px]': !item.isShow, 'max-h-[1000px]': item.isShow }"
          >
            <view class="floatZDY" v-for="i in columsList" :key="i.field">
              <text
                class="text-[18px] text-[red] font-bold"
                v-if="i.fieldName == '隐患整改率' || i.fieldName == '超期已整改率'"
              >
                {{ getItemValue(item, i.field) || 0 }}
              </text>
              <text class="text-[18px] text-[#000] font-bold" v-else>
                {{ getItemValue(item, i.field) || 0 }}
              </text>
              <text class="text-[12px] text-[#000] h-[34px]">{{ i.fieldName }}</text>
            </view>
            <!-- <view class="floatZDY">
              <text class="text-[18px] text-[#000] font-bold">{{ item.wzgs || 0 }}</text>
              <text class="text-[12px] text-[#000] h-[34px]">未整改隐患数</text>
            </view>
            <view class="floatZDY">
              <text class="text-[18px] text-[#FF5000] font-bold">{{ item.yhzgl || 0 }}</text>
              <text class="text-[12px] text-[#000] h-[34px]">隐患整改率</text>
            </view>
            <view class="floatZDY">
              <text class="text-[18px] text-[#000] font-bold">{{ item.yhdj_0 || 0 }}</text>
              <text class="text-[12px] text-[#000] h-[34px]">重大隐患数</text>
            </view>
            <view class="floatZDY">
              <text class="text-[18px] text-[#000] font-bold">{{ item.yhdj_1 || 0 }}</text>
              <text class="text-[12px] text-[#000] h-[34px]">较大隐患数</text>
            </view>
            <view class="floatZDY">
              <text class="text-[18px] text-[#000] font-bold">{{ item.yhdj_2 || 0 }}</text>
              <text class="text-[12px] text-[#000] h-[34px]">一般隐患数</text>
            </view>
            <view class="floatZDY">
              <text class="text-[18px] text-[#000] font-bold">
                {{ formatNumber(item.yhdj_3) }}
              </text>
              <text class="text-[12px] text-[#000] h-[34px]">低风险隐患数</text>
            </view>
            <view class="floatZDY">
              <text class="text-[18px] text-[#000] font-bold">
                {{ formatNumber(item.wzgs) }}
              </text>
              <text class="text-[12px] text-[#000] h-[34px]">未整改隐患数</text>
            </view>
            <view class="floatZDY">
              <text class="text-[18px] text-[#000] font-bold">
                {{ formatNumber(item.yzgyhs) }}
              </text>
              <text class="text-[12px] text-[#000] h-[34px]">已整改隐患数</text>
            </view>
            <view class="floatZDY">
              <text class="text-[18px] text-[#000] font-bold">
                {{ formatNumber(item.cqwzgyhs) }}
              </text>
              <text class="text-[12px] text-[#000] h-[34px]">超期未整改隐患数</text>
            </view>
            <view class="floatZDY">
              <text class="text-[18px] text-[#000] font-bold">
                {{ formatNumber(item.cqyzgyhs) }}
              </text>
              <text class="text-[12px] text-[#000] h-[34px]">超期已整改隐患数</text>
            </view>
            <view class="floatZDY">
              <text class="text-[18px] text-[#FF5000] font-bold">
                {{ formatNumber(item.cqyzgl) }}
              </text>
              <text class="text-[12px] text-[#000] h-[34px]">超期已整改率</text>
            </view> -->
          </view>
          <view
            class="flex items-center justify-center clear-both"
            @click="item.isShow = !item.isShow"
          >
            <wd-icon
              class="ease-in-out duration-300"
              :class="{ 'rotate-180 origin-center': item.isShow }"
              name="arrow-down"
              color="#E5E5E5"
              size="18px"
            ></wd-icon>
          </view>
        </view>
      </view>
      <!-- loading -->
      <view v-if="pageList.length">
        <view class="w-full h-[30px] flex justify-center items-center" v-if="moreLoading">
          <wd-loading :size="20" />
        </view>
        <view
          class="w-full h-[30px] flex justify-center items-center text-[#999] text-[14px] pb-[8px]"
          v-else
        >
          已经到底了
        </view>
      </view>
    </view>
  </view>
</template>
<script setup lang="ts">
import { ref, reactive } from 'vue'
import { tastPage, isCheckUnitId, tastStatistic, dwfxyhqklb, dyhflqklb, tjbbbtzd } from '../featch'
import { formatNumber } from '@/utils/index'
const props = defineProps<{
  // formData 页面条件传参
  formData?: {
    type: object
    default: () => {}
  }
  index: {
    type: number
    default: 0
  }
  text: {
    type: string
    default: ''
  }
}>()

const filter = reactive({
  yhzs: 0,
  wzgs: 0,
  yhzgl: 0,
})
const pageList = ref([])
const pageNo = ref(1)
const pageSize = ref(10)
const showPage = ref<boolean>(false)
const isShowUnit = ref<boolean>(false) // false 业务单位 true 查看统计
const taskState = ref<any>([
  { label: '待开始', bgcolor: '#F59A23', state: '1' },
  { label: '进行中', bgcolor: '#1B67FF', state: '2' },
  { label: '已完成', bgcolor: '#00A720', state: '3' },
  { label: '已停用', bgcolor: '#E23B50', state: '4' },
])
const moreLoading = ref<boolean>(false)

function removeLastComma(str) {
  if (str.charAt(str.length - 1) === ',') {
    return str.slice(0, -1)
  }
  return str
}

const handleSortChange = (prop: string, value: number) => {
  // 确定当前所属的排序属性组
  const nonUnitProps = ['yhzs', 'wzgs', 'yhzgl']
  const currentProps = nonUnitProps

  // 确保prop属于当前组
  if (!currentProps.includes(prop)) return

  // 重置同组其他属性
  currentProps.forEach((key) => {
    if (key !== prop) {
      filter[key] = 0
    }
  })

  // 设置当前属性值
  filter[prop] = value

  // 重置分页并加载数据
  pageNo.value = 1
  pageList.value = []
  getTastList()
}
const getItemValue = (item, field) => {
  return item[field]
}
const getCheckUnitId = async () => {
  const _res: any = await isCheckUnitId(props.formData.unitId)
  isShowUnit.value = _res.data || false
}
let gettastListmore = null
const getTastList = async () => {
  if (pageNo.value === 1) uni.showLoading()
  const parms = {
    ...props.formData,
    sorts: [],
    pageSize: pageSize.value,
    pageNo: pageNo.value,
    orderField: '',
    orderType: '',
  }

  // 监管部门的
  if (filter.yhzs) {
    parms.sorts.push({
      filed: 'yhzs',
      sortType: filter.yhzs === 1 ? 'desc' : 'asc',
    })
    parms.orderType = parms.sorts[0].sortType
    parms.orderField = parms.sorts[0].filed
  }
  if (filter.wzgs) {
    parms.sorts.push({
      filed: 'wzgs',
      sortType: filter.wzgs === 1 ? 'desc' : 'asc',
    })
    parms.orderType = parms.sorts[0].sortType
    parms.orderField = parms.sorts[0].filed
  }
  if (filter.yhzgl) {
    parms.sorts.push({
      filed: 'yhzgl',
      sortType: filter.yhzgl === 1 ? 'desc' : 'asc',
    })
    parms.orderType = parms.sorts[0].sortType
    parms.orderField = parms.sorts[0].filed
  }
  const api = props.index === 0 ? dwfxyhqklb : dyhflqklb
  const res: any = await api(parms)
  if (res.code !== 'success') return uni.hideLoading()
  pageList.value = res.data || []
  moreLoading.value = false
  uni.hideLoading()
}
const columsList = ref([])
const getHeaderData = () => {
  const params = {
    unitId: props.formData.unitId,
    type: props.index === 0 ? 1 : 2,
  }
  tjbbbtzd(params).then((res: any) => {
    if (props.index === 0) {
      columsList.value = res.data.filter((item) => item.fieldName !== '单位名称')
    } else {
      columsList.value = res.data.filter((item) => item.fieldName !== '隐患分类')
    }
  })
}
const init = async () => {
  gettastListmore && clearTimeout(gettastListmore)
  pageList.value = []
  pageNo.value = 1
  showPage.value = true
  await getCheckUnitId()
  getTastList()
  getHeaderData()
}

onHide(() => {
  showPage.value = false
})

onUnmounted(() => {
  showPage.value = false
})

defineExpose({ init })
</script>
<style lang="scss" scoped>
.floatZDY {
  padding: 0 4px;
  float: left;
  width: 33%;
  display: flex;
  text-align: center;
  flex-direction: column;
  padding-top: 10px;
}

// wd-sort-button UI样式
::v-deep .wd-sort-button__left {
  font-size: 15px;
}

::v-deep .wd-sort-button__left.is-active {
  font-size: 15px;
  color: #0256ff;
}

::v-deep .wd-sort-button__left.is-active::after {
  display: none;
}

::v-deep .wd-sort-button__right.is-active .wd-sort-button__icon-up,
::v-deep .wd-sort-button__right.is-active .wd-sort-button__icon-down {
  color: #0256ff;
}
</style>
