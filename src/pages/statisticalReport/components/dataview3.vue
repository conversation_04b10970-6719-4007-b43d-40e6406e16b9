<!--
 * @Author: xginger <EMAIL>
 * @Date: 2025-06-05 14:25:28
 * @LastEditors: xginger <EMAIL>
 * @LastEditTime: 2025-06-10 19:24:09
 * @FilePath: \hazard-mgr\src\pages\statisticalReport\components\dataview3.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <wd-card>
    <template #title>
      <view class="title">
        <view style="color: #000000; font-size: 16px">数据概览</view>
        <view class="title-tip">统计时间：{{ modelfrom.startTime }}-{{ modelfrom.endTime }}</view>
      </view>
    </template>
    <wd-row :gutter="10">
      <wd-col :span="8">
        <view class="numvalue">{{ taskAllStatistic?.total || '0' }}</view>
        <view class="numkey">超期隐患总数</view>
      </wd-col>
      <wd-col :span="8">
        <view class="numvalue">{{ taskAllStatistic?.wzgcq1t || '0' }}</view>
        <view class="numkey">
          超期超1天
          <br />
          (未整改)
        </view>
      </wd-col>
      <wd-col :span="8">
        <view class="numvalue">{{ taskAllStatistic?.wzgcq3t || '0' }}</view>
        <view class="numkey">
          超期超3天
          <br />
          (未整改)
        </view>
      </wd-col>
    </wd-row>
    <wd-row :gutter="10">
      <wd-col :span="8">
        <view class="numvalue">{{ taskAllStatistic?.wzgcq7t || '0' }}</view>
        <view class="numkey">
          超期超7天
          <br />
          (未整改)
        </view>
      </wd-col>
      <wd-col :span="8">
        <view class="numvalue">{{ taskAllStatistic?.ygcq1t || '0' }}</view>
        <view class="numkey">
          超期超1天
          <br />
          (已整改)
        </view>
      </wd-col>
      <wd-col :span="8">
        <view class="numvalue">{{ formatNumber(taskAllStatistic?.yzgcq3t) }}</view>
        <view class="numkey">
          超期超3天
          <br />
          (已整改)
        </view>
      </wd-col>
    </wd-row>
    <wd-row :gutter="10">
      <wd-col :span="8">
        <view class="numvalue">{{ formatNumber(taskAllStatistic?.yzgcq7t) }}</view>
        <view class="numkey">
          超期超7天
          <br />
          (已整改)
        </view>
      </wd-col>
    </wd-row>
  </wd-card>
</template>
<script setup lang="ts">
import { yhzgcqyjtj } from '../featch'
import { formatNumber } from '@/utils/index'
import dayjs from 'dayjs'
const modelfrom = ref({ startTime: '', endTime: '' })
const taskAllStatistic = ref()
const getData = (data) => {
  modelfrom.value.startTime = dayjs(data.startTime).format('YYYY-MM-DD')
  modelfrom.value.endTime = dayjs(data.endTime).format('YYYY-MM-DD')
  yhzgcqyjtj(data).then((res: any) => {
    if (res.code === 'success') {
      taskAllStatistic.value = {
        total:
          res.data.wzgcq1t +
          res.data.wzgcq3t +
          res.data.wzgcq7t +
          res.data.ygcq1t +
          res.data.yzgcq3t +
          res.data.yzgcq7t,
        ...res.data,
      }
    } else {
      uni.showToast({
        icon: 'none',
        title: res.message,
      })
    }
  })
}
defineExpose({ getData })
</script>
<style lang="scss" scoped>
.wd-col {
  text-align: center;
}

// .content {
//     padding-top: 0px;
// }
.content,
.title {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
}

.content {
  justify-content: flex-start;
}

.title {
  justify-content: space-between;
}

.title-tip {
  color: #9e9e9e;
  font-size: 14px;
}

.numkey {
  color: #000000;
  font-size: 12px;
  font-weight: 400;
}

.numvalue {
  color: #000000;
  font-size: 18px;
  font-weight: 600;
  margin: 8px 0;
}

.numvaluered {
  color: #ff5000;
  font-size: 18px;
  font-weight: 600;
  margin: 8px 0;
}
</style>
