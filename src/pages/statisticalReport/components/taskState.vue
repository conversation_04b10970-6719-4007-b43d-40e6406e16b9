<!--
 * @Author: fang<PERSON><PERSON> <EMAIL>
 * @Date: 2025-03-17 14:26:55
 * @LastEditors: fangweiwei <EMAIL>
 * @LastEditTime: 2025-03-26 14:32:11
 * @FilePath: \hazard-mgr\src\pages\dataanalysis\components\taskState.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <view class="w-full p-[15px] pt-0">
    <view class="w-full bg-[#fff] rounded-[4px] px-[12px] pb-[20px]">
      <text class="flex pt-[16px] text-[16px] text-[#000] font-bold truncate">
        {{ isShowUnit ? '单位检查任务完成情况' : '周期检查任务完成情况' }}
      </text>
      <!-- filter 业务单位内容-->
      <view v-if="!isShowUnit" class="flex justify-between">
        <wd-sort-button
          v-model="filter.taskState"
          title="任务状态"
          @change="({ value }) => handleSortChange('taskState', value)"
        />
        <wd-sort-button
          v-model="filter.taskTimeliness"
          title="任务时效"
          @change="({ value }) => handleSortChange('taskTimeliness', value)"
        />
        <wd-sort-button
          v-model="filter.checkProgress"
          title="检查进度"
          @change="({ value }) => handleSortChange('checkProgress', value)"
        />
      </view>
      <!-- filter 部门、监管单位内容-->
      <view v-else class="flex justify-between">
        <wd-sort-button
          v-model="filter.taskAllNum"
          title="任务总数"
          @change="({ value }) => handleSortChange('taskAllNum', value)"
        />
        <wd-sort-button
          v-model="filter.taskEdNum"
          title="已完成任务数"
          @change="({ value }) => handleSortChange('taskEdNum', value)"
        />
        <wd-sort-button
          v-model="filter.taskEdAvg"
          title="任务完成率"
          @change="({ value }) => handleSortChange('taskEdAvg', value)"
        />
      </view>
      <!-- 暂无数据 -->
      <view class="flex items-center justify-center mt-[30%]" v-if="!pageList.length">
        <view class="relative">
          <img src="@/static/images/no-data.png" width="237px" height="165px" />
          <text class="absolute bottom-[20px] left-[0] w-[237px] text-center color-[#969799]">
            暂无数据
          </text>
        </view>
      </view>
      <!-- 业务单位内容 -->
      <view v-if="!isShowUnit">
        <view
          class="bg-[#FAFAFA] rounded-[4px] w-full relative mb-[10px]"
          v-for="(item, index) in pageList"
          :key="index"
        >
          <text
            class="absolute top-0 right-0 text-[12px] text-[#FFF] flex px-[4px] py-[1px] rounded-tr-lg rounded-bl-lg"
            :style="{ background: taskState[item.taskState - 1].bgcolor }"
          >
            {{ taskState[item.taskState - 1].label }}
          </text>
          <text class="flex pt-[18px] px-[12px] text-[16px] text-[#0256FF] font-bold truncate">
            {{ item.taskName }}
          </text>
          <view class="flex justify-between pt-[14px]">
            <view class="flex flex-col items-center px-[4px]">
              <text class="text-[18px] text-[#000] font-bold">
                {{ item.checkContentCount || 0 }}
              </text>
              <text class="whitespace-nowrap text-[12px] text-[#000]">检查内容数</text>
            </view>
            <view class="flex flex-col items-center px-[4px]">
              <text class="text-[18px] text-[#000] font-bold">
                {{ item.checkedContentCount || 0 }}
              </text>
              <text class="whitespace-nowrap text-[12px] text-[#000]">已检查内容数</text>
            </view>
            <view class="flex flex-col items-center px-[4px]">
              <text class="text-[18px] text-[#FF5000] font-bold">{{ item.hazardCount || 0 }}</text>
              <text class="whitespace-nowrap text-[12px] text-[#000]">发现隐患数</text>
            </view>
            <view class="flex flex-col items-center px-[4px]">
              <text class="text-[18px] text-[#0256FF] font-bold" v-if="item.taskTimeliness == '1'">
                正常
              </text>
              <text class="text-[18px] text-[#FF5000] font-bold" v-if="item.taskTimeliness == '2'">
                逾期
              </text>
              <text class="whitespace-nowrap text-[12px] text-[#000]">任务时效</text>
            </view>
          </view>
          <view
            class="ease-in-out duration-500 overflow-hidden px-[12px]"
            :class="{ 'max-h-[0px]': !item.isShow, 'pt-[10px] max-h-[2000px]': item.isShow }"
          >
            <view class="h-[1px] bg-[#EBEBEB] mt-[10px]"></view>
            <view class="flex flex-col my-[10px]">
              <text class="text-[14px]">任务起止日期：</text>
              <text class="text-[14px]">
                {{ item.planStartTime ? `${item.planStartTime} - ${item.planEndTime}` : '--' }}
              </text>
            </view>
            <view class="flex flex-col my-[10px]">
              <text class="text-[14px]">任务类型：</text>
              <text class="text-[14px]">{{ item.checkType || '--' }}</text>
            </view>
            <view class="flex flex-col my-[10px]">
              <text class="text-[14px]">任务实际开始时间：</text>
              <text class="text-[14px]">{{ item.actualStartTime || '--' }}</text>
            </view>
            <view class="flex flex-col my-[10px]">
              <text class="text-[14px]">任务实际结束时间：</text>
              <text class="text-[14px]">{{ item.actualEndTime || '--' }}</text>
            </view>
            <view class="flex flex-col my-[10px]">
              <text class="text-[14px]">检查执行方式：</text>
              <text class="text-[14px]" v-if="item.checkExecuteMethod == '1'">非逐一检查</text>
              <text class="text-[14px]" v-else-if="item.checkExecuteMethod == '2'">逐一检查</text>
              <text class="text-[14px]" v-else>--</text>
            </view>
            <view class="flex flex-col my-[10px]">
              <text class="text-[14px]">检查负责人：</text>
              <text class="text-[14px]">
                {{ item.fzrUsers ? removeLastComma(item.fzrUsers) : '--' }}
              </text>
            </view>
            <view class="flex flex-col my-[10px]">
              <text class="text-[14px]">检查参与人：</text>
              <text class="text-[14px]">
                {{ item.cyrUsers ? removeLastComma(item.cyrUsers) : '--' }}
              </text>
            </view>
          </view>
          <view class="flex items-center justify-center" @click="item.isShow = !item.isShow">
            <wd-icon
              class="ease-in-out duration-300"
              :class="{ 'rotate-180 origin-center': item.isShow }"
              name="arrow-down"
              color="#E5E5E5"
              size="18px"
            ></wd-icon>
          </view>
        </view>
      </view>
      <!-- 部门、监管单位内容 -->
      <view v-else>
        <view
          class="bg-[#FAFAFA] rounded-[4px] w-full relative mb-[10px]"
          v-for="(item, index) in pageList"
          :key="index"
        >
          <text class="flex pt-[18px] px-[12px] text-[16px] text-[#0256FF] font-bold truncate">
            {{ item.unitName }}
          </text>
          <view
            class="px-[12px] ease-in-out duration-300 overflow-hidden"
            :class="{ 'max-h-[76px]': !item.isShow, 'max-h-[1000px]': item.isShow }"
          >
            <view class="floatZDY">
              <text class="text-[18px] text-[#000] font-bold">{{ item.taskAllNum || 0 }}</text>
              <text class="text-[12px] text-[#000] h-[34px]">检查任务总数</text>
            </view>
            <view class="floatZDY">
              <text class="text-[18px] text-[#000] font-bold">{{ item.taskEdNum || 0 }}</text>
              <text class="text-[12px] text-[#000] h-[34px]">已完成检查任务数</text>
            </view>
            <view class="floatZDY">
              <text class="text-[18px] text-[#FF5000] font-bold">{{ item.taskEdAvg || 0 }}%</text>
              <text class="text-[12px] text-[#000] h-[34px]">检查任务完成率</text>
            </view>
            <view class="floatZDY">
              <text class="text-[18px] text-[#000] font-bold">{{ item.taskUnNum || 0 }}</text>
              <text class="text-[12px] text-[#000] h-[34px]">未完成检查任务数</text>
            </view>
            <view class="floatZDY">
              <text class="text-[18px] text-[#000] font-bold">{{ item.taskTimeEdNum || 0 }}</text>
              <text class="text-[12px] text-[#000] h-[34px]">按期已完成检查任务数</text>
            </view>
            <view class="floatZDY">
              <text class="text-[18px] text-[#000] font-bold">
                {{ formatNumber(item.taskTimeOutNum) }}
              </text>
              <text class="text-[12px] text-[#000] h-[34px]">逾期已完成检查任务数</text>
            </view>
            <view class="floatZDY">
              <text class="text-[18px] text-[#FF5000] font-bold">
                {{ formatNumber(item.taskTimeEdAvg) }}%
              </text>
              <text class="text-[12px] text-[#000] h-[34px]">检查任务按期完成率</text>
            </view>
          </view>
          <view
            class="flex items-center justify-center clear-both"
            @click="item.isShow = !item.isShow"
          >
            <wd-icon
              class="ease-in-out duration-300"
              :class="{ 'rotate-180 origin-center': item.isShow }"
              name="arrow-down"
              color="#E5E5E5"
              size="18px"
            ></wd-icon>
          </view>
        </view>
      </view>
      <!-- loading -->
      <view v-if="pageList.length">
        <view class="w-full h-[30px] flex justify-center items-center" v-if="moreLoading">
          <wd-loading :size="20" />
        </view>
        <view
          class="w-full h-[30px] flex justify-center items-center text-[#999] text-[14px] pb-[8px]"
          v-else
        >
          已经到底了
        </view>
      </view>
    </view>
  </view>
</template>
<script setup lang="ts">
import { ref, reactive } from 'vue'
import { tastPage, isCheckUnitId, tastStatistic } from '../featch'
import { formatNumber } from '@/utils/index'
const props = defineProps<{
  // formData 页面条件传参
  formData?: {
    type: object
    default: () => {}
  }
}>()

const filter = reactive({
  taskState: 0,
  taskTimeliness: 0,
  checkProgress: 0,

  taskAllNum: 1,
  taskEdNum: 0,
  taskEdAvg: 0,
})
const pageList = ref([])
const pageNo = ref(1)
const pageSize = ref(10)
const showPage = ref<boolean>(false)
const isShowUnit = ref<boolean>(false) // false 业务单位 true 查看统计
const taskState = ref<any>([
  { label: '待开始', bgcolor: '#F59A23', state: '1' },
  { label: '进行中', bgcolor: '#1B67FF', state: '2' },
  { label: '已完成', bgcolor: '#00A720', state: '3' },
  { label: '已停用', bgcolor: '#E23B50', state: '4' },
])
const moreLoading = ref<boolean>(false)

function removeLastComma(str) {
  if (str.charAt(str.length - 1) === ',') {
    return str.slice(0, -1)
  }
  return str
}

const handleSortChange = (prop: string, value: number) => {
  // 确定当前所属的排序属性组
  const unitProps = ['taskAllNum', 'taskEdNum', 'taskEdAvg']
  const nonUnitProps = ['taskState', 'taskTimeliness', 'checkProgress']
  const currentProps = isShowUnit.value ? unitProps : nonUnitProps

  // 确保prop属于当前组
  if (!currentProps.includes(prop)) return

  // 重置同组其他属性
  currentProps.forEach((key) => {
    if (key !== prop) {
      filter[key] = 0
    }
  })

  // 设置当前属性值
  filter[prop] = value

  // 重置分页并加载数据
  pageNo.value = 1
  pageList.value = []
  getTastList()
}

const getCheckUnitId = async () => {
  const _res: any = await isCheckUnitId(props.formData.unitId)
  isShowUnit.value = _res.data || false
}
let gettastListmore = null
const getTastList = async () => {
  if (pageNo.value === 1) uni.showLoading()
  const parms = {
    ...props.formData,
    sorts: [],
    pageSize: pageSize.value,
    pageNo: pageNo.value,
  }
  if (!isShowUnit.value) {
    parms.sorts.push({
      filed: 'planStartTime',
      sortType: 'desc',
    })
    // 业务单位的
    if (filter.taskTimeliness) {
      parms.sorts.unshift({
        filed: 'taskTimeliness',
        sortType: filter.taskTimeliness === 1 ? 'desc' : 'asc',
      })
    }

    if (filter.checkProgress) {
      parms.sorts.unshift({
        filed: 'checkProgress',
        sortType: filter.checkProgress === 1 ? 'desc' : 'asc',
      })
    }

    if (filter.taskState) {
      parms.sorts.unshift({
        filed: 'taskState',
        sortType: filter.taskState === 1 ? 'desc' : 'asc',
      })
    }
    const res: any = await tastPage(parms)
    if (res.code !== 'success') return uni.hideLoading()
    if (res?.data?.rows?.length) {
      for (let index = 0; index < res.data?.rows?.length; index++) {
        const element = res.data.rows[index]
        element.isShow = false
      }
    }
    if (showPage.value && res?.data?.total > pageNo.value * pageSize.value) {
      moreLoading.value = true
      pageList.value = [...pageList.value, ...res.data.rows]
      pageNo.value = pageNo.value + 1
      gettastListmore = setTimeout(() => {
        getTastList()
      }, 3000)
    } else {
      // gettastListmore && clearTimeout(gettastListmore)
      pageList.value = [...pageList.value, ...res.data.rows]
      moreLoading.value = false
    }
    uni.hideLoading()
  } else {
    // 监管部门的
    if (filter.taskAllNum) {
      parms.sorts.push({
        filed: 'taskAllNum',
        sortType: filter.taskAllNum === 1 ? 'desc' : 'asc',
      })
    }

    if (filter.taskEdNum) {
      parms.sorts.push({
        filed: 'taskEdNum',
        sortType: filter.taskEdNum === 1 ? 'desc' : 'asc',
      })
    }

    if (filter.taskEdAvg) {
      parms.sorts.push({
        filed: 'taskEdAvg',
        sortType: filter.taskEdAvg === 1 ? 'desc' : 'asc',
      })
    }
    const res: any = await tastStatistic(parms)
    if (res.code !== 'success') return uni.hideLoading()
    pageList.value = res.data || []
    moreLoading.value = false
    uni.hideLoading()
  }
}

const init = async () => {
  gettastListmore && clearTimeout(gettastListmore)
  pageList.value = []
  pageNo.value = 1
  showPage.value = true
  await getCheckUnitId()
  getTastList()
}

onHide(() => {
  showPage.value = false
})

onUnmounted(() => {
  showPage.value = false
})

defineExpose({ init })
</script>
<style lang="scss" scoped>
.floatZDY {
  padding: 0 4px;
  float: left;
  width: 33%;
  display: flex;
  text-align: center;
  flex-direction: column;
  padding-top: 10px;
}

// wd-sort-button UI样式
::v-deep .wd-sort-button__left {
  font-size: 15px;
}

::v-deep .wd-sort-button__left.is-active {
  font-size: 15px;
  color: #0256ff;
}

::v-deep .wd-sort-button__left.is-active::after {
  display: none;
}

::v-deep .wd-sort-button__right.is-active .wd-sort-button__icon-up,
::v-deep .wd-sort-button__right.is-active .wd-sort-button__icon-down {
  color: #0256ff;
}
</style>
