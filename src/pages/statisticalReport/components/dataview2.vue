<!--
 * @Author: xginger <EMAIL>
 * @Date: 2025-06-05 16:10:46
 * @LastEditors: xginger <EMAIL>
 * @LastEditTime: 2025-06-10 19:56:59
 * @FilePath: \hazard-mgr\src\pages\statisticalReport\components\dataview2.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <wd-card>
    <template #title>
      <view class="title">
        <view style="color: #000000; font-size: 16px">数据概览</view>
        <view class="title-tip">统计时间：{{ modelfrom.startTime }}-{{ modelfrom.endTime }}</view>
      </view>
    </template>
    <wd-row :gutter="10">
      <wd-col :span="8" v-for="(item, index) in taskAllStatistic" :key="item.code">
        <view
          class="numvalue"
          v-if="
            item.name === '整改完成率' || item.name === '超期已整改率' || item.name === '隐患整改率'
          "
          style="color: red"
        >
          {{ item.value || '0' }}
        </view>
        <view v-else class="numvalue">{{ item.value || '0' }}</view>
        <view class="numkey">{{ item.name }}</view>
      </wd-col>
    </wd-row>
  </wd-card>
</template>
<script setup lang="ts">
import { dwfxyhqktj, yhflqktj } from '../featch'
import dayjs from 'dayjs'
import { formatNumber } from '@/utils/index'
const props = defineProps({
  index: {
    type: Number,
    default: '',
  },
})
const modelfrom = ref({ startTime: '', endTime: '' })
const taskAllStatistic = ref()
const getData = (data, type) => {
  console.log(type, 'data')
  modelfrom.value.startTime = dayjs(data.startTime).format('YYYY-MM-DD')
  modelfrom.value.endTime = dayjs(data.endTime).format('YYYY-MM-DD')
  const api = type === 0 ? dwfxyhqktj : yhflqktj
  console.log(api, 'api')
  if (type == 0) {
    dwfxyhqktj(data).then((res: any) => {
      if (res.code === 'success') {
        taskAllStatistic.value = res.data
      } else {
        uni.showToast({
          icon: 'none',
          title: res.message,
        })
      }
    })
  } else {
    yhflqktj(data).then((res: any) => {
      if (res.code === 'success') {
        taskAllStatistic.value = res.data
      } else {
        uni.showToast({
          icon: 'none',
          title: res.message,
        })
      }
    })
  }
}
defineExpose({ getData })
</script>
<style lang="scss" scoped>
.wd-col {
  text-align: center;
}

// .content {
//     padding-top: 0px;
// }
.content,
.title {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
}

.content {
  justify-content: flex-start;
}

.title {
  justify-content: space-between;
}

.title-tip {
  color: #9e9e9e;
  font-size: 14px;
}

.numkey {
  color: #000000;
  font-size: 12px;
  font-weight: 400;
}

.numvalue {
  color: #000000;
  font-size: 18px;
  font-weight: 600;
  margin: 8px 0;
}

.numvaluered {
  color: #ff5000;
  font-size: 18px;
  font-weight: 600;
  margin: 8px 0;
}
</style>
