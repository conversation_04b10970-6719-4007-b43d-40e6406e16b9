<!--
 * @Author: fang<PERSON><PERSON> <EMAIL>
 * @Date: 2025-03-17 14:26:55
 * @LastEditors: xginger <EMAIL>
 * @LastEditTime: 2025-06-10 19:24:41
 * @FilePath: \hazard-mgr\src\pages\dataanalysis\components\taskState.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <view class="w-full p-[15px] pt-0">
    <view class="w-full bg-[#fff] rounded-[4px] px-[12px] pb-[20px]">
      <text class="flex pt-[16px] text-[16px] text-[#000] font-bold truncate">
        单位隐患整改超期情况
      </text>
      <!-- filter 业务单位内容-->

      <!-- filter 部门、监管单位内容-->
      <view class="flex justify-between">
        <wd-sort-button
          v-model="filter.cqyhzs"
          title="超期隐患总数"
          @change="({ value }) => handleSortChange('cqyhzs', value)"
        />
      </view>
      <!-- 暂无数据 -->
      <view class="flex items-center justify-center mt-[30%]" v-if="!pageList.length">
        <view class="relative">
          <img src="@/static/images/no-data.png" width="237px" height="165px" />
          <text class="absolute bottom-[20px] left-[0] w-[237px] text-center color-[#969799]">
            暂无数据
          </text>
        </view>
      </view>

      <view>
        <view
          class="bg-[#FAFAFA] rounded-[4px] w-full relative mb-[10px]"
          v-for="(item, index) in pageList"
          :key="index"
        >
          <text class="flex pt-[18px] px-[12px] text-[16px] text-[#0256FF] font-bold truncate">
            {{ item.unitName }}
          </text>
          <view
            class="px-[12px] ease-in-out duration-300 overflow-hidden"
            :class="{ 'max-h-[76px]': !item.isShow, 'max-h-[1000px]': item.isShow }"
          >
            <view class="floatZDY">
              <text class="text-[18px] text-[#000] font-bold">{{ item.cqyhzs || 0 }}</text>
              <text class="text-[12px] text-[#000] h-[34px]">超期隐患总数</text>
            </view>
            <view class="floatZDY">
              <text class="text-[18px] text-[#000] font-bold">{{ item.wzgcq1t || 0 }}</text>
              <text class="text-[12px] text-[#000] h-[34px]">
                超期超1天
                <br />
                (未整改)
              </text>
            </view>
            <view class="floatZDY">
              <text class="text-[18px] text-[#000] font-bold">{{ item.wzgcq3t || 0 }}</text>
              <text class="text-[12px] text-[#000] h-[34px]">
                超期超3天
                <br />
                (未整改)
              </text>
            </view>
            <view class="floatZDY">
              <text class="text-[18px] text-[#000] font-bold">{{ item.wzgcq7t || 0 }}</text>
              <text class="text-[12px] text-[#000] h-[34px]">
                超期超7天
                <br />
                (未整改)
              </text>
            </view>
            <view class="floatZDY">
              <text class="text-[18px] text-[#000] font-bold">{{ item.ygcq1t || 0 }}</text>
              <text class="text-[12px] text-[#000] h-[34px]">
                超期超1天
                <br />
                (已整改)
              </text>
            </view>
            <view class="floatZDY">
              <text class="text-[18px] text-[#000] font-bold">
                {{ formatNumber(item.yzgcq3t) }}
              </text>
              <text class="text-[12px] text-[#000] h-[34px]">
                超期超3天
                <br />
                (已整改)
              </text>
            </view>
            <view class="floatZDY">
              <text class="text-[18px] text-[#000] font-bold">
                {{ formatNumber(item.yzgcq7t) }}
              </text>
              <text class="text-[12px] text-[#000] h-[34px]">
                超期超7天
                <br />
                (已整改)
              </text>
            </view>
          </view>
          <view
            class="flex items-center justify-center clear-both"
            @click="item.isShow = !item.isShow"
          >
            <wd-icon
              class="ease-in-out duration-300"
              :class="{ 'rotate-180 origin-center': item.isShow }"
              name="arrow-down"
              color="#E5E5E5"
              size="18px"
            ></wd-icon>
          </view>
        </view>
      </view>
      <!-- loading -->
      <view v-if="pageList.length">
        <view class="w-full h-[30px] flex justify-center items-center" v-if="moreLoading">
          <wd-loading :size="20" />
        </view>
        <view
          class="w-full h-[30px] flex justify-center items-center text-[#999] text-[14px] pb-[8px]"
          v-else
        >
          已经到底了
        </view>
      </view>
    </view>
  </view>
</template>
<script setup lang="ts">
import { ref, reactive } from 'vue'
import { tastPage, isCheckUnitId, yhzgcqyjlb } from '../featch'
import { formatNumber } from '@/utils/index'
const props = defineProps<{
  // formData 页面条件传参
  formData?: {
    type: object
    default: () => {}
  }
}>()

const filter = reactive({
  cqyhzs: 0,
})
const pageList = ref([])
const pageNo = ref(1)
const pageSize = ref(10)
const showPage = ref<boolean>(false)
const isShowUnit = ref<boolean>(false) // false 业务单位 true 查看统计
const taskState = ref<any>([
  { label: '待开始', bgcolor: '#F59A23', state: '1' },
  { label: '进行中', bgcolor: '#1B67FF', state: '2' },
  { label: '已完成', bgcolor: '#00A720', state: '3' },
  { label: '已停用', bgcolor: '#E23B50', state: '4' },
])
const moreLoading = ref<boolean>(false)

function removeLastComma(str) {
  if (str.charAt(str.length - 1) === ',') {
    return str.slice(0, -1)
  }
  return str
}

const handleSortChange = (prop: string, value: number) => {
  // 确定当前所属的排序属性组

  const nonUnitProps = ['cqyhzs']
  const currentProps = nonUnitProps

  // 确保prop属于当前组
  if (!currentProps.includes(prop)) return

  // 重置同组其他属性
  currentProps.forEach((key) => {
    if (key !== prop) {
      filter[key] = 0
    }
  })

  // 设置当前属性值
  filter[prop] = value

  // 重置分页并加载数据
  pageNo.value = 1
  pageList.value = []
  getTastList()
}

const getCheckUnitId = async () => {
  const _res: any = await isCheckUnitId(props.formData.unitId)
  isShowUnit.value = _res.data || false
}
let gettastListmore = null
const getTastList = async () => {
  if (pageNo.value === 1) uni.showLoading()
  const parms = {
    ...props.formData,
    sorts: [],
    pageSize: pageSize.value,
    pageNo: pageNo.value,
    orderField: '',
    orderType: '',
  }

  // 监管部门的
  if (filter.cqyhzs) {
    parms.sorts.push({
      filed: 'cqyhzs',
      sortType: filter.cqyhzs === 1 ? 'desc' : 'asc',
    })
    parms.orderType = parms.sorts[0].sortType
    parms.orderField = parms.sorts[0].filed
  }

  const res: any = await yhzgcqyjlb(parms)
  if (res.code !== 'success') return uni.hideLoading()
  pageList.value = res.data || []
  moreLoading.value = false
  uni.hideLoading()
}

const init = async () => {
  gettastListmore && clearTimeout(gettastListmore)
  pageList.value = []
  pageNo.value = 1
  showPage.value = true
  await getCheckUnitId()
  getTastList()
}

onHide(() => {
  showPage.value = false
})

onUnmounted(() => {
  showPage.value = false
})

defineExpose({ init })
</script>
<style lang="scss" scoped>
.floatZDY {
  padding: 0 4px;
  float: left;
  width: 33%;
  display: flex;
  text-align: center;
  flex-direction: column;
  padding-top: 10px;
}

// wd-sort-button UI样式
::v-deep .wd-sort-button__left {
  font-size: 15px;
}

::v-deep .wd-sort-button__left.is-active {
  font-size: 15px;
  color: #0256ff;
}

::v-deep .wd-sort-button__left.is-active::after {
  display: none;
}

::v-deep .wd-sort-button__right.is-active .wd-sort-button__icon-up,
::v-deep .wd-sort-button__right.is-active .wd-sort-button__icon-down {
  color: #0256ff;
}
</style>
