<template>
  <view>
    <view class="header">
      <view class="title">
        <wd-icon size="28rpx" :name="arrowIcon" />
        <text class="title-text">高频隐患详情TOP5</text>
      </view>
      <wd-button type="text" @click="toPage">更多 ></wd-button>
    </view>

    <view class="table-box">
      <wd-table :data="dataList" v-if="dataList.length > 0">
        <wd-table-col width="50" align="center" prop="index" label="TOP" fixed>
          <template #value="{ index }">
            <view :class="['top-col', `top-col_${index + 1}`]">
              <text>{{ index + 1 }}</text>
            </view>
          </template>
        </wd-table-col>
        <wd-table-col width="45%" prop="hazardDesc" label="隐患描述"></wd-table-col>
        <wd-table-col width="80" prop="total" label="隐患数量"></wd-table-col>
        <wd-table-col width="80" prop="rate" label="占比">
          <template #value="{ row }">
            <text>{{ row.rate }}%</text>
          </template>
        </wd-table-col>
      </wd-table>

      <Empty v-else />
    </view>
  </view>
</template>
<script lang="ts" setup>
import { ref } from 'vue'
import arrowIcon from '@/pages/dataanalysis/assets/title_arrow.png'
import { FilterService } from '../HighHidenFilter/filterService'
import { getHidenTop5Data as getData } from './featch'
import { IHidenDesData } from './type'
import { useUserStore } from '@/store'
import Empty from '@/components/empty/index.vue'

defineOptions({ name: 'HighHidenTable' })

const { userInfo } = useUserStore()

const toPage = () => {
  uni.navigateTo({
    url: '/pages/dangerlist/index',
  })
}

const dataList = ref<IHidenDesData>([])

const initData = (filter) => {
  getData({
    starTime: filter.startTime,
    endTime: filter.endTime,
    unitId: userInfo.unitId,
  }).then((res) => {
    const { code, data } = res
    if (code === 'success') {
      dataList.value = data
    }
  })
}

onMounted(() => {
  watch(
    () => FilterService.filterFrom.value,
    (value) => {
      initData(value)
    },
    { immediate: true, deep: true },
  )
})
</script>

<style lang="scss" scoped>
.header {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.title {
  flex: 1;
  font-size: 28rpx;
  color: #000;
  line-height: 1.25em;
  margin-bottom: 20rpx;
  .title-text {
    margin-left: 24rpx;
  }
}

.table-box {
  .top-col {
    width: 40rpx;
    height: 40rpx;
    display: flex;
    align-items: center;
    justify-content: center;

    &.top-col_1 {
      color: #d42121;
      background: url('@/pages/dataanalysis/assets/icon_top_1.png') 0 0 no-repeat;
      background-size: 100% 100%;
    }
    &.top-col_2 {
      color: #c24400;
      background: url('@/pages/dataanalysis/assets/icon_top_2.png') 0 0 no-repeat;
      background-size: 100% 100%;
    }
    &.top-col_3 {
      color: #cd6402;
      background: url('@/pages/dataanalysis/assets/icon_top_3.png') 0 0 no-repeat;
      background-size: 100% 100%;
    }
  }
}
</style>
