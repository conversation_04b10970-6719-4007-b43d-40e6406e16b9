import { httpGet } from '@/utils/http'
import { $api } from '@/api'
import { IHidenType, IHidenCheckItem, IHidenDesData } from './type'

export const getHidenTypeData = (query) => {
  const url = $api.type.apiHazard + $api.name.apiHazard.highHarazdAnalysis
  return httpGet<IHidenType>(url, query)
}

export const getHidenCheckItemData = (query) => {
  const url = $api.type.apiHazard + $api.name.apiHazard.highCheckAnalysisTop10
  return httpGet<IHidenCheckItem>(url, query)
}

export const getHidenTop5Data = (query) => {
  const url = $api.type.apiHazard + $api.name.apiHazard.highTop5
  return httpGet<IHidenDesData>(url, query)
}
