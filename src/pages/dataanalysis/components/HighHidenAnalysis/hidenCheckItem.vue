<template>
  <view>
    <view class="header">
      <view class="title">
        <wd-icon size="28rpx" :name="arrowIcon" />
        <text class="title-text">高频隐患检查项TOP10</text>
      </view>
      <wd-button type="text" @click="toPage">更多 ></wd-button>
    </view>

    <BarChart :chart-data="chartData" :graphic-color="graphicColor" />
  </view>
</template>
<script lang="ts" setup>
import { ref } from 'vue'
import BarChart from '@/components/chart/barChart.vue'
import arrowIcon from '@/pages/dataanalysis/assets/title_arrow.png'
import { FilterService } from '../HighHidenFilter/filterService'
import { getHidenCheckItemData as getData } from './featch'
import { useUserStore } from '@/store'

defineOptions({ name: 'HighHidenCheckItem' })

const { userInfo } = useUserStore()

const toPage = () => {
  uni.navigateTo({
    url: '/pages/dangerlist/index',
  })
}

const chartData = ref({
  xData: [],
  yData: [],
})
const graphicColor = [['#1CE598', '#02BF7F']]

const initData = (filter) => {
  getData({
    starTime: filter.startTime,
    endTime: filter.endTime,
    unitId: userInfo.unitId,
  }).then((res) => {
    const { code, data } = res
    if (code === 'success') {
      chartData.value = {
        xData: data.dataX,
        yData: data.dataY,
      }
    }
  })
}

onMounted(() => {
  watch(
    () => FilterService.filterFrom.value,
    (value) => {
      initData(value)
    },
    { immediate: true, deep: true },
  )
})
</script>

<style lang="scss" scoped>
.header {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.title {
  flex: 1;
  font-size: 28rpx;
  color: #000;
  line-height: 1.25em;
  .title-text {
    margin-left: 24rpx;
  }
}
</style>
