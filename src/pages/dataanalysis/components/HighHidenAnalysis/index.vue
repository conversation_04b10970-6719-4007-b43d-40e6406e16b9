<template>
  <card title="高频隐患数据分析" :icon="gpyh">
    <!-- <SpeCard class="mt-40" :content="content" /> -->
    <HidenType class="mt-40" />
    <HighCheckItem class="mt-40" />
    <HidenTable class="mt-40" />
  </card>
</template>
<script lang="ts" setup>
import card from '@/components/commonCard/ComCard.vue'
// import SpeCard from '@/components/commonCard/SpecificationCard.vue'
import gpyh from '@/pages/dataanalysis/assets/icon_gpyh.png'

import HidenType from './hidenType.vue'
import HighCheckItem from './hidenCheckItem.vue'
import HidenTable from './hidenTable.vue'

defineOptions({ name: 'HighHidenAnalysis' })
</script>

<style lang="scss" scoped>
.mt-40 {
  margin-top: 40rpx;
}
</style>
