<template>
  <view>
    <view class="header">
      <view class="title">
        <wd-icon size="28rpx" :name="arrowIcon" />
        <text class="title-text">高频隐患分类</text>
      </view>
      <wd-button type="text" @click="toPage">更多 ></wd-button>
    </view>

    <PieChart
      :height="700"
      :chart-data="chartData"
      :extend-option="{
        grid: {
          left: '3%',
          right: '3%',
          bottom: '50%',
          containLabel: true,
        },
      }"
    />
  </view>
</template>
<script lang="ts" setup>
import { ref } from 'vue'
import PieChart from '@/components/chart/pieChart.vue'
import arrowIcon from '@/pages/dataanalysis/assets/title_arrow.png'
import { FilterService } from '../HighHidenFilter/filterService'
import { getHidenTypeData as getData } from './featch'
import { useUserStore } from '@/store'

defineOptions({ name: 'HighHidenType' })

const { userInfo } = useUserStore()

const toPage = () => {
  uni.navigateTo({
    url: `/pages/statisticalReport/index?tab=1&subTab=1&type=${FilterService.filterFrom.type}`,
  })
}

const chartData = ref([])

const initData = (filter) => {
  getData({
    starTime: filter.startTime,
    endTime: filter.endTime,
    unitId: userInfo.unitId,
  }).then((res) => {
    const { code, data } = res
    if (code === 'success') {
      chartData.value = data.highHarazdList.map((item) => ({
        name: item.className,
        value: item.total,
      }))
    }
  })
}

onMounted(() => {
  watch(
    () => FilterService.filterFrom.value,
    (value) => {
      initData(value)
    },
    { immediate: true, deep: true },
  )
})
</script>

<style lang="scss" scoped>
.header {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.title {
  flex: 1;
  font-size: 28rpx;
  color: #000;
  line-height: 1.25em;
  .title-text {
    margin-left: 24rpx;
  }
}
</style>
