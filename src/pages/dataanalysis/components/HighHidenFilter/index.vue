<!--
 * @Author: xginger <EMAIL>
 * @Date: 2025-06-08 18:25:10
 * @LastEditors: xginger <EMAIL>
 * @LastEditTime: 2025-06-09 20:30:53
 * @FilePath: \hazard-mgr\src\pages\dataanalysis\components\HighHidenFilter\index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <view class="high-hiden-header">
    <view class="high-hiden-top">
      <text class="title">隐患排查分析</text>
      <view class="search-box">
        <text
          :class="[filterType === 'custom' ? 'active' : '', 'customBtn']"
          @click="filterTypeHandle('custom')"
        >
          自定义
        </text>
        <text
          :class="[filterType === 'short' ? 'active' : '', 'shortBtn']"
          @click="filterTypeHandle('short')"
        >
          {{ curShortData.label }}
        </text>
      </view>
    </view>
    <view class="time">统计周期：{{ dateRangeStr }}</view>

    <wd-popup
      v-model="calendarPopup"
      position="bottom"
      :safe-area-inset-bottom="true"
      @close="calendarPopup = false"
    >
      <view class="pop-title">日期范围选择</view>
      <wd-calendar-view
        type="daterange"
        :max-range="365"
        :min-date="mindate"
        :max-date="new Date().getTime()"
        v-model="customDate"
      />
      <view class="popbom">
        <view
          class="pop-button"
          style="border-top: 1px solid #ccc; color: #333"
          @click="calendarClose"
        >
          取消
        </view>
        <view
          class="pop-button"
          style="background-color: #0256ff; border-top: 1px solid #0256ff; color: white"
          @click="calendarComfirm"
        >
          确认
        </view>
      </view>
    </wd-popup>

    <wd-popup
      v-model="shortPopup"
      position="bottom"
      :safe-area-inset-bottom="true"
      @close="shortPopup = false"
      custom-style="height: 30vh;"
    >
      <view class="pop-title">
        日期类型选择
        <wd-icon class="pop-cancel" name="close" size="16px" @click="shortPopup = false"></wd-icon>
      </view>
      <wd-radio-group v-model="curShort" @change="shortHandle" cell>
        <wd-radio v-for="itme in shortList" :key="itme.value" :value="itme.value">
          {{ itme.label }}
        </wd-radio>
      </wd-radio-group>
    </wd-popup>
  </view>
</template>
<script lang="ts" setup>
import { getDateRange, dateToTimestamp, timestampToDate } from '@/utils/index'
import { FilterService } from './filterService'

defineOptions({ name: 'HighHidenFilter' })

const dateRangeStr = ref('')

const filterType = ref('short')
const filterTypeHandle = (type: 'short' | 'custom') => {
  filterType.value = type
  if (type === 'custom') {
    calendarPopup.value = true
  }
  if (type === 'short') {
    shortPopup.value = true
  }
}

// calendat -----------
const calendarPopup = ref(false)
const customDate = ref([])
// 最小选择时间设置为 当前时间往前推12个月
const mindate = computed(() => {
  const [minDate] = getDateRange(2, 'year')
  return dateToTimestamp(minDate)
})
// 关闭选择时间
function calendarClose() {
  customDate.value = []
  calendarPopup.value = false
}
function calendarComfirm() {
  if (!customDate.value[0]) {
    uni.showToast({
      title: '请选择开始时间',
      icon: 'none',
    })
    return
  }
  if (!customDate.value[1]) {
    uni.showToast({
      title: '结束时间不能为空',
      icon: 'none',
    })
    return
  }
  const startTime = timestampToDate(customDate.value[0])
  const endTime = timestampToDate(customDate.value[1])
  dateRangeStr.value = `${startTime}~${endTime}`
  FilterService.filterFrom.value = {
    dateType: '',
    startTime: `${startTime} 00:00:00`,
    endTime: `${endTime} 23:59:59`,
  }
  calendarPopup.value = false
}

// short
const shortPopup = ref(false)
const curShort = ref(7)
const shortList = [
  {
    label: '今日',
    value: 1,
    shortValue: '1',
  },
  {
    label: '昨日',
    value: 2,
    shortValue: '2',
  },
  {
    label: '近1周',
    value: 7,
    shortValue: '3',
  },
  {
    label: '近1个月',
    value: 30,
    shortValue: '4',
  },
  {
    label: '近3个月',
    value: 90,
    shortValue: '5',
  },
]
const curShortData = computed(() => {
  const _filter = shortList.filter((item) => item.value === curShort.value)
  return _filter[0]
})

const shortHandle = ({ value }) => {
  curShort.value = value
  FilterService.filterFrom.type = value
  const [startTime, endTime] = getDateRange(value, 'day')
  const isOneDay = value === 1 || value === 2
  dateRangeStr.value = isOneDay ? `${startTime}` : `${startTime}~${endTime}`
  FilterService.filterFrom.value = {
    dateType: curShortData.value.shortValue,
    startTime: isOneDay ? `${startTime} 00:00:00` : `${startTime} 00:00:00`,
    endTime: isOneDay ? `${startTime} 23:59:59` : `${endTime} 23:59:59`,
  }

  shortPopup.value = false
}

shortHandle({ value: 7 })
</script>

<style lang="scss" scoped>
.high-hiden-header {
  margin-top: 60rpx;
  margin-bottom: 20rpx;

  .high-hiden-top {
    display: flex;
    flex-flow: row nowrap;
    align-items: center;
    justify-content: space-between;
    .title {
      flex: 1;
      font-size: 36rpx;
      font-weight: 700;
      line-height: 48rpx;
    }
    .search-box {
      display: flex;
      flex-flow: row nowrap;
      align-items: center;
      gap: 0;
      border: 1px solid #ebebeb;
      border-radius: 32rpx;
      background-color: #fff;
      font-size: 28rpx;
      line-height: 28rpx;
      overflow: hidden;
      .customBtn {
        padding: 16rpx 32rpx;
      }
      .shortBtn {
        padding: 16rpx 32rpx;
      }
      .active {
        background-color: rgba(2, 86, 255, 0.1) !important;
        color: #0256ff;
      }
    }
  }
  .time {
    font-size: 24rpx;
    color: #0256ff;
    margin-top: 10rpx;
  }
}

.pop-title {
  position: sticky;
  width: 100%;
  margin: 20px auto 10px;
  font-size: 16px;
  color: #000000;
  text-align: center;
  font-weight: bold;

  .pop-cancel {
    position: absolute;
    top: 0px;
    right: 20px;
    color: rgb(175, 175, 175);
  }
}

.popbom {
  display: flex;
  justify-content: space-around;
  align-items: center;
  z-index: 99;
  margin: 10px auto;
  margin-bottom: 0px;

  .pop-button {
    width: 50%;
    text-align: center;
    padding: 8px;
  }
}
</style>
