<template>
  <card title="单位发生隐患TOP5" :icon="rwpm">
    <view class="table-box">
      <wd-table v-if="dataList.length > 0" :data="dataList">
        <wd-table-col width="20%" align="center" prop="index" label="TOP" fixed>
          <template #value="{ index }">
            <view :class="['top-col', `top-col_${index + 1}`]">
              <text>{{ index + 1 }}</text>
            </view>
          </template>
        </wd-table-col>
        <wd-table-col width="40%" prop="orgName" label="一级经营单位"></wd-table-col>
        <wd-table-col width="40%" prop="total" label="隐患数量"></wd-table-col>
      </wd-table>

      <Empty v-else />
    </view>
  </card>
</template>
<script lang="ts" setup>
import { ref } from 'vue'
import card from '@/components/commonCard/ComCard.vue'
import rwpm from '@/pages/dataanalysis/assets/icon_rwpm.png'
import { FilterService } from '../HighHidenFilter/filterService'
import { getData } from './featch'
import { useUserStore } from '@/store'
import Empty from '@/components/empty/index.vue'

defineOptions({ name: 'HidenTopComp' })

const { userInfo } = useUserStore()

const dataList = ref([])

const initData = (filter) => {
  getData({
    starTime: filter.startTime,
    endTime: filter.endTime,
    unitId: userInfo.unitId,
  }).then((res) => {
    const { code, data } = res
    if (code === 'success') {
      dataList.value = data
    }
  })
}

onMounted(() => {
  watch(
    () => FilterService.filterFrom.value,
    (value) => {
      initData(value)
    },
    { immediate: true, deep: true },
  )
})
</script>

<style lang="scss" scoped>
.table-box {
  margin-top: 20rpx;
  .top-col {
    width: 40rpx;
    height: 40rpx;
    display: flex;
    align-items: center;
    justify-content: center;

    &.top-col_1 {
      color: #d42121;
      background: url('@/pages/dataanalysis/assets/icon_top_1.png') 0 0 no-repeat;
      background-size: 100% 100%;
    }
    &.top-col_2 {
      color: #c24400;
      background: url('@/pages/dataanalysis/assets/icon_top_2.png') 0 0 no-repeat;
      background-size: 100% 100%;
    }
    &.top-col_3 {
      color: #cd6402;
      background: url('@/pages/dataanalysis/assets/icon_top_3.png') 0 0 no-repeat;
      background-size: 100% 100%;
    }
  }
}
</style>
