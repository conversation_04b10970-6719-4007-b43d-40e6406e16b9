<template>
  <BarChart :chart-data="chartData" :graphic-color="graphicColor" />
</template>
<script lang="ts" setup>
import BarChart from '@/components/chart/barChart.vue'
import { FilterService } from '../HighHidenFilter/filterService'
import { getHidenCycleData as getData } from './featch'
import { useUserStore } from '@/store'

defineOptions({ name: 'HidenCycle' })

const { userInfo } = useUserStore()

const chartData = ref({
  xData: [],
  yData: [],
})
const graphicColor = [['#1CE598', '#02BF7F']]

const initData = (filter) => {
  getData({
    starTime: filter.startTime,
    endTime: filter.endTime,
    unitId: userInfo.unitId,
  }).then((res) => {
    const { code, data } = res
    if (code === 'success') {
      chartData.value = {
        xData: data.dataX,
        yData: data.dataY1,
      }
    }
  })
}

onMounted(() => {
  watch(
    () => FilterService.filterFrom.value,
    (value) => {
      initData(value)
    },
    { immediate: true, deep: true },
  )
})
</script>

<style lang="scss" scoped></style>
