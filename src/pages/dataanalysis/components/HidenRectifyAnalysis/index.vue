<template>
  <card title="隐患整改数据分析" :icon="gpyh">
    <template v-slot:headerBtn>
      <wd-button type="text" @click="toPage">更多 ></wd-button>
    </template>

    <tabComp class="tab-box" v-model:value="curTab" :tabs="tabList" />

    <keep-alive>
      <component :is="curComp" />
    </keep-alive>
  </card>
</template>
<script lang="ts" setup>
import { ref } from 'vue'
import card from '@/components/commonCard/ComCard.vue'
import tabComp from '@/components/commonTab/tab3.vue'
import gpyh from '@/pages/dataanalysis/assets/icon_gpyh.png'

import HidenCount from './hidenCount.vue'
import HidenRate from './hidenRate.vue'
import HidenCycle from './hidenCycle.vue'

defineOptions({ name: 'HidenRectifyAnalysis' })

const toPage = () => {
  uni.navigateTo({
    url: '/pages/dangerlist/index',
  })
}

const tabList = ref([
  {
    label: '隐患上报数量',
    value: '1',
    compName: shallowRef(HidenCount),
  },
  {
    label: '隐患整改完成率',
    value: '2',
    compName: shallowRef(HidenRate),
  },
  {
    label: '隐患整改周期',
    value: '3',
    compName: shallowRef(HidenCycle),
  },
])
const curTab = ref('1')
const curComp = computed(() => {
  const tab = tabList.value.filter((item) => item.value === curTab.value)
  return tab[0].compName
})

// const compRef = ref()
// watch(
//   () => curTab.value,
//   (val) => {
//     // compRef.value?.reloadData()
//     // if (val === '1') compRef.value?.reloadData()
//     // if (val === '2') compRef.value?.reloadData()
//     // if (val === '3') compRef.value?.reloadData()
//   },
// )
</script>

<style lang="scss" scoped>
.tab-box {
  margin-top: 20rpx;
  margin-bottom: 10rpx;
}

.chart-box {
  width: 100%;
  height: 700rpx;
  background: red;
}

.mt-40 {
  margin-top: 40rpx;
}
</style>
