<template>
  <LineChart :chart-data="chartData" />
</template>
<script lang="ts" setup>
import LineChart from '@/components/chart/lineChart.vue'
import { FilterService } from '../HighHidenFilter/filterService'
import { getHidenCountData as getData } from './featch'
import { useUserStore } from '@/store'

defineOptions({ name: 'HidenCount' })

const { userInfo } = useUserStore()

const chartData = ref({
  xData: [],
  yData: [],
})
const graphicColor = [
  ['#FF7272', '#ED0D0D'],
  ['#FF9C6A', '#E74D00'],
  ['#FFC44D', '#DC8B00'],
]

const initData = (filter) => {
  getData({
    dateType: filter.dateType,
    starTime: filter.startTime,
    endTime: filter.endTime,
    unitId: userInfo.unitId,
  }).then((res) => {
    const { code, data } = res
    if (code === 'success') {
      const yData = data.hazardNumberVoList.map((item) => ({
        name: item.hazardLevelName,
        data: item.dataLists,
      }))
      chartData.value = {
        xData: data.dataX,
        yData,
      }
    }
  })
}

onMounted(() => {
  watch(
    () => FilterService.filterFrom.value,
    (value) => {
      initData(value)
    },
    { immediate: true, deep: true },
  )
})

const reloadData = () => {
  initData(FilterService.filterFrom.value)
}

defineExpose({
  reloadData,
})
</script>

<style lang="scss" scoped></style>
