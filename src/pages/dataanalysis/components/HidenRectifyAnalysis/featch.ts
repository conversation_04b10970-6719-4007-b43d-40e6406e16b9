import { httpGet } from '@/utils/http'
import { $api } from '@/api'
import { ICountData, ICycleData, IRateData } from './type'

export const getHidenCountData = (query) => {
  const url = $api.type.apiHazard + $api.name.apiHazard.hazardNumberAnalysis
  return httpGet<ICountData>(url, query)
}

export const getHidenRateData = (query) => {
  const url = $api.type.apiHazard + $api.name.apiHazard.hazardDisposeAnalysis
  return httpGet<IRateData>(url, query)
}

export const getHidenCycleData = (query) => {
  const url = $api.type.apiHazard + $api.name.apiHazard.hazardDisposeTimeAnalysis
  return httpGet<ICycleData>(url, query)
}
