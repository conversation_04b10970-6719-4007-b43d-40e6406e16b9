<template>
  <BarLineChart :chart-data="chartData" />
</template>
<script lang="ts" setup>
import BarLine<PERSON>hart from '@/components/chart/barLineChart.vue'
import { FilterService } from '../HighHidenFilter/filterService'
import { getTaskRateData as getData } from './featch'
import { useUserStore } from '@/store'

defineOptions({ name: 'TaskRate' })

const { userInfo } = useUserStore()

const chartData = ref({
  xData: [],
  yData: [],
})

const initData = (filter) => {
  getData({
    dateType: filter.dateType,
    starTime: filter.startTime,
    endTime: filter.endTime,
    unitId: userInfo.unitId,
  }).then((res) => {
    const { code, data } = res
    if (code === 'success') {
      chartData.value = {
        xData: data.dataX,
        yData: [
          {
            name: '检查完成数',
            type: 'bar',
            data: data.dataY1,
          },
          {
            name: '检查任务总数',
            type: 'bar',
            data: data.dataY2,
          },
          {
            name: '任务完成率',
            type: 'line',
            data: data.dataY3,
          },
        ],
      }
    }
  })
}

onMounted(() => {
  watch(
    () => FilterService.filterFrom.value,
    (value) => {
      initData(value)
    },
    { immediate: true, deep: true },
  )
})
</script>

<style lang="scss" scoped></style>
