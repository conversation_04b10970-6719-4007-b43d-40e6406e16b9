<template>
  <LineChart :chart-data="chartData" />
</template>
<script lang="ts" setup>
import LineChart from '@/components/chart/lineChart.vue'
import { FilterService } from '../HighHidenFilter/filterService'
import { getTaskTrendData as getData } from './featch'
import { useUserStore } from '@/store'

defineOptions({ name: 'TaskTrend' })

const { userInfo } = useUserStore()

const chartData = ref({
  xData: [],
  yData: [],
})

const initData = (filter) => {
  getData({
    starTime: filter.startTime,
    endTime: filter.endTime,
    unitId: userInfo.unitId,
  }).then((res) => {
    const { code, data } = res
    if (code === 'success') {
      const yData = data.taskUnitVoList.map((item) => ({
        name: item.itemName,
        data: item.dataLists,
      }))

      chartData.value = {
        xData: data.dataX,
        yData,
      }
    }
  })
}

onMounted(() => {
  watch(
    () => FilterService.filterFrom.value,
    (value) => {
      initData(value)
    },
    { immediate: true, deep: true },
  )
})
</script>

<style lang="scss" scoped></style>
