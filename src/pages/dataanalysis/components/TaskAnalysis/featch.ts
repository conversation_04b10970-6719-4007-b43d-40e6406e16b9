import { httpGet } from '@/utils/http'
import { $api } from '@/api'
import { IProgressData, IRateData, ITrendData } from './type'

export const getTaskProgressData = (query) => {
  const url = $api.type.apiHazard + $api.name.apiHazard.taskProgessAnalysis
  return httpGet<IProgressData>(url, query)
}

export const getTaskRateData = (query) => {
  const url = $api.type.apiHazard + $api.name.apiHazard.taskCompleteAnalysis
  return httpGet<IRateData>(url, query)
}

export const getTaskTrendData = (query) => {
  const url = $api.type.apiHazard + $api.name.apiHazard.taskTrendAnalysis
  return httpGet<ITrendData>(url, query)
}
