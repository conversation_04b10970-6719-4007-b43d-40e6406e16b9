<template>
  <StackBarChart :chart-data="chartData" :bar-color="barColor" />
</template>
<script lang="ts" setup>
import StackBarChart from '@/components/chart/stackBarChart.vue'
import { FilterService } from '../HighHidenFilter/filterService'
import { getTaskProgressData as getData } from './featch'
import { useUserStore } from '@/store'

defineOptions({ name: 'HidenCount' })

const { userInfo } = useUserStore()

const chartData = ref({
  category: [],
  yData: [],
})
const barColor = ref(['#FD2727', '#11D0CB', '#0080FF', '#FD9905', '#11D0CB'])

const initData = (filter) => {
  getData({
    starTime: filter.startTime,
    endTime: filter.endTime,
    unitId: userInfo.unitId,
  }).then((res) => {
    const { code, data } = res
    if (code === 'success') {
      chartData.value = {
        category: ['任务完成总进度', '任务逾期完成进度'],
        yData: [
          {
            name: '逾期已完成数',
            data: [data.yqwcs, 0],
          },
          {
            name: '逾期任务总数',
            data: [data.yqrwzs, 0],
          },
          {
            name: '进行中',
            data: [0, data.jxzzs],
          },
          {
            name: '待开始',
            data: [0, data.dkszs],
          },
          {
            name: '已完成',
            data: [0, data.ywczs],
          },
        ],
      }
    }
  })
}

onMounted(() => {
  watch(
    () => FilterService.filterFrom.value,
    (value) => {
      initData(value)
    },
    { immediate: true, deep: true },
  )
})
</script>

<style lang="scss" scoped></style>
