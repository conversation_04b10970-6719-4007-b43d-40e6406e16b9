<template>
  <card title="排查任务执行分析" :icon="gpyh">
    <template v-slot:headerBtn>
      <wd-button type="text" @click="toPage">更多 ></wd-button>
    </template>

    <tabComp class="tab-box" v-model:value="curTab" :tabs="tabList" />

    <keep-alive>
      <component :is="curComp" />
    </keep-alive>
  </card>
</template>
<script lang="ts" setup>
import { ref } from 'vue'
import card from '@/components/commonCard/ComCard.vue'
import tabComp from '@/components/commonTab/tab3.vue'
import gpyh from '@/pages/dataanalysis/assets/icon_gpyh.png'

import TaskProgress from './taskProgress.vue'
import TaskRate from './taskRate.vue'
import TaskTrend from './taskTrend.vue'

defineOptions({ name: 'TaskAnalysis' })

const toPage = () => {
  uni.navigateTo({
    url: '/pages/task/index?tab=1',
  })
}

const tabList = ref([
  {
    label: '任务进度分析',
    value: '1',
    compName: shallowRef(TaskProgress),
  },
  {
    label: '任务完成率分析',
    value: '2',
    compName: shallowRef(TaskRate),
  },
  {
    label: '任务趋势分析',
    value: '3',
    compName: shallowRef(TaskTrend),
  },
])
const curTab = ref('1')

const curComp = computed(() => {
  const tab = tabList.value.filter((item) => item.value === curTab.value)
  return tab[0].compName
})
</script>

<style lang="scss" scoped>
.tab-box {
  margin-top: 20rpx;
  margin-bottom: 10rpx;
}

.chart-box {
  width: 100%;
  height: 700rpx;
  background: red;
}

.mt-40 {
  margin-top: 40rpx;
}
</style>
