<template>
  <card title="近1年隐患整改情况" :icon="yhzg" :bg-img="bg">
    <view class="hiden-bar">
      <view
        v-for="item in barList"
        :key="item.id"
        :class="['bar-item', item.id === '3' ? 'bar-item_wzg' : '']"
      >
        <wd-icon class="bar-icon" size="48rpx" :name="item.icon" />
        <text class="bar-value">{{ item.value }}</text>
        <view class="bar-name">{{ item.name }}</view>
      </view>
    </view>

    <view class="title">
      <wd-icon size="28rpx" :name="arrowIcon" />
      <text class="title-text">隐患等级分布</text>
    </view>

    <tabComp class="tab-box" v-model:value="curTab" :tabs="tabList" />
    <BarChart :chart-data="chartData" :graphic-color="graphicColor" />
  </card>
</template>
<script lang="ts" setup>
import { ref } from 'vue'
import card from '@/components/commonCard/ComCard.vue'
import tabComp from '@/components/commonTab/tab1.vue'
import BarChart from '@/components/chart/barChart.vue'

import yhzg from '@/pages/dataanalysis/assets/icon_yhzg.png'
import yzg from '@/pages/dataanalysis/assets/icon_yzg.png'
import dzg from '@/pages/dataanalysis/assets/icon_dzg.png'
import wzg from '@/pages/dataanalysis/assets/icon_wzg.png'
import arrowIcon from '@/pages/dataanalysis/assets/title_arrow.png'
import bg from '@/components/commonCard/assets/bg.png'
import { getData } from './featch'
import { IData } from './type'
import { useUserStore } from '@/store'

defineOptions({ name: 'HidenRectify' })

const { userInfo } = useUserStore()

const barList = ref([
  {
    id: '1',
    name: '已整改隐患',
    value: 0,
    icon: yzg,
  },
  {
    id: '2',
    name: '待整改隐患',
    value: 0,
    icon: dzg,
  },
  {
    id: '3',
    name: '逾期未整改',
    value: 0,
    icon: wzg,
  },
])

const tabList = ref([
  {
    label: '待整改',
    value: '0',
  },
  {
    label: '已整改',
    value: '1',
  },
  {
    label: '全部',
    value: '2',
  },
])
const curTab = ref('0')

const chartData = ref({
  xData: [],
  yData: [],
})
const graphicColor = [
  ['#FF7272', '#ED0D0D'],
  ['#FF9C6A', '#E74D00'],
  ['#FFC44D', '#DC8B00'],
]

const initData = () => {
  getData({
    disposeState: curTab.value === '2' ? '' : curTab.value,
    unitId: userInfo.unitId,
  }).then((res) => {
    const { code, data } = res
    if (code === 'success') {
      const { disposedNum, timeoutNum, unDisposedNum, dataX, dataY } = data

      barList.value[0].value = disposedNum
      barList.value[1].value = unDisposedNum
      barList.value[2].value = timeoutNum

      chartData.value = {
        xData: dataX,
        yData: dataY,
      }
    }
  })
}

watch(
  () => curTab.value,
  () => {
    initData()
  },
)

onMounted(() => {
  initData()
})
</script>

<style lang="scss" scoped>
.hiden-bar {
  display: flex;
  flex-flow: row nowrap;
  justify-content: space-between;
  margin-top: 20rpx;
  margin-bottom: 40rpx;
}

.bar-item {
  padding: 12rpx 16rpx;
  background: #f0f5fa;
  color: #0080ff;
  border-radius: 8rpx;
  min-width: 196rpx;

  display: grid;
  grid-template-areas:
    'icon value'
    'name name';
  grid-template-columns: 48rpx 1fr;
  gap: 10rpx;

  &.bar-item_wzg {
    background: #f7f3ee;
    color: #ff8a44;
  }

  .bar-icon {
    grid-area: icon;
    display: flex;
    align-items: center;
  }
  .bar-value {
    grid-area: value;
    display: flex;
    align-items: center;
    font-size: 40rpx;
    line-height: 1.25em;
  }
  .bar-name {
    grid-area: name;
    display: flex;
    align-items: center;
    font-size: 26rpx;
    line-height: 1.25em;
  }
}

.title {
  font-size: 28rpx;
  color: #000;
  line-height: 1.25em;
  margin-bottom: 20rpx;
  .title-text {
    margin-left: 24rpx;
  }
}

.tab-box {
  margin-bottom: 10rpx;
}
</style>
