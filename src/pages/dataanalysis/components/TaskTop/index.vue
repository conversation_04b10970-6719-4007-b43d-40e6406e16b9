<template>
  <card title="近30天排查任务TOP5" :icon="rwpm">
    <template v-slot:headerBtn>
      <wd-button type="text" @click="toPage">更多 ></wd-button>
    </template>

    <view class="tab-box" v-if="dataList.length > 0">
      <TabComp class="tab-comp" v-model:value="curTab" :tabs="tabList" />
    </view>

    <view class="table-box">
      <wd-table :data="dataList" v-if="dataList.length > 0">
        <wd-table-col width="50" align="center" prop="index" label="TOP" fixed>
          <template #value="{ index }">
            <view :class="['top-col', `top-col_${index + 1}`]">
              <text>{{ index + 1 }}</text>
            </view>
          </template>
        </wd-table-col>
        <wd-table-col width="29%" prop="unitName" label="单位名称"></wd-table-col>
        <wd-table-col width="110" prop="rwzs" label="单位任务数"></wd-table-col>
        <wd-table-col width="110" prop="rcwcl" label="任务完成率">
          <template #value="{ row }">
            <text>{{ row.rcwcl }}%</text>
          </template>
        </wd-table-col>
      </wd-table>

      <Empty v-else />
    </view>
  </card>
</template>
<script lang="ts" setup>
import { ref } from 'vue'
import card from '@/components/commonCard/ComCard.vue'
import rwpm from '@/pages/dataanalysis/assets/icon_rwpm.png'
import TabComp from '@/components/commonTab/tab2.vue'
import { getData } from './featch'
import { IData } from './type'
import { useUserStore } from '@/store'
import Empty from '@/components/empty/index.vue'

defineOptions({ name: 'TaskTopComp' })

const { userInfo } = useUserStore()

const toPage = () => {
  uni.navigateTo({
    url: '/pages/task/index?tab=1',
  })
}

const tabList = ref([
  {
    label: '前五名',
    value: '1',
  },
  {
    label: '后五名',
    value: '2',
  },
])
const curTab = ref('1')

const dataList = ref<IData>([])

const initData = () => {
  getData({
    sort: curTab.value,
    unitId: userInfo.unitId,
  }).then((res) => {
    const { code, data } = res
    if (code === 'success') {
      dataList.value = data
    }
  })
}

watch(
  () => curTab.value,
  () => {
    initData()
  },
)

onMounted(() => {
  initData()
})
</script>

<style lang="scss" scoped>
.tab-box {
  margin-top: 24rpx;
  margin-bottom: 32rpx;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  .tab-comp {
    // width: 240rpx;
  }
}

.table-box {
  .top-col {
    width: 40rpx;
    height: 40rpx;
    display: flex;
    align-items: center;
    justify-content: center;

    &.top-col_1 {
      color: #d42121;
      background: url('@/pages/dataanalysis/assets/icon_top_1.png') 0 0 no-repeat;
      background-size: 100% 100%;
    }
    &.top-col_2 {
      color: #c24400;
      background: url('@/pages/dataanalysis/assets/icon_top_2.png') 0 0 no-repeat;
      background-size: 100% 100%;
    }
    &.top-col_3 {
      color: #cd6402;
      background: url('@/pages/dataanalysis/assets/icon_top_3.png') 0 0 no-repeat;
      background-size: 100% 100%;
    }
  }
}
</style>
