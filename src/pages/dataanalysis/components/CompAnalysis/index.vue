<template>
  <card title="综合分析" :icon="zhfx">
    <view class="chartBox"></view>
    <RadarChart :height="700" :chart-data="chartData" compare-mark="集团环比均值" />
    <!-- <SpeCard title="数据解读" :content="content" /> -->
  </card>
</template>
<script lang="ts" setup>
import { ref } from 'vue'
import card from '@/components/commonCard/ComCard.vue'
import RadarChart from '@/components/chart/radarChart.vue'
// import SpeCard from '@/components/commonCard/SpecificationCard.vue'
import zhfx from '@/pages/dataanalysis/assets/icon_zhfx.png'
import { useUserStore } from '@/store'
import { getData } from './featch'
import { IData } from './type'

defineOptions({ name: 'ComprehensiveAnalysis' })

const { userInfo } = useUserStore()

const chartData = ref<IData>()

const initData = () => {
  getData(userInfo.unitId).then((res) => {
    const { code, data } = res
    if (code === 'success') {
      chartData.value = data
    }
  })
}

onMounted(() => {
  initData()
})

// const content = ref([
//   '排查任务较上周期减少38%，原因为外运合肥、外运天竺排查计划未执行。',
//   '整改周期增加21%，原因为外运黄埔、外运马驹桥整改复查周期较长。',
//   '整改完成率较同期增加15%，原因为外运空运、外运合肥隐患整改率较低。',
// ])
</script>

<style lang="scss" scoped></style>
