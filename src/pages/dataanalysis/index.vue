<route lang="json5">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '数据分析',
  },
}
</route>
<template>
  <SafetyNavbar class="navbar" title="数据分析"></SafetyNavbar>
  <view class="overflow-scroll px-4 full-screen-bg">
    <!-- 综合分析 -->
    <ComprehensiveAnalysis />
    <!-- 近1年隐患整改情况 -->
    <HidenRectify />
    <!-- 近30天排查任务TOP5 -->
    <TaskTop />

    <HighHidenFilter />
    <!-- 高频隐患数据分析 -->
    <HighHidenAnalysis />
    <!-- 隐患整改数据分析 -->
    <HidenRectifyAnalysis />
    <!-- 排查任务执行分析 -->
    <TaskAnalysis />
    <!-- 单位发生隐患TOP5 -->
    <HidenTop class="mb-60" />
  </view>
</template>

<script setup lang="ts">
import SafetyNavbar from '../components/safety-navbar.vue'
import ComprehensiveAnalysis from './components/CompAnalysis/index.vue'
import HidenRectify from './components/HidenRectify/index.vue'
import TaskTop from './components/TaskTop/index.vue'
import HighHidenAnalysis from './components/HighHidenAnalysis/index.vue'
import HidenRectifyAnalysis from './components/HidenRectifyAnalysis/index.vue'
import TaskAnalysis from './components/TaskAnalysis/index.vue'
import HidenTop from './components/HidenTop/index.vue'
import HighHidenFilter from './components/HighHidenFilter/index.vue'

// import { useUserStore } from '@/store'
// const userStore = useUserStore()
// const userinfo: any = userStore.userInfo ? userStore.userInfo : {}

defineOptions({ name: 'DataAnalysisIndex' })
</script>
<style lang="scss" scoped>
.mb-60 {
  margin-bottom: 60rpx;
}
</style>
