import { $api } from '@/api'
import { http } from '@/utils/http'
// /eBuilding/getBuildingTreeByUnitId
/* 获取楼栋楼层列表 /eBuilding/getUnitBuildingInfoList */
export const postqueryErecordBuildingFloorListAPI = (parameters: any) => {
  return http.post(
    $api.type.hazard +
      '/eBuilding/queryErecordBuildingFloorList?buildingId=' +
      parameters.buildingId +
      '&floorId=' +
      parameters.floorId +
      '&unitId=' +
      parameters.unitId,
  )
}
