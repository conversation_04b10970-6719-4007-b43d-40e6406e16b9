<route lang="json5">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '选点',
  },
}
</route>
<template>
  <!-- :rightsaveBtnTxt="true" @click-right="handleClickRight" -->
  <SafetyNavbar title="选点"></SafetyNavbar>
  <view class="container">
    <view style="text-align: center" v-if="model.buildingId === ''">请先选择楼栋</view>
    <floorMap v-else :floor-info="floorData" :isAddMark="true" :pointer="pointer" @add-mark="addMark"></floorMap>
    <view class="fixed-bottom">
      <wd-button size="medium" @click="handleClickRight">确定</wd-button>
    </view>
  </view>
</template>
<script lang="ts" setup>
import floorMap from '../../components/indoorMap/index.vue'
import SafetyNavbar from '../components/safety-navbar.vue'
import { onMounted, ref, h, watch } from 'vue'
import { postqueryErecordBuildingFloorListAPI } from './featch'
import { useUserStore } from '@/store'
const userStore = useUserStore()
const userinfo: any = userStore.userInfo ? userStore.userInfo : {}
const model = ref<any>({ unitId: '', buildingId: '', floorId: '' })

const pointer = ref<any>({ x: '', y: '' }) // 回显点位
onLoad((params) => {
  // console.log(params)
  model.value.buildingId = params.buildId
  model.value.floorId = params.floorId
  // 根据上个页面选择的隐患单位查询对应的隐患位置
  if (params.status === '1') {
    model.value.unitId = params.unitId
  } else {
    model.value.unitId = userinfo.unitId
  }
  pointer.value.x = params.x
  pointer.value.y = params.y
  //   x=13705659.220891256&y=5010837.072670768
  if (params.buildId !== '') {
    setTimeout(() => {
      getFloorListByUnitIdAndBuilding()
    }, 100)
  }
})

const floorData = ref({
  unitId: '',
  buildingId: '',
  floorId: '',
  floorAreaImg: '',
})

function getFloorListByUnitIdAndBuilding() {
  postqueryErecordBuildingFloorListAPI(model.value)
    .then((res: any) => {
      // console.log(res)
      if (res.data.rows.length === 0) {
        uni.showToast({
          icon: 'none',
          title: '没有找到该信息数据',
        })
        return
      }
      floorData.value.unitId = res.data.rows[0].unitId
      floorData.value.buildingId = res.data.rows[0].buildingId
      floorData.value.floorId = res.data.rows[0].floorId
      floorData.value.floorAreaImg = res.data.rows[0].floorAreaImg
    })
    .finally(() => { })
}
let pointeinfo = null
function handleClickRight() {
  uni.setStorageSync('pointerinfo', pointeinfo)
  uni.navigateBack()
}

const addMark = (val: { x: number; y: number; text: string }) => {
  // console.log('🚀 ~ addMark ~ val:', val)
  pointeinfo = val
}

defineOptions({ name: 'plannedManagementDetails' })
</script>
<style lang="scss">
::v-deep {
  .wd-button.is-medium.is-round {
    width: 90%;
    min-width: 120px;
    border-radius: 5px;
  }
}

.fixed-bottom {
  position: fixed;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: space-around;
  width: 100%;
  height: 3.375rem;
  line-height: 3.375rem;
  // color: #fff;
  background-color: white;
}
</style>
