<route lang="json5">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '隐患库',
  },
}
</route>
<template>
  <z-paging ref="paging" v-model="factorClassItemCommonList" @query="getClassItemAndCommonByFactorIdList">
    <template #top>
      <SafetyNavbar title="隐患库"></SafetyNavbar>
      <wd-search placeholder-left placeholder="请输入隐患描述模糊搜索" @change="search" :hide-cancel="true"
        v-model="paramsList.likeParam" @clear="clear" />
      <view style="display: flex; align-items: center; width: 92%; padding-bottom: 10px; margin: auto">
        <el-tree-select v-model="deviceTypeArr" :data="treeData" fit-input-width @node-click="getCheckedNodes"
          clearable="true" @clear="cleartree" check-strictly placeholder="请选择隐患分类" />
      </view>
    </template>
    <view class="container">
      <view v-if="factorClassItemCommonList.length">
        <banklist v-if="factorClassItemCommonList.length !== 0" :factorClassData="factorClassItemCommonList"
          :pageType="pageType"></banklist>
      </view>
      <template v-else>
        <wd-status-tip image="content" tip="暂无数据~" />
      </template>
    </view>
  </z-paging>
</template>

<script lang="ts" setup>
import SafetyNavbar from '../components/safety-navbar.vue'
import banklist from './components/banklist.vue'
import {
  postHazardEssentialFactorClassAPI,
  postHazardEssentialFactorClassItemAPI,
  postClassItemAndCommonByFactorIdAPI,
} from './featch'
const noDate = ref<boolean>(false)
const TreeList = ref<any[]>([])
const value = ref<string>()
const inspectionItem = ref<any>('')
const ids = ref<any>('')
const pageType = ref<number>(0)
const EssentialFactorClassItem = ref<any[]>([])
const essentialFactorId = ref<any>('')
const paging = ref()
const paramsList = ref({
  factorId: null,
  classId: '',
  likeParam: '',
  pageNo: 1,
  pageSize: 10,
})
onLoad((params) => {
  // 判断是否从本模块打开页面
  pageType.value = params.type
  essentialFactorId.value = params.id
  paramsList.value.factorId = essentialFactorId.value
  //   根据隐患库id获取隐患类型
  gethazardEssentialFactorClassList(params.id)
  // 根据隐患库id获取隐患库常见隐患
  // getClassItemAndCommonByFactorIdList(essentialFactorId.value)
})

// 获取隐患库检查项
// function getHazardEssentialFactorClassItem(ids: string) {
//   postHazardEssentialFactorClassItemAPI({
//     essentialFactorClassId: ids,
//     essentialFactorId: essentialFactorId.value,
//     // hazardDescribe: inspectionItem.value,
//     pageNo: 1,
//     pageSize: -1,
//   } as any)
//     .then((res: any) => {
//       // // console.log(res, '获取隐患库检查项')
//       EssentialFactorClassItem.value = res.data.rows
//     })
//     .finally(() => {})
// }

// const deviceTypeArr = ref([])
const deviceTypeArr = ref('')
const treeData = ref<any>([])
const colPickerData = ref()
function gethazardEssentialFactorClassList(id) {
  // console.log(id, '================调用获取隐患分类')
  postHazardEssentialFactorClassAPI({ essentialFactorId: id, parentId: 0 } as any)
    .then((res: any) => {
      colPickerData.value = res.data
      // const arr = colPickerData.value.map((item) => {
      // return {
      //   value: item.id,
      //   label: item.className,
      //   ...item,
      // }
      // })
      const arr = colPickerData.value.map((item) => renameKeys(item))
      treeData.value = arr
      // console.log(treeData.value, '===treeData=====')
      if (treeData.value && treeData.value.length > 0) {
        ids.value = getAllIds(treeData.value).join(',')
        paramsList.value.classId = ids.value
        paging.value.reload()
        // getClassItemAndCommonByFactorIdList()
      }
    })
    .finally(() => {
      // console.log(111)
    })
}

function renameKeys(node) {
  node.value = node.id
  node.label = node.className
  if (node.children && node.children.length > 0) {
    node.children.forEach((child) => renameKeys(child))
  }
  return node
}

// const classId = ref('')
const total = ref(0)
const factorClassItemCommonList = ref<any[]>([])
// 根据隐患库id获取隐患库常见隐患
function getClassItemAndCommonByFactorIdList(pageNo) {
  uni.showLoading({ mask: true })
  paramsList.value.pageNo = pageNo
  postClassItemAndCommonByFactorIdAPI(paramsList.value)
    .then((res: any) => {
      uni.hideLoading()
      // 常见隐患
      total.value = res.data.factorClassItemCommonList
        ? res.data.factorClassItemCommonList.total
        : 0
      res.data.factorClassItemCommonList &&
        res.data.factorClassItemCommonList.records.forEach((item) => {
          if (item.hazardDesc.length > 40) {
            item.hazardDesc = item.hazardDesc.slice(0, 40) + '...'
          }
        })
      total.value = res.data.factorClassItemCommonList.total
      paging.value.completeByTotal(res.data.factorClassItemCommonList.records, total.value)
    })
    .finally(() => {
      uni.hideLoading()
    })
}

// 点击节点
function getCheckedNodes(node) {
  // console.log(getBottomIds(node).join(','), '=======paramsList.value======')
  paramsList.value.pageNo = 1
  paramsList.value.classId = getBottomIds(node).join(',')
  // factorClassItemCommonList.value = []
  paging.value.reload()
  // getClassItemAndCommonByFactorIdList()
}
function cleartree() {
  deviceTypeArr.value = ''
  paramsList.value.pageNo = 1
  paramsList.value.classId = ids.value
  paging.value.reload()
  // factorClassItemCommonList.value = []
  // getClassItemAndCommonByFactorIdList()
}

// const { findChildrenByCode } = useColPickerData()
// const columnChange = ({ selectedItem, resolve, finish }) => {
//   finish()
//   // // console.log(colPickerData.value, "================colPickerData.value")
//   // // console.log(selectedItem.value, "================selectedItem.value")
//   // getTreeByIdfindchild(colPickerData.value, selectedItem.value)
//   // // console.log(getTreeByIdfindchild(colPickerData.value, selectedItem.value))
//   // getPList(selectedItem.value)
//   const areaData = findChildrenByCode(colPickerData.value, selectedItem.value)
//   if (areaData && areaData.length) {
//     resolve(
//       areaData.map((item) => {
//         return {
//           value: item.id,
//           label: item.className,
//           ...item,
//         }
//       }),
//     )
//   } else {
//     finish()
//   }
// }

// // 格式化方法
// const displayFormat = (selectedItems) => {
//   // console.log(selectedItems)
//   if (!selectedItems.length) return
//   return selectedItems.map((item) => item.label).join('>')
// }
// // 类型筛选
// function handleConfirm({ value, selectedItems }) {
//   // getHazardEssentialFactorClassItem(getBottomIds(selectedItems).join(','))
//   // // console.log(getBottomIds(selectedItems).join(','), '--------------???????????????/??')
//   paramsList.value.pageNo = 1
//   factorClassItemCommonList.value = []
//   paramsList.value.classId = getBottomIds(selectedItems).join(',')
//   getClassItemAndCommonByFactorIdList()
// }

// // 点击获取
// function getPList(id) {
//   paramsList.value.classId = id
//   getClassItemAndCommonByFactorIdList()
// }
// // 查询所有
// function cleartype() {
//   deviceTypeArr.value = []
//   gethazardEssentialFactorClassList(essentialFactorId.value)
// }

const scrolltolower = () => {
  // console.log('底部')
  if (factorClassItemCommonList.value.length < total.value) {
    paramsList.value.pageNo++
    // getClassItemAndCommonByFactorIdList()
  } else {
    uni.showToast({
      title: '没有更多数据了',
      icon: 'none',
      duration: 2000,
    })
  }
}

// 根据隐患描述模糊查询
let timeoutId: ReturnType<typeof setTimeout> | null = null
function search() {
  // // console.log(event)
  if (timeoutId !== null) {
    clearTimeout(timeoutId)
    timeoutId = null
  }
  timeoutId = setTimeout(() => {
    paramsList.value.pageNo = 1
    // factorClassItemCommonList.value = []
    paging.value.reload()
    // getClassItemAndCommonByFactorIdList()
  }, 500)
}
// 清空输入框
function clear() {
  inspectionItem.value = ''
}
// 获取点击节点下的所有id
function getBottomIds(tree: any) {
  // console.log(tree, 'tree')
  const bottomIds = []
  function traverse(node) {
    if (!node.children || node.children.length === 0) {
      bottomIds.push(node.id)
    } else {
      bottomIds.push(node.id)
      node.children.forEach((child) => traverse(child))
    }
  }
  traverse(tree)
  return bottomIds
}
// 获取所有父id
function getAllPIds(tree: any) {
  // console.log(tree, 'tree')
  const AllIds = []
  tree.forEach((item, index) => {
    AllIds.push(item.id)
  })
  // console.log(AllIds, 'AllIds')
  return AllIds
}
function getAllIds(nodes: any) {
  const allIds = []
  function getIdsRecursive(nodes: any) {
    nodes.forEach((node) => {
      allIds.push(node.id)
      if (node.children && node.children.length > 0) {
        getIdsRecursive(node.children)
      }
    })
  }
  getIdsRecursive(nodes)
  return allIds
}
</script>

<style lang="scss" scoped>
::v-deep {
  .navbar {
    position: unset !important;
  }

  .wd-search {
    background: #fff0;
  }
}

.custom-footer {
  display: flex;
  justify-content: flex-end;
  padding: 10px;
}
</style>
