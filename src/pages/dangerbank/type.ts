// 隐患库列表
export type pageForm = {
  delFlag: string
  elementCode: string
  elementName: string
  status: number
  createTime: string
  id: string
  updateTime: string
  searchCount: number
  zhId: string
  pageNo: number
  pageSize: number
  pages: number
  total: number
  unitId: string
  childUnitId: string
  valid: number // 1 有效 0 无效
}
// 隐患分类
export type hazardEssentialFactorClass = {
  id: string
  essentialFactorId: string
  parentId: string
  className: string
  classSort: number
  createTime: string
  rootFlag: number
  delFlag: number
  children: []
}
// 常见隐患
export type ClassItemAndCommonByFactorData = {
  classId: string
  factorId: string
  likeParam: string
}
// 隐患检查项
export type hazardEssentialFactorClassItem = {
  id: string
  essentialFactorClassFullName: string
  essentialFactorClassName: string
  essentialFactorId: string
  essentialFactorClassId: string
  hazardGradeId: string
  inspectionItem: string
  inspectionDescribe: string
  inspectionAsk: string
  legalText: string
  legalLiability: string
  hazardDescribe: string
  updateTime: string
  createTime: string
  delFlag: number
}

// 隐患库常见隐患
export type factorClassListData = {
  factorClassItemCommonList: []
  factorClassList: []
  factorClassItemList: []
}

export type pageData<T> = {
  pageNo: number
  pageSize: number
  pages: number
  rows: T[]
  total: number
}
export type ResponsesData<T> = {
  total: number
  code: string
  data: T[]
  dataType: string
  message: object
  status: string
  token: string
}
