<route lang="json5">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '隐患库',
  },
}
</route>
<template>
  <view class="container" style="overflow: auto">
    <SafetyNavbar title="隐患库"></SafetyNavbar>
    <wd-cell-group v-if="bankData.length > 0" border>
      <wd-cell
        v-for="itme in bankData"
        :key="itme.id"
        :title="itme.elementName"
        @click="selType(itme.id)"
        is-link
      ></wd-cell>
    </wd-cell-group>
    <view v-else class="list-null">
      <wd-status-tip image="content" tip="暂无数据~" />
    </view>
  </view>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import SafetyNavbar from '../components/safety-navbar.vue'
// import banklist from './components/banklist.vue'
import { postHazardEssentialFactorAPI } from './featch'
import { useUserStore } from '@/store'
import { pageForm } from './type'
const userStore = useUserStore()
const userinfo: any = userStore.userInfo ? userStore.userInfo : {}
const type = ref(0)

const Parameters = ref<pageForm>({
  zhId: userinfo.zhId,
  delFlag: '',
  elementCode: '',
  elementName: '',
  status: 0,
  createTime: '',
  id: '',
  updateTime: '',
  searchCount: 0,
  pageNo: 1,
  pageSize: -1,
  pages: 0,
  total: 0,
  // topUnitId
  unitId: '',
  childUnitId: '',
  valid: 1,
})
onLoad((params) => {
  // console.log(params)
  if (params.type) {
    type.value = params.type
  }

  // Parameters.value.unitId = params.unitId ? params.unitId : userinfo.topUnitId
  if (params.unitId) {
    Parameters.value.unitId = params.unitId
  } else {
    Parameters.value.unitId = +userinfo.orgRes === 1 ? userinfo.topUnitId : userinfo.serverUnitId
  }
})
const bankData = ref<pageForm[]>([])
watch(
  () => Parameters.value,
  () => {
    uni.showLoading({ mask: true })
    postHazardEssentialFactorAPI(Parameters.value)
      .then((res: any) => {
        uni.hideLoading()
        bankData.value = res.data.rows
      })
      .finally(() => {
        uni.hideLoading()
        // console.log(111)
      })
  },
  { deep: true },
)
// postHazardEssentialFactorAPI(Parameters.value)
//   .then((res: any) => {
//     bankData.value = res.data.rows
//   })
//   .finally(() => {
//     // console.log(111)
//   })
function selType(id) {
  // // console.log(type.value)
  uni.navigateTo({
    url: `/pages/dangerbank/dangertypelist?id=${id}&type=${type.value}`,
  })
}
</script>

<style lang="scss" scoped>
::v-deep {
  .wd-search {
    background: #fff0;
  }
}
</style>
