<route lang="json5">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '隐患详情',
  },
}
</route>
<template>
  <SafetyNavbar title="隐患详情"></SafetyNavbar>
  <view class="container">
    <info1 :info1-itme="detailinfo"></info1>
    <view v-if="parseInt(pageType) === 1" class="fixed-bottom">
      <wd-button @click="selctFactorClassItem(detailinfo)">上报隐患</wd-button>
    </view>
  </view>
</template>

<script lang="ts" setup>
import SafetyNavbar from '../components/safety-navbar.vue'
import { postHazardEssentialFactorClassItemDetailAPI } from './featch'
import { hazardEssentialFactorClassItem } from './type'
import info1 from './components/info1.vue'
const detailinfo = ref<hazardEssentialFactorClassItem>({
  id: '',
  essentialFactorId: '',
  essentialFactorClassId: '',
  hazardGradeId: '',
  inspectionItem: '',
  inspectionDescribe: '',
  inspectionAsk: '',
  legalText: '',
  legalLiability: '',
  hazardDescribe: '',
  updateTime: '',
  createTime: '',
  delFlag: 0,
  essentialFactorClassFullName: '',
  essentialFactorClassName: '',
})
const pageType = ref('')
onLoad((params) => {
  // console.log(params)
  pageType.value = params.type
  getEssentialFactorClassItemDetail(params.id)
})

function getEssentialFactorClassItemDetail(id) {
  postHazardEssentialFactorClassItemDetailAPI(id)
    .then((res) => {
      detailinfo.value = res.data as any
    })
    .finally(() => {
      // console.log(111)
    })
}
function selctFactorClassItem(event) {
  uni.setStorageSync('FactorClassItem', event)
  uni.navigateBack({
    delta: 3,
  })
}
</script>

<style lang="scss" scoped>
::v-deep {
  .wd-search {
    background: #fff0;
  }
}

.fixed-bottom {
  position: fixed;
  bottom: 0;
  display: flex;
  flex-direction: row;
  align-items: center;
  width: 100%;
  height: 3.375rem;
  line-height: 3.375rem;
  color: #fff;
  text-align: center;
  background-color: white;
}
</style>
