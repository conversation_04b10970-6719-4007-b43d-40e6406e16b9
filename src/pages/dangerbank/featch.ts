/*
 * @Author: 悦悦 <EMAIL>
 * @Date: 2024-09-21 15:55:16
 * @LastEditors: 悦悦 <EMAIL>
 * @LastEditTime: 2024-11-22 15:03:03
 * @FilePath: /隐患排查app/src/pages/dangerbank/featch.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
// hazardEssentialFactor/listPage
import { http } from '@/utils/http'
import {
  ResponsesData,
  pageData,
  pageForm,
  hazardEssentialFactorClass,
  hazardEssentialFactorClassItem,
  ClassItemAndCommonByFactorData,
} from './type'
import { $api } from '@/api'

/** post 请求
 * 隐患库列表
 * /hazardEssentialFactor/listPage */
export const postHazardEssentialFactorAPI = (Parameters: pageForm) => {
  return http.post($api.type.hazard + '/hazardEssentialFactor/listPage', Parameters)
}
/** post 请求
 * 隐患分类列表
 * /hazardEssentialFactorClass/treeList */
export const postHazardEssentialFactorClassAPI = (Parameters: hazardEssentialFactorClass) => {
  return http.post($api.type.hazard + '/hazardEssentialFactorClass/treeList', {
    ...Parameters,
  })
}
/** post 请求
 * 隐患库检查项列表
 * /hazardEssentialFactorClassItem/listPage */
export const postHazardEssentialFactorClassItemAPI = (
  Parameters: hazardEssentialFactorClassItem,
) => {
  return http.post($api.type.hazard + '/hazardEssentialFactorClassItem/listPage', {
    ...Parameters,
  })
}
/** post 请求
 * 隐患库检查项列表
 * /hazardEssentialFactorClassItem/detail */
export const postHazardEssentialFactorClassItemDetailAPI = (id: any) => {
  return http.post($api.type.hazard + '/hazardEssentialFactorClassItem/detail?classItemId=' + id)
}

/** post 请求
 * 隐患库常见隐患列表
 * /hazardEssentialFactor/getClassItemAndCommonByFactorId */
export const postClassItemAndCommonByFactorIdAPI = (Parameters: ClassItemAndCommonByFactorData) => {
  return http.post($api.type.hazard + '/hazardEssentialFactor/getClassItemAndCommonByFactorId', {
    ...Parameters,
  })
}
