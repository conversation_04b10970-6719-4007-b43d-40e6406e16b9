<!--
 * @Author: 悦悦 <EMAIL>
 * @Date: 2024-11-21 13:51:55
 * @LastEditors: 悦悦 <EMAIL>
 * @LastEditTime: 2024-11-27 14:00:35
 * @FilePath: /隐患排查app/src/pages/dangerbank/components/banklist.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '隐患库',
  },
}
</route>
<template>
  <view class="bank">
    <!-- <view class="bank_itme" v-for="(itme, index) in data" :key="index">
      <view>{{ itme.inspectionItem }}</view>
      <view v-for="(iFactorClassItemCommon, $i) in factorData" :key="$i">
        {{ iFactorClassItemCommon.hazardDesc }}
      </view>
      <view style="padding-top: 8px; font-size: 12px; color: #5b4949cc">
        {{ itme.essentialFactorClassFullName || itme.essentialFactorClassName }}
      </view>
      <view style="margin-top: 0.5rem">
        <wd-button @click="goDetail(itme.id)">详情</wd-button>
        <wd-button @click="selctFactorClassItem(itme)" v-if="parseInt(pageType) === 1">
          上报隐患
        </wd-button>
      </view>
    </view> -->

    <view class="bank_itme" v-for="(iFactorClassItemCommon, $i) in factorClassData" :key="$i">
      <view :key="$i">
        {{ iFactorClassItemCommon.hazardDesc }}
      </view>
      <view>
        <view style="padding-top: 8px; font-size: 12px; color: #5b4949cc">
          {{
            iFactorClassItemCommon.essentialFactorClassFullName ||
            iFactorClassItemCommon.essentialFactorClassName
          }}
        </view>
        <view style="margin-top: 0.5rem">
          <wd-button @click="goDetail(iFactorClassItemCommon.id)">详情</wd-button>
          <wd-button
            @click="selctFactorClassItem(iFactorClassItemCommon)"
            v-if="parseInt(pageType) === 1"
          >
            上报隐患
          </wd-button>
        </view>
      </view>
    </view>
  </view>
</template>
<script lang="ts" setup>
import {
  ResponsesData,
  pageData,
  pageForm,
  hazardEssentialFactorClassItem,
  factorClassListData,
} from '../type'

const props = defineProps<{
  data: hazardEssentialFactorClassItem[]
  factorClassData: any
  pageType: any
}>()

function goDetail(id) {
  uni.navigateTo({
    url: `/pages/dangerbank/detail?id=${id}&type=${props.pageType}`,
  })
}
// 选择按钮
function selctFactorClassItem(event) {
  uni.setStorageSync('FactorClassItem', event)
  uni.navigateBack({
    delta: 2,
  })
}
</script>
<style lang="scss">
::v-deep {
  .wd-button.is-medium.is-round {
    margin: 0.5rem;
  }
}

.bank {
  padding: 0.5rem;

  .bank_itme {
    padding: 0.5rem;
    margin: auto;
    // border: 1px solid red;
    margin: 0.5rem;
    background-color: #ebeef5;
  }
}
</style>
