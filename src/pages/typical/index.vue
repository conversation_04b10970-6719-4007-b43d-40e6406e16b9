<route lang="json5">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '举一反三',
  },
}
</route>
<template>
  <z-paging ref="paging" v-model="cardsData" @query="getPageList">
    <template #top>
      <SafetyNavbar title="举一反三"></SafetyNavbar>
      <wd-drop-menu>
        <wd-drop-menu-item title="检查类型" v-model="fromDate.checkType" :options="planTypeList"
          @update:model-value="handleChange" />
        <wd-drop-menu-item title="任务状态" v-model="fromDate.taskStatus" :options="taskStateList"
          @update:model-value="handleChange" />
      </wd-drop-menu>
    </template>
    <view class="container">
      <view v-if="cardsData.length">
        <cardList :cardsData="cardsData"></cardList>
      </view>
      <template v-else>
        <view class="list-null">
          <noData title="暂无数据~"></noData>
        </view>
      </template>
    </view>
  </z-paging>
</template>
<script lang="ts" setup>
import { debounce } from '@/utils'
import SafetyNavbar from '../components/safety-navbar.vue'
import cardList from './components/card_list.vue'
import { postgetOneToThreeDetailAPI, postgetOneToThreePageAPI } from './featch'
import { useUserStore } from '@/store'
const userStore = useUserStore()
const userinfo: any = userStore.userInfo ? userStore.userInfo : {}
// "典型频发问题"修改为 "频发典型问题"
// 0 - 省级督导检查; 1 - 市级督导检查; 2 - 行业专家指导检查; 3 - 集团督导检查; 4 - 公司督导检查; 5 - 频发典型问题
const planTypeList = ref([
  { label: '全部', value: null },
  { label: '省级督导检查', value: 0 },
  { label: '市级督导检查', value: 1 },
  { label: '行业专家指导检查', value: 2 },
  { label: '集团督导检查', value: 3 },
  { label: '公司督导检查', value: 4 },
  { label: '频发典型问题', value: 5 },
])
const taskStateList = ref([
  { label: '全部', value: null },
  { label: '进行中', value: 1 },
  { label: '已结束', value: 2 },
])
const paging = ref()
const fromDate = ref({
  checkType: null, // 举一反三的检查类型
  taskStatus: null, // 状态
  pageNo: 1,
  pageSize: 10,
  searchCount: 0,
  unitIds: userinfo.unitId,
  findUpOrDownUnit: 1, // 查下级单位还是上级:1-上级;2-下级
})
const total = ref(0)
const noDate = ref<boolean>(false)
const cardsData = ref<any>([])

function handleChange() {
  // console.log(fromDate.value)
  fromDate.value.pageNo = 1
  cardsData.value = []
  // getPageList()
  paging.value.reload()
}

// const handleScrollToLower = debounce(() => {
//   // 滚动到底部时执行的操作
//   // console.log('底部')
//   if (cardsData.value.length < total.value) {
//     fromDate.value.pageNo++
//     // getPageList()
//   } else {
//     uni.showToast({
//       title: '没有更多数据了',
//       icon: 'none',
//       duration: 2000,
//     })
//   }
// }, 500)
onLoad(() => {
  // getPageList()
  paging.value.reload()
})
function getPageList(pageNo) {
  uni.showLoading({ mask: true })
  fromDate.value.pageNo = pageNo
  postgetOneToThreePageAPI(fromDate.value)
    .then((res: any) => {
      uni.hideLoading()
      if ((res.code as any) === 'success') {
        total.value = res.data.total
        paging.value.completeByTotal(res.data.rows, total.value)
      }
    })
    .finally(() => {
      uni.hideLoading()
    })
}
onShow(() => {
  // 局部更新数据状态
  paging.value.reload()
  // const taskid = uni.getStorageSync('taskIdByindex') ? uni.getStorageSync('taskIdByindex') : ''
  // if (taskid !== '') {
  //   postgetOneToThreeDetailAPI(taskid).then((res: any) => {
  //     uni.removeStorageSync('taskIdByindex')
  //     updateValueById(taskid, res.data.hazardNum)
  //   })
  // }
})
// const updateValueById = (id: string, newValue: number) => {
//   const item = cardsData.value.find((item) => item.id === id)
//   if (item) {
//     item.hazardNum = newValue
//   } else {
//     console.warn(`Item with id ${id} not found`)
//   }
// }
</script>
<style lang="scss" scoped>
::v-deep {
  .navbar {
    position: unset !important;
  }
}

.no-date {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100px;
  color: rgb(125, 125, 125);
}
</style>
