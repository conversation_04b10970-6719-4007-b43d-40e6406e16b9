<template>
  <view class="container">
    <view class="infobox">
      <view class="infobox_title">
        <!-- <view class="fasticon"></view> -->
        <view>{{ info1Itme.hazardDesc || '--' }}</view>
      </view>
      <view class="infoItme">
        <view class="infokey">单位</view>
        <view class="infovalue">{{ info1Itme.unitName || '--' }}</view>
      </view>
      <view class="infoItme">
        <view class="infokey">隐患分类</view>
        <view class="infovalue">{{ info1Itme.hazardTypeName || '--' }}</view>
      </view>
      <view class="infoItme">
        <view class="infokey">隐患等级</view>
        <view class="infovalue">{{ info1Itme.hazardLevelName || '--' }}</view>
      </view>
      <view class="infoItme">
        <view class="infokey">隐患位置</view>
        <view class="infovalue">{{ info1Itme.hazardPosition || '--' }}</view>
      </view>
      <view class="infoItme">
        <view class="infokey">隐患来源</view>
        <view class="infovalue">{{ info1Itme.hazardSourceName || '--' }}</view>
      </view>
      <view class="infoItme">
        <view class="infokey">隐患整改人员</view>
        <view class="infovalue">
          <span v-if="info1Itme.reformUsers?.length === 0">--</span>
          <span v-else v-for="(item, index) in info1Itme.reformUsers" :key="index">
            {{ item.reformUserName }}
            {{ index === info1Itme.reformUsers.length - 1 ? '' : ',' }}
          </span>
        </view>
      </view>
      <view class="infoItme">
        <view class="infokey" style="min-width: 5.25rem">隐患图片</view>
        <view class="infovalue info_img"
          style="display: flex; flex-direction: row; flex-wrap: wrap; justify-content: flex-end">
          <view class="fileList" v-for="(item, index) in info1Itme.files" :key="index">
            <!-- <wd-img
              style="width: 100%; height: 90%"
              :src="VITE_PREVIEW_BASEURL + item.fileUrl"
              :enable-preview="true"
            /> -->
            <wd-img style="width: 100%; height: 90%" :src="getFileURL(item.fileUrl, true)"
              @click="previewImage(getFileURL(item.fileUrl))" />
          </view>
        </view>
      </view>
      <view class="infoItme">
        <view class="infokey">任务发布人员</view>
        <view class="infovalue">
          <span>{{ info1Itme.createInfo?.createByName || '--' }}</span>
        </view>
      </view>
      <view class="infoItme">
        <view class="infokey">任务发布时间</view>
        <view class="infovalue">
          <span>{{ info1Itme.createInfo?.taskStartTime || '--' }}</span>
        </view>
      </view>
      <view class="infoItme">
        <view class="infokey">任务截止时间</view>
        <view class="infovalue">
          <span>{{ formattedDate(info1Itme.createInfo?.taskEndTime) || '--' }}</span>
        </view>
      </view>
    </view>
  </view>
  <comOverlay ref="overlay" v-if="imageshow" :imgurl="imgurl" @closeimg="closeimg"></comOverlay>
</template>

<script lang="ts" setup>
import { useAppStore, usefileConfigStore } from '@/store'
import { formattedDate, getFileURL } from '@/utils'
import comOverlay from '@/components/commonSelect/com-overlay.vue'
defineProps({
  info1Itme: {
    type: Object,
  },
})
const overlay = ref(null)
const imageshow = ref(false)
const imgurl = ref('')
const previewImage = (url) => {
  imageshow.value = true
  imgurl.value = url
}
// 关闭查看图片
function closeimg(val) {
  imageshow.value = val
}
</script>

<style lang="scss" scoped>
body {
  background-color: #f9f9f9 !important;
}

.infobox {
  padding-bottom: 1.125rem;
  margin-bottom: 1rem;
  background-color: white;

  .infobox_title {
    display: flex;
    flex-direction: row;
    align-items: center;
    width: 90%;
    // padding: 0.25rem 0 0.9375rem 1.125rem;
    padding: 0.25rem;
    margin: auto;
    border-bottom: #d3d2d2 0.5px solid;

    .fasticon {
      width: 0.25rem;
      height: 0.875rem;
      margin-right: 0.1875rem;
      background-color: #597bf7;
      border-radius: 1.125rem;
    }
  }

  .infoItme {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    padding: 0.5rem 1rem 0.5rem 1rem;
    font-size: 0.875rem;

    .infokey {
      min-width: 6.25rem;
      color: #7f7f7f;
    }

    .infovalue {
      width: 100%;
      text-align: left;
    }
  }
}

.fixed-bottom {
  position: fixed;
  bottom: 0;
  width: 100%;
  height: 3.375rem;
  line-height: 3.375rem;
  color: #fff;
  text-align: end;
  background-color: white;
  border-top: 0.3px solid #ccc;
}

.fileList {
  display: inline-block;
  width: 4.125rem;
  height: 4.125rem;
  margin: 0.5rem;
  text-align: left;
  border-radius: 0.375rem;
}

.info_img {
  display: flex;
  align-items: center;
  justify-content: start !important;
}
</style>
