<template>
  <view class="container1">
    <!-- 统计 -->
    <div class="count_div">
      <div class="itme">
        <view class="itmefont20">{{ hazardMegerStatistics.total || 0 }}</view>
        <view>隐患总数</view>
      </div>
      <div class="itme">
        <view class="itmefont20 color1">{{ hazardMegerStatistics.disposedNum || 0 }}</view>
        <view>已整改</view>
      </div>
      <div class="itme">
        <view class="itmefont20 color2">
          {{ hazardMegerStatistics.disposingNum + hazardMegerStatistics.unDisposedNum || 0 }}
        </view>
        <view>未整改</view>
      </div>
      <div class="itme">
        <view class="itmefont20 color3">{{ hazardMegerStatistics.timeout || 0 }}</view>
        <view>已超期</view>
      </div>
    </div>
    <!-- 列表 -->
    <view style="padding: 1px 0; margin-top: 8px; background-color: white">
      <div class="no-date" v-if="noDate">暂无数据</div>
      <scroll-view v-else style="height: 78vh" scroll-y="true">
        <view v-for="(item, index) in cardsData" :key="index" @click="openDetail(item)" class="muenitme">
          <view class="field1" style="padding-top: 1rem">
            <view class="field_title">{{ item.hazardTypeName || '--' }}</view>
            <view>
              <wd-tag style="margin-right: 6px; font-size: 0.875rem" type="primary" plain>
                {{ item.hazardLevelName || '--' }}
              </wd-tag>
              <!-- warning  success :type="item.disposeState == 0 ? 'warning' : 'success'" -->
              <wd-tag style="min-width: 4rem; font-size: 0.875rem; text-align: center" :style="item.disposeState == 0
                  ? 'background:#F39600'
                  : item.disposeState == 1
                    ? 'background:#00B578'
                    : 'background:#00B578'
                ">
                {{ item.disposeStateName || '--' }}
              </wd-tag>
            </view>
          </view>
          <view class="field">
            <view class="field_content">
              {{ item.hazardDesc || '--' }}
            </view>
          </view>
          <view class="field1">
            <view>{{ item.unitName || '--' }}</view>
            <view style="max-width: 60%; overflow: hidden; text-overflow: ellipsis; white-space: nowrap">
              {{ item.hazardPosition || '--' }}
            </view>
          </view>
          <view class="field1">
            <view>{{ item.createByName || '--' }}</view>
            <view style="max-width: 60%; overflow: hidden; text-overflow: ellipsis; white-space: nowrap">
              {{ item.eventTime || '--' }}
            </view>
          </view>
        </view>
      </scroll-view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { posthazardMegerpageEventAPI, posthazardMegerStatisticsAPI } from '../featch'
const props = defineProps<{
  taskId?: string
}>()
const noDate = ref<boolean>(false)
const hazardMegerStatistics = ref({})
const gettaskId = ref('')
gettaskId.value = props.taskId || ''
GetoneToThreeStatistics()
function GetoneToThreeStatistics() {
  posthazardMegerStatisticsAPI({
    haveOneFindThree: gettaskId.value,
  }).then((res: any) => {
    // console.log(res)
    hazardMegerStatistics.value = res.data
  })
}

const cardsData = ref<any[]>([])
getPageList()
function getPageList() {
  posthazardMegerpageEventAPI({ haveOneFindThree: gettaskId.value, pageNo: 1, pageSize: -1 }).then(
    (res: any) => {
      // console.log(res.data.rows)
      if (res.data && res.data.rows.length === 0) {
        noDate.value = true
      } else {
        noDate.value = false
        cardsData.value = cardsData.value.concat(res.data.rows)
      }
    },
  )
}

function openDetail(item) {
  // 隐患id，flag,
  //   disposeId.value = params.disposeId
  // paramasData.value.flag = params.flag
  // paramasData.value.id = params.id
  uni.navigateTo({
    url:
      '/pages/dangerlist/detail?id=' +
      item.id +
      '&flag=' +
      item.flag +
      '&disposeId=' +
      item.disposeId +
      '&isdispose=1',
  })
}
const updateAPI = () => {
  gettaskId.value = props.taskId || ''
  cardsData.value = []
  getPageList()
  GetoneToThreeStatistics()
}
defineExpose({ updateAPI })
</script>

<style lang="scss" scoped>
.count_div {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-around;
  padding: 16px;
  margin-top: 8px;
  background-color: white;

  .itme {
    text-align: center;
  }

  .itmefont20 {
    font-size: 20px;
    font-weight: bold;
  }

  .color1 {
    color: #2ba471;
  }

  .color2 {
    color: #d54941;
  }

  .color3 {
    color: #e37318;
  }
}

.muenitme {
  width: 21.5625rem;
  // height: 11.25rem;
  margin: 0.9375rem;
  background-color: #ebeef5;
  border-radius: 0.6rem;

  .field1 {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.2rem 0.5rem;
    font-size: 0.75rem;
  }

  .field_title {
    width: 40%;
    overflow: hidden;
    font-size: 1rem;
    font-weight: bold;
    color: #000000;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .field_content {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .field {
    padding: 0.2rem 0.5rem;
    font-size: 0.875rem;
  }
}

.no-date {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100px;
  color: rgb(125, 125, 125);
}
</style>
