<!-- 隐患整改催促 -->
<template>
  <view class="abarbeitung">
    <view v-if="model.length === 0" style="margin-top: 1rem; color: #7f7f7f; text-align: center">
      暂无数据
    </view>
    <view v-else>
      <view v-for="(item, index) in model" :key="index" class="abarbeitung_itme">
        <view class="abarbeitung_itme_top">
          <wd-icon name="check-circle-filled" style="color: #00a720" size="22px"></wd-icon>
          <view style="margin-left: 0.625rem">{{ item.nodeName }}</view>
        </view>
        <view class="abarbeitung_itme_content">
          <view class="abarbeitung_itme_content_info">
            <view>
              <view v-for="(nodeitem, tag) in item.nodeInfo" :key="tag" class="info">
                <view style="display: flex" v-if="nodeitem.webType !== 'image'">
                  <view class="info_titele">{{ nodeitem.description }}</view>
                  <view class="info_content">{{ nodeitem.dataValue }}</view>
                </view>
                <view v-if="nodeitem.webType === 'image'" class="info">
                  <view class="info_titele">{{ nodeitem.description }}</view>
                  <view class="info_content" style="display: flex; flex-wrap: wrap">
                    <view class="fileList" v-for="imgurl in nodeitem.dataValue" :key="imgurl">
                      <!-- <wd-img style="width: 100%; height: 90%" :src="VITE_PREVIEW_BASEURL + imgurl"
                        :enable-preview="true" /> -->
                      <wd-img style="width: 100%; height: 90%" :src="getFileURL(imgurl, true)"
                        @click="previewImage(getFileURL(imgurl))" />
                    </view>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
  <comOverlay ref="overlay" v-if="imageshow" :imgurl="imgurl" @closeimg="closeimg"></comOverlay>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { postrecordAPI } from '../featch'
import { useAppStore, usefileConfigStore, useUserStore } from '@/store'
import comOverlay from '@/components/commonSelect/com-overlay.vue'
import { getFileURL } from '@/utils/index'

const userStore = useUserStore()
const userinfo: any = userStore.userInfo ? userStore.userInfo : {}
const porps = defineProps<{
  disposeId?: string
}>()
const getrecordParams = ref({
  disposeId: porps.disposeId,
  eventType: '4',
  subCenterCode: userinfo.zhId,
})
const model = ref<any>([])
postrecordAPI(getrecordParams.value)
  .then((res: any) => {
    model.value = res.data.rows
  })
  .finally(() => { })

// const previewImage = (url) => {
//   uni.previewImage({
//     urls: [url],
//   })
// }
const overlay = ref(null)
const imageshow = ref(false)
const imgurl = ref('')
const previewImage = (url) => {
  imageshow.value = true
  imgurl.value = url
}
// 关闭查看图片
function closeimg(val) {
  imageshow.value = val
}

const updaterecordAPI = () => {
  getrecordParams.value.disposeId = porps.disposeId
  postrecordAPI(getrecordParams.value)
}
defineExpose({ updaterecordAPI })
</script>

<style lang="scss" scoped>
.abarbeitung {
  //   border: 1px solid;
  //   display: flex;
  font-size: 0.875rem;

  .abarbeitung_itme {
    .abarbeitung_itme_top {
      display: flex;
    }

    .abarbeitung_itme_content {
      display: flex;
      width: 90%;
      padding-bottom: 0.75rem;
      padding-left: 1.2rem;
      margin-left: 0.6rem;
      border-left: 2px solid #ebebeb;

      .abarbeitung_itme_content_info {
        width: 100%;
        padding: 1rem;
        margin-top: 0.625rem;
        background-color: #ebeef5;
        border-radius: 2%;

        .info {
          display: flex;
          margin: 0.625rem 0 1rem 0;

          .info_titele {
            min-width: 5rem;
            color: #7f7f7f;
          }

          .info_content {
            margin-left: 1rem;
          }

          .fileList {
            width: 4.125rem;
            height: 4.375rem;
            margin: 0 0.5rem 0.5rem 0;
          }
        }
      }
    }
  }
}
</style>
