<template>
  <view v-for="(itme, index) in cardsData" :key="index" @click="openDetail(itme)" class="muenitme">
    <view class="field1" style="padding-top: 1rem">
      <view class="field_title">{{ itme.checkTypeName || '--' }}</view>
      <view>
        <wd-tag style="margin-right: 6px; font-size: 0.875rem" type="primary" plain>
          类比排查项：{{ itme.hazardNum || 0 }}
        </wd-tag>
        <!-- success  warning-->
        <wd-tag style="min-width: 4rem; padding: 2px; font-size: 0.875rem; text-align: center"
          :type="itme.taskStatus == 1 ? 'warning' : 'success'">
          {{ itme.taskStatus == 1 ? '进行中' : '已结束' }}
        </wd-tag>
      </view>
    </view>
    <view class="field">
      <view class="field_content">
        {{ itme.hazardDescribe || '--' }}
      </view>
    </view>
    <view class="field1">
      <view style="width: 40%; min-width: 40%" class="fontoverflow">
        {{ itme.createByName || '--' }}
      </view>
      <view style="max-width: 60%; overflow: hidden; text-overflow: ellipsis; white-space: nowrap">
        {{ formattedDate(itme.taskStartTime) || '--' }}至{{
          formattedDate(itme.taskEndTime) || '--'
        }}
      </view>
    </view>
  </view>
</template>
<script lang="ts" setup>
import { formattedDate } from '@/utils'
defineProps<{
  cardsData: any[]
}>()
function openDetail(itme) {
  // console.log('itme', itme)
  // 隐患id，发布人，发布时间，结束时间，任务类型 taskType 1-类比 2-典型频发问题
  uni.navigateTo({
    url: `/pages/typical/detail?id=${itme.id || ''}&hazardRandomCheckEventId=${itme.hazardRandomCheckEventId || ''}&taskType=${itme.taskType}`,
  })
}
</script>
<style lang="scss" scoped>
.muenitme {
  width: 21.5625rem;
  // height: 11.25rem;
  margin: 0.9375rem;
  background-color: #f7f7f7;
  border-radius: 0.6rem;

  .field1 {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.5rem;
    font-size: 0.875rem;
  }

  .field_title {
    width: 40%;
    overflow: hidden;
    font-size: 1rem;
    font-weight: bold;
    color: #000000;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .field_content {
    overflow: hidden;
    color: #9c9c9c;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .field {
    padding: 0.2rem 0.5rem;
    font-size: 0.875rem;
  }
}

.fontoverflow {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
