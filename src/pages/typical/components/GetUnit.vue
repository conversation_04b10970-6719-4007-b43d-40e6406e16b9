<!--
 * @Author: 悦悦 <EMAIL>
 * @Date: 2024-10-22 09:54:56
 * @LastEditors: 悦悦 <EMAIL>
 * @LastEditTime: 2024-10-24 17:58:50
 * @FilePath: /隐患排查app/src/components/commonSelect/common-Unit.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <wd-select-picker label="隐患单位" placeholder="请选择隐患单位" v-model="value" :columns="gradeList" value-key="unitId"
    label-key="unitName" align-right type="radio" required @confirm="handleConfirm"></wd-select-picker>
</template>

<script lang="ts" setup>
import { useUserStore } from '@/store'
import { getUnitListByTaskIdAPI } from '../featch'
const props = defineProps<{
  selectvalue?: string
  taskid?: string
}>()
const userStore = useUserStore()
const userinfo: any = userStore.userInfo ? userStore.userInfo : {}
const $emit = defineEmits(['getUnit'])
const value = ref<string>(props.selectvalue)
// 下级隐患单位列表
const gradeList = ref<any[]>()
getUnitListByTaskIdAPI({ id: props.taskid, type: 1 })
  .then((res: any) => {
    gradeList.value = res.data
    if (!props.selectvalue) {
      value.value = gradeList.value[0].unitId
      $emit('getUnit', gradeList.value[0])
    }
  })
  .finally(() => { })

function handleConfirm({ value, selectedItems }) {
  $emit('getUnit', selectedItems)
  //   model.value.hazardCategoryName = selectedItems.label
}
</script>

<style lang="scss" scoped></style>
