<template>
  <view class="container">
    <view class="infobox">
      <view class="infobox_title">
        <!-- <view class="fasticon"></view> -->
        <view>{{ info1Itme.createInfo?.hazardDescribe || '--' }}</view>
      </view>
      <view class="infoItme">
        <view class="infokey">隐患分类</view>
        <view class="infovalue">{{ info1Itme.createInfo?.essentialFactorClassName || '--' }}</view>
      </view>
      <view class="infoItme">
        <view class="infokey">隐患等级</view>
        <view class="infovalue">{{ info1Itme.createInfo?.essentialFactorGradeName || '--' }}</view>
      </view>
      <view class="infoItme">
        <view class="infokey">隐患来源</view>
        <view class="infovalue">{{ info1Itme.createInfo?.checkTypeName || '--' }}</view>
      </view>
      <view class="infoItme">
        <view class="infokey">任务发布人员</view>
        <view class="infovalue">
          <span>{{ info1Itme.createInfo?.createByName || '--' }}</span>
        </view>
      </view>
      <view class="infoItme">
        <view class="infokey">任务发布时间</view>
        <view class="infovalue">
          <span>{{ info1Itme.createInfo?.taskStartTime || '--' }}</span>
        </view>
      </view>
      <view class="infoItme">
        <view class="infokey">任务截止时间</view>
        <view class="infovalue">
          <span>{{ info1Itme.createInfo?.taskEndTime || '--' }}</span>
        </view>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
defineProps({
  info1Itme: {
    type: Object,
  },
})
</script>

<style lang="scss" scoped>
body {
  background-color: #f9f9f9 !important;
}

.infobox {
  padding-bottom: 1.125rem;
  margin-bottom: 1rem;
  background-color: white;

  .infobox_title {
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 0.25rem 0 0.9375rem 1.125rem;

    .fasticon {
      width: 0.25rem;
      height: 0.875rem;
      margin-right: 0.1875rem;
      background-color: #597bf7;
      border-radius: 1.125rem;
    }
  }

  .infoItme {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    padding: 0.5rem 1rem 0.5rem 1rem;
    font-size: 0.875rem;

    .infokey {
      min-width: 6.25rem;
      color: #7f7f7f;
    }

    .infovalue {
      width: 100%;
      text-align: left;
    }
  }
}

.fixed-bottom {
  position: fixed;
  bottom: 0;
  width: 100%;
  height: 3.375rem;
  line-height: 3.375rem;
  color: #fff;
  text-align: end;
  background-color: white;
  border-top: 0.3px solid #ccc;
}

.fileList {
  display: inline-block;
  width: 4.125rem;
  height: 4.125rem;
  margin: 0.5rem;
  text-align: left;
  border-radius: 0.375rem;
}

.info_img {
  display: flex;
  align-items: center;
  justify-content: start !important;
}
</style>
