<route lang="json5">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '隐患上报',
  },
}
</route>

<template>
  <SafetyNavbar title="隐患上报"></SafetyNavbar>
  <view class="h-100vh flex flex-col overflow-hidden">
    <scroll-view style="height: 75vh" scroll-y="true">
      <view>
        <div
          style="display: flex; padding: 8px; font-size: 14px; color: #999999; background: #f8f8f8"
        >
          <view class="required" style="width: 80px; min-width: 80px">所属任务</view>
          <view class="hidefont">
            {{ taskDec }}
          </view>
        </div>
        <div v-for="(form, index) in modelArray" :key="index">
          <view class="form-title" style="">
            <view class="form-title-icon" v-if="index !== 0"></view>
            <view
              style="
                display: flex;
                flex-direction: row;
                align-items: center;
                justify-content: space-between;
                width: 93%;
              "
            >
              <view style="margin-left: 4pt" v-if="index !== 0">隐患位置{{ index }}</view>
              <view v-if="index !== 0">
                <wd-button size="small" plain @click="delmodelArraybyindex(index)">删除</wd-button>
              </view>
            </view>
          </view>
          <wd-cell-group class="group" title="" border>
            <wd-form ref="form" @click="getindex(index)" :model="form">
              <!-- 隐患单位 -->
              <!-- <GetUnit @click="getindex(index)" @getUnit="getUnitvalue" :selectvalue="form.unitId" :taskid="taskId">
              </GetUnit> -->
              <!-- 隐患单位 -->
              <commonUnit
                @click="getindex(index)"
                @getGrade="getUnitvalue"
                :types="'1'"
                :selectvalue="form.unitId"
                :selecttext="form.unitName"
              ></commonUnit>
              <!-- <commonUnit @click="getindex(index)" @getGrade="getUnitvalue" :types="'1'"></commonUnit> -->
              <!-- 从隐患库中选择 -->
              <commonHazardDescribe
                @click="getindex(index)"
                :unitId="topUnitId"
              ></commonHazardDescribe>
              <view class="com-cell">
                <wd-textarea
                  type="textarea"
                  label-width="100px"
                  :maxlength="200"
                  show-word-limit
                  prop="remark"
                  clearable
                  auto-height
                  v-model="form.hazardDesc"
                  placeholder="请输入隐患描述"
                  @click="getindex(index)"
                />
              </view>
              <!-- <wd-select-picker :readonly="idreadonly" label="隐患分类" v-model="form.hazardType" :columns="EssentialFactor"
                align-right type="radio" placeholder="请选择隐患分类" @confirm="handleConfirm" value-key="id"
                label-key="className" required @click="getindex(index)"></wd-select-picker> -->
              <commonEssentialFactorNew
                @click="getindex(index)"
                :treeData="EssentialFactor"
                @sendEssen="sendEssen"
                :mrhazardType="form.hazardTypeName"
                :mrhazardTypeId="form.hazardType"
              ></commonEssentialFactorNew>
              <!-- 隐患分类结束 -->
              <!-- 隐患等级 -->
              <wd-select-picker
                @click="getindex(index)"
                z-index="999"
                label="隐患等级"
                placeholder="请选择隐患等级"
                v-model="form.hazardLevel"
                :columns="gradeList"
                value-key="id"
                label-key="gradeName"
                align-right
                type="radio"
                required
                @confirm="handleConfirm1"
              ></wd-select-picker>
              <!-- <commonGrade @click="getindex(index)" @getGrade="getGrade" :selectvalue="form.hazardLevel"></commonGrade> -->
              <wd-cell
                title="楼栋楼层"
                title-width="100px"
                :value="form.morehazardPlace"
                is-link
                @click="handleposition(index)"
                clickable
                required
              />
              <wd-cell-group border>
                <div style="display: flex">
                  <div style="padding-left: 15px; color: red">*</div>
                  <div style="padding-left: 5px; font-size: 14px">隐患位置</div>
                </div>
                <wd-input
                  style="padding-left: 20px; padding-right: 20px"
                  @click="getindex(index)"
                  label-width="100px"
                  prop="hazardDesc"
                  :maxlength="50"
                  required
                  show-word-limit
                  v-model="form.hazardPosition"
                  placeholder="请描述隐患详细位置"
                  use-prefix-slot
                >
                  <!-- <template #suffix>
                    <wd-icon name="location" @click="SelectXY(index)" color="#4d80f0" size="25px">
                      选点
                    </wd-icon>
                  </template> -->
                </wd-input>
              </wd-cell-group>
              <!-- <wd-input label="隐患位置" label-width="100px" prop="hazardDesc" :maxlength="50" required show-word-limit
                v-model="form.hazardPosition" placeholder="点击图标选择具体位置" use-prefix-slot>
                <template #suffix>
                  <wd-icon name="location" @click="SelectXY(index)" color="#4d80f0" size="25px">
                    选点
                  </wd-icon>
                </template>
              </wd-input> -->
              <wd-cell title="隐患图片" required />
              <view style="padding: 0rem 0rem 1rem 1rem">
                <uploadButton
                  @click="getindex(index)"
                  ref="uploadButtonRef"
                  :imginfo="form.files"
                  @getFileObjList="getFilelist"
                  @deleteImg="deleteImg"
                ></uploadButton>
              </view>
              <commonSelectpicker
                :title="'隐患整改人员'"
                :orgUnitId="form.unitId"
                :orgname="form.unitName || userinfo.unitName"
                :outUnitid="form.unitId"
                :status="1"
                @send="reception"
                @click="getindex(index)"
              ></commonSelectpicker>
              <view>
                <wd-datetime-picker
                  label="隐患整改期限"
                  type="date"
                  v-model="pickerDate"
                  required
                  :minDate="Date.now()"
                  @confirm="confirmDate($event, index)"
                ></wd-datetime-picker>
                <view class="deadline">
                  <text class="text-[#666] text-[12px] flex-shrink-0">快捷选项：</text>
                  <wd-radio-group
                    shape="button"
                    v-model="deadDate"
                    size="small"
                    @change="confirmShort($event, index)"
                    class="flex-1 flex justify-around"
                  >
                    <wd-radio :value="7">一周</wd-radio>
                    <wd-radio :value="14">两周</wd-radio>
                    <wd-radio :value="30">一个月</wd-radio>
                    <wd-radio :value="60">两个月</wd-radio>
                  </wd-radio-group>
                </view>
              </view>

              <!-- <commonSelectpicker :title="'隐患整改人员1'" :orgUnitId="form.unitId" :status="1" @send="reception"
                @click="getindex(index)">
              </commonSelectpicker>-->
              <wd-textarea
                label="备注"
                type="textarea"
                label-width="100px"
                :maxlength="500"
                show-word-limit
                prop="remark"
                clearable
                v-model="form.remark"
                placeholder="此处填写隐患相关备注的内容"
              />
            </wd-form>
          </wd-cell-group>
        </div>
        <view class="add-hazard" @click="addfrom">新增隐患</view>
        <!-- <info1 v-if="pagetype === 1" :info1Itme="info"></info1> -->
        <view class="fixed-bottom">
          <wd-button type="info" size="large" @click="goBack" block>取消</wd-button>
          <wd-button type="primary" size="large" :loading="loading" @click="handleSubmit" block>
            保存
          </wd-button>
        </view>
        <!-- </scroll-view> -->
      </view>
      <view class="container-placeholder"></view>
    </scroll-view>
  </view>
</template>

<script lang="ts" setup>
import SafetyNavbar from '../../components/safety-navbar.vue'
import customForm from './custom-form.vue'
import commonEssentialFactorNew from '@/components/commonSelect/common-EssentialFactorNew.vue'
import commonSelectpicker from '@/components/commonSelect/common-selectpicker-new.vue'
import commonHazardDescribe from '@/components/commonSelect/common-hazardDescribe.vue'
import commonGrade from '@/components/commonSelect/common-Grade.vue'
// import commonUnit from '@/components/commonSelect/common-Unit.vue'
import commonUnit from '@/components/commonSelect/common-Unit.vue'
import GetUnit from '../components/GetUnit.vue'
import uploadButton from '@/components/upload/upload-button.vue'
import { addForms } from '../type'
import { ref } from 'vue'
// import {
//   inspectDetail,
//   posthazardEssentialFactorClassAPI,
//   posthazardPlanTaskEventaddAPI,
//   posthazardRandomCheckDetailAPI,
//   posthazardRecordAddEventAPI,
// } from '../featch'
import { useUserStore } from '@/store'
import { debounce } from '@/utils'
import { posthazardEssentialFactorClassAPI, posthazardRecordAddEventAPI } from '../featch'
import { postTopLevelOrgCodeAPI } from '@/pages/randomcheck/featch'
import { posthazardGradeAPI } from '@/components/commonSelect/featch'
import dayjs from 'dayjs'

const pickerDate = ref<number>(dayjs().valueOf()) // timestamp
const deadDate = ref<number | undefined>(undefined) // timestamp

const loading = ref(false)
const userStore = useUserStore()
const userinfo: any = userStore.userInfo ? userStore.userInfo : {}
// console.log(userinfo, '========userinfo')
const checkId = ref<any>('')
const taskId = ref<any>('')
const getplanId = ref<any>('')
const modelArray = ref<addForms<any>[]>([])
const uploadButtonRef = ref()
const FactorClassFullName = ref('') // 从隐患库中选择的分类
const model = ref<addForms<any>>({
  buildingId: '',
  createBy: userinfo.id,
  createTime: '',
  disposeId: '',
  disposeState: '0',
  disposeStateName: '待整改',
  endTime: '',
  essentialFactorClassItemId: '',
  checkAreaEssentialFactorId: '',
  eventTime: '',
  files: [],
  floorId: '',
  hazardDesc: '',
  hazardLevel: '',
  hazardLevelName: '',
  hazardPosition: '',
  hazardSource: 22, // 检查id为空 类型为人工上报
  hazardSourceName: '',
  hazardType: null,
  hazardTypeName: '',
  latitude: 0,
  longitude: 0,
  mapX: 0,
  mapY: 0,
  mapZ: 0,
  randomCheckId: '',
  reformUserJson: [],
  remark: '',
  search: '',
  startTime: '',
  superviseUnitId: userinfo.deptId,
  superviseUnitName: userinfo.deptName,
  unitId: '',
  unitName: '',
  updateBy: '',
  zhId: userinfo.zhId,
  timeoutDays: 0,
  id: '',
  morehazardPlace: '',
  planId: '',
  createByName: userinfo.userName,
  taskId: '',
  hazardRandomCheckEventUsers: [],
  haveOneFindThree: '',
  correctionTime: dayjs().format('YYYY-MM-DD'), // 整改期限
})
const topUnitId = ref<string>('')
const taskDec = ref<string>('')
onLoad((params) => {
  // if (Object.keys(params).length !== 0) {
  taskId.value = params.taskId
  taskDec.value = params.dec
  // 获取隐患上报来源
  model.value.hazardSource = 22
  model.value.taskId = taskId.value
  model.value.haveOneFindThree = taskId.value
  if (userinfo.unitOrgType === '1') {
    model.value.unitId = userinfo.unitId
    model.value.unitName = userinfo.unitName
    getGradeAndLeve(userinfo.unitId)
  }
  // 获取所属任务的详情
  if (params.topUnitId) {
    topUnitId.value = params.topUnitId
  }
  nextTick(() => {
    modelArray.value.push(model.value)
  })
  // }
})

const descmodel = ref<any>({})
// function getcheckinfo(params) { }
// 新增隐患
function addfrom() {
  modelArray.value.push({
    buildingId: '',
    createBy: userinfo.id,
    createTime: '',
    disposeId: '',
    disposeState: '0',
    disposeStateName: '待整改',
    endTime: '',
    essentialFactorClassItemId: modelArray.value[0].essentialFactorClassItemId,
    checkAreaEssentialFactorId: modelArray.value[0].checkAreaEssentialFactorId,
    eventTime: '',
    files: [],
    floorId: '',
    hazardDesc: modelArray.value[0].hazardDesc,
    hazardLevel: modelArray.value[0].hazardLevel,
    hazardLevelName: '',
    hazardPosition: '',
    hazardSource: model.value.hazardSource,
    hazardSourceName: '',
    hazardType: modelArray.value[0].hazardType,
    hazardTypeName: modelArray.value[0].hazardTypeName,
    latitude: 0,
    longitude: 0,
    mapX: null,
    mapY: null,
    mapZ: 0,
    randomCheckId: modelArray.value[0].randomCheckId,
    reformUserJson: [],
    remark: '',
    search: '',
    startTime: '',
    superviseUnitId: userinfo.deptId,
    superviseUnitName: userinfo.deptName,
    unitId: modelArray.value[0].unitId,
    unitName: modelArray.value[0].unitName,
    updateBy: '',
    zhId: userinfo.zhId,
    timeoutDays: 0,
    id: '',
    morehazardPlace: '',
    planId: getplanId.value,
    createByName: userinfo.userName,
    taskId: taskId.value,
    hazardRandomCheckEventUsers: [],
    haveOneFindThree: modelArray.value[0].haveOneFindThree,
    correctionTime: modelArray.value[0].correctionTime || dayjs().format('YYYY-MM-DD'), // 整改期限
  })
}

// 隐患等级
const gradeList = ref<any>([])
function getgradeList(unitIdval) {
  // console.log(unitIdval, '================调用获取隐患等级')
  posthazardGradeAPI({ unitId: unitIdval })
    .then((res) => {
      gradeList.value = res.data as any[]
    })
    .finally(() => {})
}
// 调用获取隐患分类
const EssentialFactor = ref<any>([])
function gethazardEssentialFactorClass(unitIdval) {
  // console.log(unitIdval, '================调用获取隐患分类')
  posthazardEssentialFactorClassAPI({ unitId: unitIdval, parentId: '0' })
    .then((res: any) => {
      EssentialFactor.value = res.data
      // // console.log(EssentialFactor.value, '=========================res.data')
    })
    .finally(() => {
      // console.log(111)
    })
}

const indexnum: any = ref(0)
onShow(() => {
  if (uni.getStorageSync('checkFormIndex')) {
    indexnum.value = uni.getStorageSync('checkFormIndex')
  }
  if (modelArray.value.length > 0) {
    if (uni.getStorageSync('FactorClassItem')) {
      // 调用子组件方法
      // 检查项id
      // checkAreaEssentialFactorId  checkAreaEssentialFactorId
      modelArray.value[indexnum.value].essentialFactorClassItemId =
        uni.getStorageSync('FactorClassItem').essentialFactorClassItemId
      // 列表和详情返回字段不同 （从列表点击详情上报/从列表直接上报）
      modelArray.value[indexnum.value].hazardDesc = modelArray.value[indexnum.value].hazardDesc
        ? modelArray.value[indexnum.value].hazardDesc
        : uni.getStorageSync('FactorClassItem').hazardDescribe
      // 隐患分类iD
      modelArray.value[indexnum.value].hazardType =
        uni.getStorageSync('FactorClassItem').essentialFactorClassId

      modelArray.value[indexnum.value].hazardTypeName =
        uni.getStorageSync('FactorClassItem').essentialFactorClassName ||
        uni.getStorageSync('FactorClassItem').essentialFactorClassFullName
      // modelArray.value[indexnum.value].hazardTypeName =
      //   uni.getStorageSync('FactorClassItem').essentialFactorClassName
      // 从隐患库中带出隐患分类
      FactorClassFullName.value = uni.getStorageSync('FactorClassItem').essentialFactorClassName
      // 隐患等级id
      modelArray.value[indexnum.value].hazardLevel = modelArray.value[indexnum.value].hazardLevel
        ? modelArray.value[indexnum.value].hazardLevel
        : uni.getStorageSync('FactorClassItem').hazardGradeId
    }
    if (uni.getStorageSync('Building') && uni.getStorageSync('BuildingFool')) {
      // 添加楼栋楼层id
      modelArray.value[indexnum.value].floorId = uni.getStorageSync('Building').value
      modelArray.value[indexnum.value].buildingId = uni.getStorageSync('Building').parentId
      // modelArray.value[indexnum.value].hazardPosition =
      // uni.getStorageSync('BuildingFool').label + uni.getStorageSync('Building').label
      modelArray.value[indexnum.value].morehazardPlace =
        uni.getStorageSync('BuildingFool').label + uni.getStorageSync('Building').label
    }
    if (uni.getStorageSync('pointerinfo')) {
      modelArray.value[indexnum.value].mapX = uni.getStorageSync('pointerinfo').x
      modelArray.value[indexnum.value].mapY = uni.getStorageSync('pointerinfo').y
    }
  }
})
/* 获取位置 */
function handleposition(index) {
  indexnum.value = index
  if (modelArray.value[indexnum.value].unitId) {
    uni.navigateTo({
      url: `/pages/hazardPhoto/common-position?selunitid=${modelArray.value[indexnum.value].unitId}`,
    })
  } else {
    uni.showToast({
      icon: 'none',
      title: '请先选择隐患单位',
    })
  }
}
// 获取图片列表
function getFilelist(event) {
  setTimeout(() => {
    indexnum.value = uni.getStorageSync('checkFormIndex')
    modelArray.value[indexnum.value].files = event
  }, 100)
}
function deleteImg() {
  // console.log(modelArray.value[indexnum.value].files)
}
function handleConfirm({ value, selectedItems }) {
  modelArray.value[indexnum.value].hazardType = selectedItems.id
  modelArray.value[indexnum.value].hazardTypeName = selectedItems.className
}

const getindex = (index: number) => {
  uni.setStorageSync('checkFormIndex', index)
  indexnum.value = index
}
// 分类
function sendEssen(node) {
  console.log(node, '获取到的值')
  modelArray.value[indexnum.value].hazardType = node.id
  modelArray.value[indexnum.value].hazardTypeName = node.className
}
// 获取选择的隐患等级
// function getGrade(event) {
//   modelArray.value[indexnum.value].hazardLevel = event.id
// }

const idreadonly = ref(true)
// 调用获取隐患等级和分类
function getGradeAndLeve(code) {
  idreadonly.value = false
  postTopLevelOrgCodeAPI({ orgCode: code })
    .then((_res: any) => {
      // console.log(_res.data.orgCode, '=========================topUnitId')
      gethazardEssentialFactorClass(_res.data.orgCode)
      getgradeList(_res.data.orgCode)
    })
    .catch(() => {
      // console.log(111)
    })
}

const getUnitvalue = (data) => {
  // 把选中的单位id传给隐患整改人员接口
  console.log(data, '====')
  // console.log(modelArray.value[indexnum.value].unitId, '==========2222=====' + data.unitId)
  if (modelArray.value[indexnum.value].unitId !== data.id) {
    getGradeAndLeve(data.id)
    uni.removeStorageSync('Building')
    uni.removeStorageSync('pointerinfo')
    uni.removeStorageSync('BuildingFool')
    modelArray.value[indexnum.value].floorId = ''
    modelArray.value[indexnum.value].buildingId = ''
    modelArray.value[indexnum.value].hazardPosition = ''
    modelArray.value[indexnum.value].morehazardPlace = ''
    modelArray.value[indexnum.value].mapX = null
    modelArray.value[indexnum.value].mapY = null
  }
  modelArray.value[indexnum.value].unitId = data.id
  modelArray.value[indexnum.value].unitName = data.text
}
// 获取整改人
function reception(event) {
  modelArray.value[indexnum.value].reformUserJson = convertToreformUserJson(
    JSON.parse(JSON.stringify(event)),
  )
}

// 确定时间
function confirmDate(e: { value: any }, index: number) {
  deadDate.value = undefined
  modelArray.value[index].correctionTime = dayjs(e.value).format('YYYY-MM-DD')
}
// 快捷选项
function confirmShort(e: { value: any }, index: number) {
  if (e.value) {
    pickerDate.value = dayjs().add(e.value, 'day').valueOf()
    modelArray.value[index].correctionTime = dayjs().add(e.value, 'day').format('YYYY-MM-DD')
  }
}
// 取消上报
function goBack() {
  uni.navigateBack()
}
// 选点
function SelectXY(index) {
  if (modelArray.value[index].buildingId) {
    // 隐患上报取orgUnitId   随机检查取model.value.unitId
    const id = modelArray.value[index].unitId
    uni.navigateTo({
      url: `/pages/map/index?status=1&unitId=${id}&buildId=${modelArray.value[index].buildingId}&floorId=${modelArray.value[index].floorId}&x=${modelArray.value[index].mapX}&y=${modelArray.value[index].mapY}`,
    })
  } else {
    uni.showToast({
      icon: 'none',
      title: '请先选择楼栋楼层',
    })
  }
}

// 删除新增隐患
function delmodelArraybyindex(index) {
  modelArray.value.splice(index, 1)
}
// watch(
//   modelArray.value[indexnum.value].unitId,
//   () => {
//     // console.log(modelArray.value[indexnum.value].unitId, "第" + index + "条数据unitId改变")
//   },
//   {
//     deep: true,
//   },
// )

/* 提交按钮 */
const handleSubmit = debounce(() => {
  console.log(modelArray.value)
  loading.value = true
  // // 非空判断======开始
  for (let i = 0; i < modelArray.value.length; i++) {
    if (!modelArray.value[i].hazardDesc) {
      uni.showToast({
        icon: 'none',
        title: '请输入隐患描述',
      })
      loading.value = false
      return
    }
    if (!modelArray.value[i].hazardType) {
      uni.showToast({
        icon: 'none',
        title: '请选择隐患分类',
      })
      loading.value = false
      return
    }
    if (!modelArray.value[i].hazardLevel) {
      uni.showToast({
        icon: 'none',
        title: '请选择隐患等级',
      })
      loading.value = false
      return
    }
    if (!modelArray.value[i].hazardPosition || !modelArray.value[i].morehazardPlace) {
      uni.showToast({
        icon: 'none',
        title: '请输入隐患位置',
      })
      loading.value = false
      return
    }
    if (!modelArray.value[i].unitId) {
      uni.showToast({
        icon: 'none',
        title: '请输入隐患单位',
      })
      loading.value = false
      return
    }
    if (modelArray.value[i].mapX === null || modelArray.value[i].mapY === null) {
      uni.showToast({
        icon: 'none',
        title: '请选择点位',
      })
      loading.value = false
      return
    }
    if (modelArray.value[i].files.length === 0) {
      uni.showToast({
        icon: 'none',
        title: '请选择上传隐患图片',
      })
      loading.value = false
      return
    }
    if (modelArray.value[i].reformUserJson.length === 0) {
      uni.showToast({
        icon: 'none',
        title: '请选择整改人员',
      })
      loading.value = false
      return
    }
    if (!modelArray.value[i].correctionTime) {
      uni.showToast({
        icon: 'none',
        title: '请选择隐患整改期限',
      })
      loading.value = false
      return
    }
  }
  posthazardRecordAddEventAPI(JSON.parse(JSON.stringify(modelArray.value)))
    .then((res: any) => {
      if (res.code === 'error') {
        uni.showToast({
          icon: 'none',
          title: res.message || '请求错误',
        })
        return
      }
      uni.removeStorageSync('FactorClassItem')
      uni.removeStorageSync('Building')
      uni.removeStorageSync('pointerinfo')
      uni.removeStorageSync('BuildingFool')
      uni.showToast({
        icon: 'none',
        title: '添加成功',
      })
      goBack()
    })
    .catch(() => {
      uni.showToast({
        icon: 'none',
        title: '网络错误',
      })
      loading.value = false
    })
    .finally(() => {
      loading.value = false
    })
}, 500)
function convertToreformUserJson(items) {
  return items.map((item) => ({
    // reformUserName: item.userName,
    // reformUserId: item.id,
    // reformType: 0,
    reformUserName: item.userName || item.name,
    reformUserId: item.usertype === 2 ? item.loginUserId : item.id,
    reformType: 0,
    // 用户类型（1：组织内人员；2：承租方人 ;3-相关方)
    userType: item.usertype,
    // 相关方/承租方---字段名可能不一样,在选择人员模块添加好
    userUnitId: item.unitId,
    userUnitName: item.unitName,
    //  组织机构传unitId,unitName
    userMasterUnitId: item.usertype === 1 ? item.unitId : item.userMasterUnitId,
    userMasterUnitName: item.usertype === 1 ? item.unitName : item.userMasterUnitName,
  }))
}

onUnload(() => {
  uni.removeStorageSync('FactorClassItem')
  uni.removeStorageSync('Building')
  uni.removeStorageSync('pointerinfo')
  uni.removeStorageSync('BuildingFool')
})
</script>
<style lang="scss">
::v-deep {
  .wd-input__label.is-required {
    padding-left: 7px;
  }

  .wd-cell__value {
    font-size: 0.8rem;
    color: #999999 !important;
  }

  .wd-button.is-block {
    width: 100% !important;
    height: 100% !important;
    border-radius: 0 !important;
  }

  .wd-cell__value {
    font-size: 0.8rem;
    color: #999999 !important;
  }

  .text .wd-cell__value {
    color: rgba($color: #000000, $alpha: 0.8) !important;
  }

  .wd-button.is-block {
    width: 100% !important;
    height: 100% !important;
    border-radius: 0 !important;
  }

  .wd-col-picker__cell.is-align-right .wd-col-picker__value {
    width: 100px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .wd-select-picker__value--placeholder {
    font-size: 13.312px;
  }
}

.FactorClassFullNamecalss {
  ::v-deep {
    .wd-select-picker__value--placeholder {
      color: black !important;
    }

    .wd-col-picker__value--placeholder {
      color: black !important;
    }
  }
}

.required::before {
  // position: absolute;
  // left: 0;
  top: 0.125rem;
  line-height: 1.1;
  color: #fa4350;
  content: '*';
}

.deadline {
  @apply flex justify-between items-center flex-wrap;
  margin: 0 1rem 0 1rem;

  :deep(.wd-radio.is-button .wd-radio__label) {
    height: 1.5rem;
    line-height: 1.5rem;
    padding: 0 0.8rem;
  }

  padding-bottom: 0.8rem;
  border-bottom: 1px solid #e5e7eb;
}

.com-cell {
  margin: 0 1rem 0 1rem;
  // border: 0.0187rem solid #ccc;
}

.fixed-bottom {
  position: fixed;
  bottom: 0;
  display: flex;
  flex-direction: row;
  align-items: center;
  width: 100%;
  height: 3.375rem;
  line-height: 3.375rem;
  color: #fff;
  text-align: center;
  background-color: white;
  z-index: 6;
}

.add-hazard {
  width: 96vw;
  height: 35px;
  margin: 10px auto 20px;
  line-height: 35px;
  color: #fff;
  text-align: center;
  background-color: #0256f7;
  border-radius: 6px;
}

.container-placeholder {
  width: 100%;
  height: 3.375rem;
}

.select-address {
  display: flex;
  justify-content: flex-end;
  padding-right: 10px;
  margin: 10px 0px;

  .select-address-button {
    padding: 8px 14px;
    color: #fff;
    background-color: #597bf7;
    border-radius: 10px;
  }
}
</style>
<!-- <style lang="scss" scoped>
::v-deep {
  .wd-cell__value {
    font-size: 0.8rem;
    color: #999999 !important;
  }

  .wd-button.is-block {
    width: 100% !important;
    height: 100% !important;
    border-radius: 0 !important;
  }
}

.com-cell {
  margin: 0 1rem 0 1rem;
  border: 0.0187rem solid #ccc;
}

.fixed-bottom {
  position: fixed;
  bottom: 0;
  display: flex;
  flex-direction: row;
  align-items: center;
  width: 100%;
  height: 3.375rem;
  line-height: 3.375rem;
  color: #fff;
  text-align: center;
  background-color: white;
}

.container-placeholder {
  width: 100%;
  height: 3.375rem;
}

::v-deep {
  .wd-cell__value {
    font-size: 0.8rem;
    color: #999999 !important;
  }

  .wd-button.is-block {
    width: 100% !important;
    height: 100% !important;
    border-radius: 0 !important;
  }

  .wd-col-picker__cell.is-align-right .wd-col-picker__value {
    width: 100px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.FactorClassFullNamecalss {
  ::v-deep {
    .wd-select-picker__value--placeholder {
      color: black !important;
    }

    .wd-col-picker__value--placeholder {
      color: black !important;
    }
  }
}

.required::before {
  // position: absolute;
  // left: 0;
  top: 0.125rem;
  line-height: 1.1;
  color: #fa4350;
  content: '*';
}

.com-cell {
  margin: 0 1rem 0 1rem;
  // border: 0.0187rem solid #ccc;
}

.fixed-bottom {
  position: fixed;
  bottom: 0;
  z-index: 2;
  display: flex;
  flex-direction: row;
  align-items: center;
  width: 100%;
  height: 3.375rem;
  line-height: 3.375rem;
  color: #fff;
  text-align: center;
  background-color: white;
}

.form-title {
  display: flex;
  align-items: center;
  padding-top: 0.625rem;
  padding-left: 0.875rem;

  .form-title-icon {
    width: 4pt;
    height: 14pt;
    background-color: #597bf7;
    border-radius: 18pt;
  }
}

.select-address {
  display: flex;
  justify-content: flex-end;
  padding-right: 10px;
  margin: 10px 0px;

  .select-address-button {
    padding: 8px 14px;
    color: #fff;
    background-color: #597bf7;
    border-radius: 10px;
  }
}

.add-hazard {
  width: 96vw;
  height: 35px;
  margin: 10px auto 20px;
  line-height: 35px;
  color: #fff;
  text-align: center;
  background-color: #0256f7;
  border-radius: 6px;
}
</style> -->
