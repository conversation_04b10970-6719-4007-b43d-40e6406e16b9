<route lang="json5">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '隐患上报',
  },
}
</route>

<template>
  <SafetyNavbar title="隐患上报"></SafetyNavbar>
  <view class="h-100vh flex flex-col overflow-hidden">
    <scroll-view style="height: 75vh" scroll-y="true">
      <customForm></customForm>
      <view class="container-placeholder"></view>
    </scroll-view>
  </view>
</template>

<script lang="ts" setup>
import SafetyNavbar from '../../components/safety-navbar.vue'
import customForm from './custom-form.vue'
// const syscode = ref('')

onUnload(() => {
  uni.removeStorageSync('FactorClassItem')
  uni.removeStorageSync('Building')
  uni.removeStorageSync('pointerinfo')
  uni.removeStorageSync('BuildingFool')
})
</script>

<style lang="scss">
::v-deep {
  .wd-cell__value {
    font-size: 0.8rem;
    color: #999999 !important;
  }

  .wd-button.is-block {
    width: 100% !important;
    height: 100% !important;
    border-radius: 0 !important;
  }
}

.com-cell {
  margin: 0 1rem 0 1rem;
  border: 0.0187rem solid #ccc;
}

.fixed-bottom {
  position: fixed;
  bottom: 0;
  display: flex;
  flex-direction: row;
  align-items: center;
  width: 100%;
  height: 3.375rem;
  line-height: 3.375rem;
  color: #fff;
  text-align: center;
  background-color: white;
}

.container-placeholder {
  width: 100%;
  height: 3.375rem;
}
</style>
