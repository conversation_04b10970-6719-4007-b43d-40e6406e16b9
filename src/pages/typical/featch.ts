import { $api } from '@/api'
import { http } from '@/utils/http'

/* 整改记录 dispose/record */
export const postrecordAPI = (Parameters: any) => {
  return http.post($api.type.hazard + '/dispose/record', {
    ...Parameters,
  })
}
/** post 请求 获取隐患分类列表 hazardEssentialFactorClass/treeList   */
export const posthazardEssentialFactorClassAPI = (Parameters: any) => {
  return http.post($api.type.hazard + '/hazardEssentialFactorClass/treeList', {
    ...Parameters,
  })
}

/** post 请求 类比排查任务 hazardHaveOneFindThree/getOneToThreePage 、app=/hazardHaveOneFindThree/getOneToThreePageApp   */
export const postgetOneToThreePageAPI = (Parameters: any) => {
  return http.post($api.type.hazard + '/hazardHaveOneFindThree/getOneToThreePageApp', {
    ...Parameters,
  })
}

// 隐患详情 flag=2 /hazardMeger/queryDetail
export const posthazardMegerqueryDetailAPI = (Parameters: any) => {
  return http.post(
    $api.type.hazard + '/hazardMeger/queryDetail?id=' + Parameters.id + '&flag=' + Parameters.flag,
    {
      ...Parameters,
    },
  )
}

// 类比排查任务-数据统计/hazardMeger/statistics
export const posthazardMegerStatisticsAPI = (Parameters: any) => {
  return http.post($api.type.hazard + '/hazardMeger/statistics', {
    ...Parameters,
  })
}

// 隐患列表-/hazardRecord/pageEvent
export const posthazardMegerpageEventAPI = (Parameters: any) => {
  return http.post($api.type.hazard + '/hazardMeger/pageEvent', {
    ...Parameters,
  })
}

/* post 隐患上报 /hazardRecord/addEvent */
export const posthazardRecordAddEventAPI = (Parameters: any) => {
  return http.post($api.type.hazard + '/hazardRecord/addEvent', Parameters)
}

// /hazardHaveOneFindThree/getOneToThreeDetail
export const postgetOneToThreeDetailAPI = (Parameters: string) => {
  return http.post(
    $api.type.hazard + '/hazardHaveOneFindThree/getOneToThreeDetail?id=' + Parameters,
  )
}

// 获取下级单位
export const getAllUnit = (Parameters: any) => {
  return http.post($api.type.hazard + '/ehsUpms/getAllUnit', { ...Parameters })
}

// 获取下级单位 /hazardHaveOneFindThree/getUnitListByTaskId
export const getUnitListByTaskIdAPI = (Parameters: any) => {
  return http.post(
    $api.type.hazard +
      '/hazardHaveOneFindThree/getUnitListByTaskId?id=' +
      Parameters.id +
      '&type=' +
      Parameters.type,
    { ...Parameters },
  )
}
// export const getDownUnit = (Parameters: any) => {
//   return http.post('/ehsUpms/getAllUnit', {
//     ...Parameters,
//   })
// }
