<route lang="json5">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '任务详情',
  },
}
</route>
<template>
  <view class="h-100vh flex flex-col overflow-hidden" style="background: #efefef">
    <SafetyNavbar title="任务详情"></SafetyNavbar>
    <view class="content">
      <wd-tabs @click="handleClick">
        <block v-for="item in tabs" :key="item">
          <wd-tab :title="`${item}`"></wd-tab>
        </block>
      </wd-tabs>
      <view v-if="activeIndex === 0">
        <scroll-view style="height: 90vh" scroll-y="true">
          <!-- 其它类型 -->
          <basicinfo v-if="taskType == '1'" :info1Itme="hazardInfo"></basicinfo>
          <!-- 典型频发问题 -->
          <typicalinfo v-else :info1Itme="hazardInfo"></typicalinfo>
          <view style="background-color: white">
            <CustomTabs
              v-if="taskType == '1'"
              :tabs="tabs1"
              :activeIndex="activeIndex1"
            ></CustomTabs>
            <div class="tableitme" v-if="activeIndex1 === 0 && taskType == '1'">
              <changerecord ref="updaterecord" :disposeId="disposeId"></changerecord>
            </div>
          </view>
        </scroll-view>
      </view>
      <view v-if="activeIndex === 1">
        <analogyinfo ref="updateinfo" :taskId="taskId"></analogyinfo>
      </view>
    </view>
    <!--zyl修改-类比排查情况tab页不显示按钮---2024-12-19-- by:wj -->
    <view
      v-if="new Date(createInfo.taskEndTime) >= new Date() && activeIndex == 0"
      class="fixed-bottom"
    >
      <wd-button type="primary" @click="goTodanger" block>上报类比排查隐患</wd-button>
    </view>
  </view>
</template>
<script lang="ts" setup>
import SafetyNavbar from '../components/safety-navbar.vue'
import CustomTabs from '../components/custom-Tabs.vue'
import basicinfo from './components/basicinfo.vue'
import changerecord from './components/change_record.vue'
import analogyinfo from './components/analogyinfo.vue'
import typicalinfo from './components/typicalinfo.vue'
import { postgetOneToThreeDetailAPI, posthazardMegerqueryDetailAPI } from './featch'
const tabs = ['隐患基本信息', '类比排查情况']
const updaterecord = ref()
const updateinfo = ref()
const activeIndex = ref(0)
function handleClick(event) {
  activeIndex.value = event.index
}
const activeIndex1 = ref(1)
const tabs1 = ['整改记录']
// 隐患基本信息
const hazardInfo = ref({})
// 从列表带过来的创建人信息以及任务开始时间和结束时间
const createInfo = ref({})
const hazardRandomCheckEventId = ref('') // 隐患id
const taskType = ref('') // 任务类型 1-类比 2-典型频发问题
const disposeId = ref('') // 整改记录id

const taskId = ref('') // 任务id
onLoad(async (pamars) => {
  taskId.value = pamars.id
  uni.setStorageSync('taskIdByindex', taskId.value)
  // 隐患id
  hazardRandomCheckEventId.value = pamars.hazardRandomCheckEventId
  taskType.value = pamars.taskType
  await getOneToThreeDetail()
  if (taskType.value === '1') {
    getDetail()
  }
})
// 获取隐患详情
function getDetail() {
  posthazardMegerqueryDetailAPI({
    id: hazardRandomCheckEventId.value,
    flag: '2',
  }).then((res: any) => {
    hazardInfo.value = res.data
    hazardInfo.value.createInfo = createInfo.value
    // files
    if (hazardInfo.value.files) {
      hazardInfo.value.files = hazardInfo.value.files.map((item) => {
        const lastindex = item.fileUrl.lastIndexOf('.')
        const _res = item.fileUrl.substring(lastindex + 1)
        // (_res.length + 1)
        return {
          ...item,
          _fileUrl: `${item.fileUrl.slice(0, item.fileUrl.length - (_res.length + 1))}_160X160${item.fileUrl.slice(item.fileUrl.length - (_res.length + 1), item.fileUrl.length)}`,
        }
      })
    }
    disposeId.value = res.data.disposeId
    activeIndex1.value = 0
    // // console.log(hazardInfo.value, '=================hazardInfo.value222')
  })
}
// 获取典型详情
function getOneToThreeDetail() {
  postgetOneToThreeDetailAPI(taskId.value).then((res: any) => {
    createInfo.value = res.data
    if (taskType.value !== '1') {
      hazardInfo.value.createInfo = createInfo.value
    }
    // // console.log(hazardInfo.value, '=================hazardInfo.value111')
  })
}
// 上报类比排查隐患
const unitList = ref([])
function goTodanger() {
  // // console.log(hazardInfo.value)
  // unitList.value = hazardInfo.value.unitName.split(',').map((item, index) => {
  //   return {
  //     unitName: item,
  //     id: hazardInfo.value.unitId.split(',')[index],
  //   }
  // })
  // uni.setStorageSync('unitList', unitList.value)
  // hazardDescribe
  const hazardDescribe = hazardInfo.value.hazardDesc || hazardInfo.value.createInfo.hazardDescribe
  // // console.log(hazardDescribe)
  uni.navigateTo({
    url:
      '/pages/typical/add/index?taskId=' +
      hazardInfo.value.createInfo.id +
      '&dec=' +
      hazardDescribe +
      '&topUnitId=' +
      hazardInfo.value.createInfo.topUnitId,
  })
}
</script>
<style lang="scss" scoped>
::v-deep {
  .wd-button.is-medium.is-round {
    width: 100%;
    border-radius: 0;
  }
}

.fixed-bottom {
  position: fixed;
  bottom: 0;
  z-index: 33;
  display: flex;
  flex-direction: row;
  align-items: center;
  width: 100%;
  height: 3.375rem;
  color: #fff;
  text-align: center;
  background-color: white;
}
</style>
