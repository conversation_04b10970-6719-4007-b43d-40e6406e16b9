<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: 'Tabs',
  },
}
</route>

<template>
  <view class="w-full">
    <wd-tabs
      v-bind="$attrs"
      :sticky="true"
      color="#0256FF"
      inactiveColor="#969696"
      offset-top="11rem"
    >
      <block v-for="item in tabsData" :key="item.id">
        <wd-tab :title="item.title">
          <view class="h-full">
            <Identify v-show="item.id == 1"></Identify>
            <SafetyMeasure v-show="item.id == 2"></SafetyMeasure>
          </view>
        </wd-tab>
      </block>
    </wd-tabs>
  </view>
</template>

<script setup lang="ts">
import Identify from '../danger/components/identify.vue'
import SafetyMeasure from '../danger/components/safety-measure.vue'

defineProps({
  tabsData: {
    type: Array<{
      id: number
      title: string
    }>,
    default: [],
  },
})
</script>

<style lang="scss" scoped>
::v-deep {
  .wd-tabs__line {
    background: #0256ff !important;
  }
}
</style>
