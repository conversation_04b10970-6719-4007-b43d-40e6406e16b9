<!--
 * @Author: <EMAIL> <EMAIL>
 * @Date: 2024-12-18 11:19:44
 * @LastEditors: <EMAIL> <EMAIL>
 * @LastEditTime: 2024-12-20 09:45:12
 * @FilePath: /hazard-mgr/src/pages/components/custom-Tabs.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <view class="tabs-header">
    <view
      v-for="(item, index) in tabs"
      :key="index"
      class="tabs-header-item"
      :class="{ 'tabs-header-active': activeIndex === index }"
      @click="handleClick(index)"
    >
      {{ item }}
      <view :class="{ 'tabs-header-item-active': activeIndex === index }"></view>
    </view>
  </view>
</template>

<script lang="ts" setup>
// const tabs = ['计划任务', '我执行的任务']
const props = defineProps({
  tabs: {
    type: Array,
  },
  activeIndex: {
    type: Number,
    default: 0,
  },
})
const activeIndex = ref(props.activeIndex)

const $emit = defineEmits(['handleClick'])

function handleClick(index) {
  activeIndex.value = index
  $emit('handleClick', index)
}
// const handleClick = (index: number) => {
//   activeIndex.value = index
// }
</script>

<style lang="scss">
.tabs {
  display: flex;
  flex-direction: column;
}

.tabs-header {
  display: flex;
  // background-color: #5783f4;
  margin-top: -2px;
  // padding-bottom: 1rem;

  // background-image: url(/aqsc/v1/hazard/h5/src/static/safety/image/work_bg.png);
  // background-size: 100%, 100%;
}

.tabs-header-active {
  color: #0256ff;
}

.tabs-header-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 10px;
  cursor: pointer;
}

.tabs-header-item-active {
  width: 40px;
  height: 4px;
  margin-top: 6px;
  background-color: #0256ff;
  border-radius: 1px;
}
</style>
