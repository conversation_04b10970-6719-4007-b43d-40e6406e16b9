<template>
  <view class="selectprincipal">
    <view>{{ title }}</view>
    <view class="principalcontent">
      <view
        v-for="(itme, index) in selectedItem"
        :key="index"
        @click="delprincipal(itme.checkUserId)"
        class="principal_itme"
      >
        <view>{{ itme.checkUserName }}</view>
        <view><wd-icon name="error-fill" color="#0256FF"></wd-icon></view>
      </view>
      <wd-select-picker
        v-model="value"
        use-default-slot
        value-key="checkUserId"
        label-key="checkUserName"
        :columns="columns"
        filterable
        @confirm="handleConfirm"
      >
        <wd-button icon="add" plain hairline></wd-button>
      </wd-select-picker>
    </view>
  </view>
</template>

<script lang="ts" setup>
const props = defineProps<{
  columns: any[]
  title: string
}>()
const $emit = defineEmits(['send'])
const selectedItem = ref<any[]>([])
const value = ref<string[]>()
function handleConfirm({ value, selectedItems }) {
  selectedItem.value = selectedItems
  //   给model赋值
  $emit('send', selectedItem.value)
}
function delprincipal(selvalue) {
  selectedItem.value = selectedItem.value.filter((item) => item.checkUserId !== selvalue)
  value.value = value.value.filter((item) => item !== selvalue)
  $emit('send', selectedItem.value)
}
</script>

<style lang="scss">
::v-deep {
  .wd-button.is-medium.is-round {
    min-width: 0 !important;
    margin-top: 0.8125rem;
    margin-left: 0.625rem;
  }
}
.selectprincipal {
  padding: 1.3125rem 0 1.3125rem 0;
  margin: 0.625rem 0 1.3125rem 13px;
  font-size: 0.875rem;
  border-top: 0.3px solid #ebebeb;
  border-bottom: 0.3px solid #ebebeb;
  .principalcontent {
    .principal_itme {
      display: flex;
      align-items: center;
      justify-content: space-around;
      float: left;
      width: 5.8125rem;
      height: 2.375rem;
      margin: 0.625rem;
      background-color: #f0f0f0;
      border-radius: 6px;
    }
  }
}
</style>
