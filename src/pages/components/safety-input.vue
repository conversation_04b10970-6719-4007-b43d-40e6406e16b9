<!--
 * @Author: <EMAIL> <EMAIL>
 * @Date: 2024-12-18 11:19:44
 * @LastEditors: <EMAIL> <EMAIL>
 * @LastEditTime: 2024-12-20 17:41:05
 * @FilePath: /hazard-mgr/src/pages/components/safety-input.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: 'label',
  },
}
</route>

<template>
  <view class="">
    <wd-picker :columns="columns" v-model="value" @confirm="handleConfirm" />
  </view>
</template>

<script lang="ts" setup>
const columns = ref(['选项1', '选项2', '选项3', '选项4', '选项5', '选项6', '选项7'])
const value = ref('')

function handleConfirm({ value }) {
  value.value = value
}
</script>

<style lang="scss" scoped>
::v-deep {
  .wd-picker__cell {
    // padding: 0;
    border: 0.0625rem solid #ddd;
  }
}
</style>
