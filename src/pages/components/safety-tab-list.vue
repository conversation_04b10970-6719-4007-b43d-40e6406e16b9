<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: 'label',
  },
}
</route>

<template>
  <view>
    <view>
      <view></view>
    </view>
    <wd-row :gutter="10">
      <wd-col :span="8" v-for="e in list" :key="e.id">
        <view class="text-center bg-indigo h-8 leading-8 font-size-3 color-white mb-2.5">
          {{ e.tabName }}
        </view>
      </wd-col>
    </wd-row>
  </view>
</template>

<script lang="ts" setup>
interface TabListType {
  id: number
  tabName: string
}
const props = defineProps<{
  list: Array<TabListType>
}>()
</script>

<style lang="scss" scoped></style>
