export type parameFrom = {
  hazardLevel: string
  likeParam: string
  pageNo: number
  pageSize: number
  reviewState: number
  unitName: string
  userId: string
  unitId: string
  checkUnitId: string
  likeFields: string
  likeFieldValue: string
  roleCodes: []
}

export type CheckReviewForm = {
  dealId: string
  dealUserId: string
  disposeId: string
  filePathList: []
  randomCheckEventId: string
  reviewDesc: string
  reviewState: number
  reviewStateName: string
  reviewTime: number
  reviewUserId: string
  reviewUserName: string
  zhId: string
  reviewUserPhone: string
}
export type addCheckForms = {
  createBy: string
  createTime: number
  randomCheckEventId: string
  reformType: number
  reformUserId: string
  reformUserName: string
  updateBy: string
  updateTime: number
  zhId: string
}

export type pageForm = {
  id: string
  disposeId: string
  hazardSource: string
  hazardSourceName: string
  unitId: string
  unitName: string
  randomCheckId: string
  eventSourceId: string
  essentialFactorClassItemId: string
  hazardDesc: string
  hazardPosition: string
  hazardType: string
  hazardTypeName: string
  hazardLevel: string
  hazardLevelName: string
  remark: string
  timeoutDays: string
  disposeState: string
  disposeStateName: string
  eventTime: string
  createBy: string
  createTime: string
  updateBy: string
  updateTime: string
  zhId: string
  buildingId: string
  floorId: string
  mapX: number
  mapY: number
  mapZ: number
  longitude: number
  latitude: number
  dealUserId: string
  dealId: string
}
export type pageData<T> = {
  pageNo: number
  pageSize: number
  pages: number
  rows: T[]
  total: number
}
export type ResponsesData<T> = {
  total: number
  code: string
  data: T[]
  dataType: string
  message: object
  status: string
  token: string
}
