<route lang="json5">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '隐患复查详情',
  },
}
</route>

<template>
  <view class="container">
    <view class="infobox">
      <view class="infoItme">
        <view class="infokey">检查内容</view>
        <view class="infovalue">{{ info2Itme.inspectionItem }}</view>
      </view>
      <view class="infoItme">
        <view class="infokey">检查详情</view>
        <view class="infovalue">{{ info2Itme.inspectionDescribe }}</view>
      </view>
      <view class="infoItme">
        <view class="infokey">法规依据</view>
        <view class="infovalue">{{ info2Itme.inspectionItemBasis }}</view>
      </view>
      <view class="infoItme">
        <view class="infokey">合规要求</view>
        <view class="infovalue">{{ info2Itme.inspectionAsk }}</view>
      </view>
      <view class="infoItme">
        <view class="infokey">法律原文</view>
        <view class="infovalue">{{ info2Itme.legalText }}</view>
      </view>
      <view class="infoItme">
        <view class="infokey">法律责任</view>
        <view class="infovalue">{{ info2Itme.legalLiability }}</view>
      </view>
      <view class="infoItme">
        <view class="infokey">图例</view>
        <view class="infovalue"
          style="display: flex; flex-direction: row; flex-wrap: wrap; justify-content: flex-start">
          <view class="fileList" v-for="(item, index) in info2Itme.inspectionFiles" :key="index">
            <wd-img style="width: 100%; height: 90%" :src="VITE_PREVIEW_BASEURL + item.fileUrl"
              :enable-preview="true" />
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { useAppStore, usefileConfigStore } from '@/store'

// const VITE_PREVIEW_BASEURL = import.meta.env.VITE_PREVIEW_BASEURL
// interface info1Itme {
//   id: number
//   name: string
// }
defineProps({
  info2Itme: {
    type: Object,
  },
})
const usefileStore = usefileConfigStore()
const VITE_PREVIEW_BASEURL = ref()
const currentEnvInfo = useAppStore().appEnv
if (!currentEnvInfo.wychApp) {
  VITE_PREVIEW_BASEURL.value = usefileStore.fileUrl.fileUrlPrefixMiniProgram + '/'
} else {
  VITE_PREVIEW_BASEURL.value = usefileStore.fileUrl.fileUrlPrefix + '/'
}

// const VITE_PREVIEW_BASEURL = ref(usefileStore.fileUrl + '/')
// const VITE_PREVIEW_BASEURL = ref('')
// getbaseurl()
// // eslint-disable-next-line camelcase
// function getbaseurl() {
//   const dynamicsUrl = location.origin
//   if (location.protocol === 'https:') {
//     return (VITE_PREVIEW_BASEURL.value = dynamicsUrl + import.meta.env.VITE_PREVIEW_BASEURL_dev)
//   } else {
//     return (VITE_PREVIEW_BASEURL.value = import.meta.env.VITE_PREVIEW_BASEURL)
//   }
// }
</script>

<style lang="scss" scoped>
body {
  background-color: #f9f9f9 !important;
}

.infobox {
  padding-bottom: 1.125rem;
  margin-bottom: 1rem;
  background-color: white;

  .infobox_title {
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 0.25rem 0 0.9375rem 1.125rem;

    .fasticon {
      width: 0.25rem;
      height: 0.875rem;
      margin-right: 0.1875rem;
      background-color: #597bf7;
      border-radius: 1.125rem;
    }
  }

  .infoItme {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    padding: 0.5rem 1rem 0rem 1rem;
    font-size: 0.875rem;

    .infokey {
      min-width: 4.25rem;
      color: #7f7f7f;
    }
  }
}

.fixed-bottom {
  position: fixed;
  bottom: 0;
  width: 100%;
  height: 3.375rem;
  line-height: 3.375rem;
  color: #fff;
  text-align: end;
  background-color: white;
  border-top: 0.3px solid #ccc;
}

.infovalue {
  width: 100%;
  text-align: left;
}

.fileList {
  width: 4.125rem;
  height: 4.125rem;
  margin: 0.5rem;
  text-align: center;
  border-radius: 0.375rem;
}
</style>
