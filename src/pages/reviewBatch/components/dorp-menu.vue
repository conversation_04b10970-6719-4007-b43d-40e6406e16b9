<!--
 * @Author: 悦悦 <EMAIL>
 * @Date: 2024-09-21 15:55:16
 * @LastEditors: 悦悦 <EMAIL>
 * @LastEditTime: 2024-10-11 15:50:07
 * @FilePath: /隐患排查app/src/pages/reviewdanger/components/dorp-menu.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '下拉菜单',
  },
}
</route>
<template>
  <wd-drop-menu>
    <wd-drop-menu-item title="等级" v-model="Gradevalue" :options="GradeList" @change="handleChange1" />
    <wd-drop-menu-item title="单位名称" v-model="Unitvalue" :options="UnitList" @change="handleChange2" />
  </wd-drop-menu>
</template>

<script lang="ts" setup>
import { posthazardGradeAPI } from '../featch'

const $emit = defineEmits(['getGrade', 'getUnit'])
// 下拉菜单
const Gradevalue = ref<string>('0000')
const Unitvalue = ref<string>('0')
// const GradeList = ref<Record<string, any>>([])
const GradeList = ref<any[]>([])
GradeList.value.unshift({ label: '全部', value: '0000' })
posthazardGradeAPI({})
  .then((res) => {
    GradeList.value.push(...convertTocolumns(res.data))
  })
  .finally(() => { })

const UnitList = ref<Record<string, any>>([
  { label: '全部', value: 0 },
  { label: '单位名称1', value: 1 },
  { label: '单位名称2', value: 2 },
])
Unitvalue.value = UnitList.value[0].value

function handleChange1({ value, selectedItem }) {
  // console.log(selectedItem)
  $emit('getGrade', selectedItem)
}
function handleChange2({ value, selectedItem }) {
  $emit('getUnit', selectedItem)
}

function convertTocolumns(items) {
  return items.map((item) => ({
    label: item.gradeName,
    value: item.id,
  }))
}
</script>

<style lang="scss" scoped></style>
