<route lang="json5">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '隐患详情',
  },
}
</route>

<template>
  <view class="container">
    <view class="infobox">
      <view class="infobox_title">
        <view class="fasticon"></view>
        <view>{{ info1Itme.hazardDesc }}</view>
      </view>
      <view class="infoItme">
        <view class="infokey">单位</view>
        <view class="infovalue">{{ info1Itme.unitName || '--' }}</view>
      </view>
      <view class="infoItme">
        <view class="infokey">隐患分类</view>
        <view class="infovalue">{{ info1Itme.hazardTypeName || '--' }}</view>
      </view>
      <view class="infoItme">
        <view class="infokey">隐患等级</view>
        <view class="infovalue">{{ info1Itme.hazardLevelName || '--' }}</view>
      </view>
      <view class="infoItme">
        <view class="infokey">隐患位置</view>
        <view class="infovalue">{{ info1Itme.hazardPosition || '--' }}</view>
      </view>
      <view class="infoItme">
        <view class="infokey">检查人</view>
        <view class="infovalue" style="display: flex">{{ info1Itme.createByName || '--' }}</view>
      </view>
      <view class="infoItme">
        <view class="infokey">检查时间</view>
        <view class="infovalue">{{ info1Itme.createTime || '--' }}</view>
      </view>
      <view class="infoItme">
        <view class="infokey">上报时间</view>
        <view class="infovalue">{{ info1Itme.eventTime || '--' }}</view>
      </view>
      <view class="infoItme">
        <view class="infokey">隐患整改人员</view>
        <view class="infovalue">
          <span v-for="(item, index) in info1Itme.hazardMendUsers" :key="index">
            {{ item.reformUserName }},
          </span>
        </view>
      </view>
      <view class="infoItme">
        <view class="infokey">备注</view>
        <view class="infovalue">{{ info1Itme.remark || '--' }}</view>
      </view>
      <view class="infoItme">
        <view class="infokey" style="min-width: 5.25rem">隐患图片</view>
        <view class="infovalue" style="display: flex; flex-direction: row; flex-wrap: wrap; justify-content: flex-end">
          <view class="fileList" v-for="(item, index) in info1Itme.hazardFiles" :key="index">
            <wd-img style="width: 100%; height: 90%" :src="VITE_PREVIEW_BASEURL + item.fileUrl"
              :enable-preview="true" />
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { useAppStore, usefileConfigStore } from '@/store'

// const VITE_PREVIEW_BASEURL = import.meta.env.VITE_PREVIEW_BASEURL
defineProps({
  info1Itme: {
    type: Object,
  },
})
const usefileStore = usefileConfigStore()
const VITE_PREVIEW_BASEURL = ref()
const currentEnvInfo = useAppStore().appEnv
if (!currentEnvInfo.wychApp) {
  VITE_PREVIEW_BASEURL.value = usefileStore.fileUrl.fileUrlPrefixMiniProgram + '/'
} else {
  VITE_PREVIEW_BASEURL.value = usefileStore.fileUrl.fileUrlPrefix + '/'
}

// const VITE_PREVIEW_BASEURL = ref(usefileStore.fileUrl + '/')
// const VITE_PREVIEW_BASEURL = ref('')
// getbaseurl()
// // eslint-disable-next-line camelcase
// function getbaseurl() {
//   const dynamicsUrl = location.origin
//   if (location.protocol === 'https:') {
//     return (VITE_PREVIEW_BASEURL.value = dynamicsUrl + import.meta.env.VITE_PREVIEW_BASEURL_dev)
//   } else {
//     return (VITE_PREVIEW_BASEURL.value = import.meta.env.VITE_PREVIEW_BASEURL)
//   }
// }
</script>

<style lang="scss" scoped>
body {
  background-color: #f9f9f9 !important;
}

.infobox {
  padding-bottom: 1.125rem;
  margin-bottom: 1rem;
  background-color: white;

  .infobox_title {
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 0.25rem 0 0.9375rem 1.125rem;

    .fasticon {
      width: 0.25rem;
      height: 0.875rem;
      margin-right: 0.1875rem;
      background-color: #597bf7;
      border-radius: 1.125rem;
    }
  }

  .infoItme {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    padding: 0.5rem 1rem 0.5rem 1rem;
    font-size: 0.875rem;

    .infokey {
      min-width: 6.25rem;
      color: #7f7f7f;
    }
  }
}

.fixed-bottom {
  position: fixed;
  bottom: 0;
  width: 100%;
  height: 3.375rem;
  line-height: 3.375rem;
  color: #fff;
  text-align: end;
  background-color: white;
  border-top: 0.3px solid #ccc;
}

.fileList {
  width: 4.125rem;
  height: 4.125rem;
  margin: 0.5rem;
  text-align: center;
  border-radius: 0.375rem;
}
</style>
