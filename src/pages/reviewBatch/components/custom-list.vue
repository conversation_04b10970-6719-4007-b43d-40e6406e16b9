<!--
 * @Author: 悦悦 <EMAIL>
 * @Date: 2024-09-21 15:55:16
 * @LastEditors: 悦悦 <EMAIL>
 * @LastEditTime: 2024-09-24 14:36:36
 * @FilePath: /隐患排查app/src/pages/reviewdanger/components/custom-list.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<!-- <wd-tag type="primary" mark>标签</wd-tag>
<wd-tag type="danger" mark>标签</wd-tag>
<wd-tag type="warning" mark>标签</wd-tag>
<wd-tag type="success" mark>标签</wd-tag> -->
<template>
  <view class="muenitme" v-for="(item, index) in initialData" :key="index" @click="openDetail(item)">
    <view>
      <view class="_select" v-if="item.checked">
        <img src="@/static/images/_select.png" />
      </view>
      <view class="_select_border" v-else></view>
    </view>
    <view style="width: 19rem">
      <view class="field1">
        <view style="
            width: 13rem;
            padding-top: 0.5rem;
            overflow: hidden;
            font-weight: bold;
            color: #000000;
            text-overflow: ellipsis;
            white-space: nowrap;
          ">
          {{ item.hazardDesc }}
        </view>

        <view>
          <wd-tag style="min-width: 4rem; font-size: 0.675rem; text-align: center;padding: 0.3rem 0rem;" type="warning"
            mark>
            {{ item.hazardLevelName || '--' }}
          </wd-tag>
        </view>
      </view>
      <view style="padding-left: 0.5rem; color: #000000">
        {{ item.unitName }}
      </view>
      <view class="field1">
        <view style="width: 50%; overflow: hidden; text-overflow: ellipsis; white-space: nowrap">
          {{ item.hazardPosition || '--' }}
        </view>
        <view style="
            width: 10rem;
            margin-left: auto;
            overflow: hidden;
            text-align: right;
            text-overflow: ellipsis;
            white-space: nowrap;
          ">
          {{ item.hazardTypeName || '--' }}
        </view>
      </view>
      <view class="field" style="display: flex; justify-content: space-between">
        <!-- <view>{{ item.hazardPosition }}</view> -->
        <!-- <view>{{ item.hazardTypeName }}</view> -->
      </view>
      <view class="field">
        <view>检查人：{{ item.createByName || '--' }}</view>
      </view>
      <view class="field">
        <view>检查时间：{{ item.createTime || '--' }}</view>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
// import { pageForm } from '../type'
const selectList = ref([])
const Emits = defineEmits(['getSelectList'])
const props = defineProps<{
  initialData: any[]
}>()
function openDetail(value: any) {
  // console.log('value = ', value.checked)
  checkRadio(value)
  // uni.navigateTo({
  //   url: `/pages/reviewdanger/detail?id=${id}&disposeId=${disposeId}`,
  // })
  // console.log('selectList = ', selectList.value)
}
function checkRadio(column) {
  const i = props.initialData.findIndex((item: any) => item.id === column.id)
  // eslint-disable-next-line vue/no-mutating-props
  props.initialData[i].checked = !props.initialData[i].checked
  // console.log('column.checked = ', column.checked)
  if (column.checked) {
    selectList.value.push(column)
  } else {
    const i = selectList.value.findIndex((item: any) => item.id === column.id)
    selectList.value.splice(i, 1)
  }
  Emits('getSelectList', selectList.value)
}
function clearList() {
  selectList.value = []
}
defineExpose({
  clearList,
})
</script>

<style lang="scss" scoped>
::v-deep {
  .wd-tag.is-warning.is-mark {
    min-width: 3rem;
    text-align: center;
  }
}

.muenitme {
  display: flex;
  align-items: center;
  width: 21.5625rem;
  padding: 0 0 0.625rem 0;
  margin: 0.9375rem;
  font-size: 0.875rem;
  // background-color: #ebeef5;
  background-color: #fff;
  border-radius: 0.6rem;

  ._select_border {
    width: 23px;
    height: 23px;
    margin-left: 9px;
    border: 1px solid #cbcbcb;
    border-radius: 50%;
  }

  ._select {
    width: 24px;
    height: 24px;
    margin-left: 10px;

    img {
      width: 100%;
      height: 100%;
    }
  }

  .field1 {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.5rem;
  }

  .field {
    padding: 0.2rem 0.5rem;
  }
}
</style>
