import { $api } from '@/api'
import { http } from '@/utils/http'

/** post 请求 获取等级列表 hazardGrade/list   */
export const posthazardGradeAPI = (Parameters: any) => {
  return http.post($api.type.hazard + '/hazardGrade/list', Parameters)
}
/* 隐患清单  /hazardRecord/pageEvent */
export const postpageEventAPI = (Parameters: any) => {
  return http.post($api.type.hazard + '/hazardRecord/pageEvent', {
    ...Parameters,
  })
}
/* 隐患清单  /hazardRecord/pageEvent */
export const hazardPlanTaskEventAPI = (Parameters: any) => {
  return http.post($api.type.hazard + '/hazardPlanTaskEvent/pageEvent', {
    ...Parameters,
  })
}
/* 隐患清单详情  hazardRandomCheck/getHazardInventoryDetail */
export const postHazardInventoryDetailAPI = (Parameters: any) => {
  return http.post(
    $api.type.hazard + '/hazardRandomCheck/getHazardInventoryDetail?id=' + Parameters,
  )
}
/* 任务隐患记录管理----详情  hazardPlanTaskEvent/detail */
export const posthazardPlanTaskEventAPI = (Parameters: any) => {
  return http.post($api.type.hazard + '/hazardPlanTaskEvent/detail?id=' + Parameters)
}
/* 隐患整改  /hazardRandomCheck/updateHazardMend */
export const postupdateHazardMendAPI = (Parameters: any) => {
  return http.post($api.type.hazard + '/hazardRandomCheck/updateHazardMend', {
    ...Parameters,
  })
}
/* 隐患复查  /hazardRecord/hazardReview */
export const posthazardReviewAPI = (Parameters: any) => {
  return http.post($api.type.hazard + '/hazardRecord/hazardReview', {
    ...Parameters,
  })
}
/* 隐患治理头部统计 hazardRecord/eventStasticsTop */
export const posteventStasticsTopAPI = (Parameters: any) => {
  return http.post($api.type.hazard + '/hazardRecord/eventStasticsTop', {
    ...Parameters,
  })
}
/* 整改记录 dispose/record */
export const postrecordAPI = (Parameters: any) => {
  return http.post($api.type.hazard + '/dispose/record', {
    ...Parameters,
  })
}
/* 催促操作 */
export const postsaveUrgeRecordAPI = (Parameters: any) => {
  return http.post($api.type.hazard + '/dispose/saveUrgeRecord', {
    ...Parameters,
  })
}

/* post 隐患列表 //hazardMeger/pageEvent */
export const posthazardMegerpageEventAPI = (Parameters: any) => {
  return http.post(
    $api.type.hazard +
      '/hazardMeger/pageEvent?businessId=' +
      Parameters.businessId +
      '&flag=' +
      Parameters.flag,
    { ...Parameters },
  )
}
// 隐患详情 /hazardMeger/queryDetail
export const posthazardMegerqueryDetailAPI = (Parameters: any) => {
  return http.post(
    $api.type.hazard + '/hazardMeger/queryDetail?id=' + Parameters.id + '&flag=' + Parameters.flag,
    {
      ...Parameters,
    },
  )
}
