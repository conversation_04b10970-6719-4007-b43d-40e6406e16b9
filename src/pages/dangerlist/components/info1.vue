<route lang="json5">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '隐患详情',
  },
}
</route>

<template>
  <view class="container">
    <view class="infobox">
      <view class="infobox_title">
        <view class="fasticon"></view>
        <view>{{ info1Itme.hazardDesc || '--' }}</view>
      </view>
      <view class="infoItme">
        <view class="infokey">隐患编号</view>
        <view class="infovalue">{{ info1Itme.rectificationNum || '--' }}</view>
      </view>
      <view class="infoItme">
        <view class="infokey">单位</view>
        <view class="infovalue">{{ info1Itme.unitName || '--' }}</view>
      </view>
      <view class="infoItme">
        <view class="infokey">隐患分类</view>
        <view class="infovalue">{{ info1Itme.hazardTypeName || '--' }}</view>
      </view>
      <view class="infoItme">
        <view class="infokey">隐患等级</view>
        <view class="infovalue">{{ info1Itme.hazardLevelName || '--' }}</view>
      </view>
      <view class="infoItme">
        <view class="infokey">隐患位置</view>
        <view class="infovalue">{{ info1Itme.hazardPosition || '--' }}</view>
      </view>
      <view class="infoItme">
        <view class="infokey">隐患来源</view>
        <view class="infovalue">{{ info1Itme.hazardSourceName || '--' }}</view>
      </view>
      <!--  (1:物联网监测 .3:智能视频终端14、智能视频轮巡15、机器人自动巡检16、无人机自动巡检17、人工智能巡检18、人工上报（视频）19、人工上报（无人机）20、人工上报（机器人） -->
      <view
        v-if="![1, 3, 14, 15, 16, 17, 18, 19, 20].includes(info1Itme.hazardSource)"
        class="infoItme"
      >
        <view class="infokey">检查人</view>
        <view class="infovalue" style="display: flex">
          {{ info1Itme.createByName || '--' }}
        </view>
      </view>
      <view v-if="info1Itme.hazardSource !== 1" class="infoItme">
        <view class="infokey">检查时间</view>
        <view class="infovalue">{{ info1Itme.createTime || '--' }}</view>
      </view>
      <view class="infoItme">
        <view class="infokey">上报时间</view>
        <view class="infovalue">{{ info1Itme.eventTime || '--' }}</view>
      </view>
      <view class="infoItme">
        <view class="infokey">整改期限</view>
        <view class="infovalue">{{ info1Itme.correctionTime || '--' }}</view>
      </view>
      <view class="infoItme">
        <view class="infokey">隐患整改人员</view>
        <view class="infovalue" v-if="info1Itme.hazardMendUsers">
          <span v-if="info1Itme.hazardMendUsers && info1Itme.hazardMendUsers.length === 0">--</span>
          <span v-else v-for="(item, index) in info1Itme.hazardMendUsers" :key="index">
            {{ item.reformUserName }}
            {{ info1Itme.hazardMendUsers.length - 1 === index ? '' : ',' }}
          </span>
        </view>
        <!-- 计划接口--- -->
        <view class="infovalue" v-if="info1Itme.users">
          <span v-if="info1Itme.users.length === 0">--</span>
          <span v-else v-for="(item, index) in info1Itme.users" :key="index">
            {{ item.reformUserName }}
            {{ info1Itme.users.length - 1 === index ? '' : ',' }}
          </span>
        </view>
        <view class="infovalue" v-if="info1Itme.reformUsers">
          <span v-if="info1Itme.reformUsers.length === 0">--</span>
          <span v-else v-for="(item, index) in info1Itme.reformUsers" :key="index">
            {{ item.reformUserName }}
            {{ info1Itme.reformUsers.length - 1 === index ? '' : ',' }}
          </span>
        </view>
      </view>
      <view class="infoItme">
        <view class="infokey">备注</view>
        <view class="infovalue">{{ info1Itme.remark || '--' }}</view>
      </view>
      <view class="infoItme">
        <view class="infokey" style="min-width: 5.25rem">隐患图片</view>
        <view
          v-if="info1Itme.hazardFiles !== undefined"
          class="infovalue info_img"
          style="display: flex; flex-direction: row; flex-wrap: wrap; justify-content: flex-end"
        >
          <view class="fileList" v-for="(item, index) in info1Itme.hazardFiles" :key="index">
            <wd-img
              style="width: 100%; height: 90%"
              :src="getFileURL(item.fileUrl, true)"
              @click="previewImage(info1Itme.hazardFiles, index)"
            />
          </view>
        </view>
        <!-- 计划接口--- -->
        <view
          v-if="info1Itme.files !== undefined"
          class="infovalue info_img"
          style="display: flex; flex-direction: row; flex-wrap: wrap; justify-content: flex-end"
        >
          <view class="fileList" v-for="(item, index) in info1Itme.files" :key="index">
            <wd-img
              style="width: 100%; height: 90%"
              :src="getFileURL(item.fileUrl, true)"
              @click="previewImage(info1Itme.files, index)"
            />
            <!-- <wd-img
              style="width: 100%; height: 90%"
              :src="VITE_PREVIEW_BASEURL + item.fileUrl"
              :enable-preview="true"
            /> -->
          </view>
        </view>
      </view>
    </view>
  </view>
  <comOverlay ref="overlay" v-if="imageshow" :imgurl="imgurl" @closeimg="closeimg"></comOverlay>
  <!-- <template>
    <comOverlay ref="overlay" v-if="imageshow"></comOverlay>
  </template> -->
</template>

<script lang="ts" setup>
import comOverlay from '@/components/commonSelect/com-overlay.vue'
import { getFileURL } from '@/utils/index'
defineProps({
  info1Itme: {
    type: Object,
  },
})
const overlay = ref(null)
const imageshow = ref(false)
const imgurl = ref('')
const previewImage = (data, i) => {
  // imageshow.value = true
  // imgurl.value = url
  uni.previewImage({
    current: i, // 当前显示图片的索引，默认0
    urls: (data && data.map((item) => getFileURL(item.fileUrl))) || [],
    indicator: 'default', // 图片指示器样式，可选：default/number/none
    loop: false, // 是否可循环预览
  })
}
// 关闭查看图片
function closeimg(val) {
  imageshow.value = val
}
// const previewImage = (url) => {
//   // uni.previewImage({
//   //   urls: [url],
//   // })
// }
</script>

<style lang="scss" scoped>
body {
  background-color: #f9f9f9 !important;
}

.infobox {
  padding-bottom: 1.125rem;
  margin-bottom: 1rem;
  background-color: white;

  .infobox_title {
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 0.25rem 0 0.9375rem 1.125rem;

    .fasticon {
      width: 0.25rem;
      height: 0.875rem;
      margin-right: 0.1875rem;
      background-color: #597bf7;
      border-radius: 1.125rem;
    }
  }

  .infoItme {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    padding: 0.5rem 1rem 0.5rem 1rem;
    font-size: 0.875rem;

    .infokey {
      min-width: 6.25rem;
      color: #7f7f7f;
    }

    .infovalue {
      width: 100%;
      text-align: left;
    }
  }
}

.fixed-bottom {
  position: fixed;
  bottom: 0;
  width: 100%;
  height: 3.375rem;
  line-height: 3.375rem;
  color: #fff;
  text-align: end;
  background-color: white;
  border-top: 0.3px solid #ccc;
}

.fileList {
  display: inline-block;
  width: 4.125rem;
  height: 4.125rem;
  margin: 0.5rem;
  text-align: left;
  border-radius: 0.375rem;
}

.info_img {
  display: flex;
  align-items: center;
  justify-content: start !important;
}
</style>
