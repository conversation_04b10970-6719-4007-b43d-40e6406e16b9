<!--
 * @Author: 悦悦 <EMAIL>
 * @Date: 2024-09-24 11:23:13
 * @LastEditors: xginger <EMAIL>
 * @LastEditTime: 2025-06-11 00:45:52
 * @FilePath: /隐患排查app/src/pages/dangerlist/components/custom-list.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<!-- <wd-tag type="primary" mark>标签</wd-tag>
<wd-tag type="danger" mark>标签</wd-tag>
<wd-tag type="warning" mark>标签</wd-tag>
<wd-tag type="success" mark>标签</wd-tag> -->
<template>
  <view
    class="muenitme"
    v-for="(item, index) in props.initialData"
    :key="index"
    @click="openDetail(item.id, item.disposeId, item.flag)"
  >
    <view class="rigth_top" v-if="item.timeoutDays > 0">超期{{ item.timeoutDays }}天</view>
    <view :class="item.timeoutDays > 0 ? 'rigth_top2' : 'rigth_top'">
      {{ item.disposeStateName }}
    </view>
    <view class="left">
      <!-- <wd-img :src="getFileURL(item.hazardRandomCheckEventFiles[0].fileUrl, true)" /> -->
      <!-- <view
        style="
          width: 120px;
          height: 180px;
          border-radius: 10px;
          margin-top: 0.35rem;
          background: red;
        "
      ></view> -->
      <wd-img
        style="width: 120px; height: 180px; border-radius: 10px; margin-top: 0.35rem"
        :src="getFileURL(item.hazardRandomCheckEventFiles[0].fileUrl)"
      />
    </view>
    <view class="right">
      <view>
        <text
          style="
            font-size: 14px;
            border-radius: 0.6rem;
            padding-left: 8px;
            padding-right: 8px;
            display: inline-block;
            background: #e23b50;
            color: white;
            font-size: 12px;
          "
        >
          {{ item.hazardLevelName || '--' }}
        </text>
        {{ item.hazardDesc }}
      </view>
      <view>
        <text>分类：</text>
        {{ item.hazardTypeName || '--' }}
        <text v-if="item.hazardTypeTopName">({{ item.hazardTypeTopName || '' }})</text>
      </view>

      <view>
        <text>位置：</text>
        {{ item.hazardPosition || '--' }}
      </view>

      <view v-if="![1, 3, 14, 15, 16, 17, 18, 19, 20].includes(Number(item.hazardSource))">
        <text>检查人：</text>
        {{ item.createByName || '--' }}
      </view>

      <view v-if="item.hazardSource !== 1">
        <text>上报时间：</text>
        {{ item.createTime || '--' }}
      </view>

      <view>
        <text>单位名称：</text>
        {{ item.unitName || '--' }}
      </view>
      <view style="display: flex; justify-content: space-between">
        <view></view>
        <view>
          <button
            style="
              width: 120;
              border: 1px solid #e23b50;
              background: rgb(244, 6, 6, 0.1);
              color: #e23b50;
              display: inline-block;
            "
            @click.stop="saveUrgeRecord(item)"
            v-if="
              (arrayContainsValue(userinfo.roleCodes, 'expert') ||
                arrayContainsValue(userinfo.roleCodes, 'ahbAdmin') ||
                arrayContainsValue(userinfo.roleCodes, 'admin') ||
                arrayContainsValue(userinfo.roleCodes, 'safetyCommitteePerson')) &&
              item.disposeState !== 1 &&
              item?.reviewState === 1
            "
          >
            催促
          </button>
          <button
            style="
              width: 120;
              border: 1px solid #0256ff;
              background: rgb(2, 2, 246, 0.1);
              color: #0256ff;
              display: inline-block;
              margin-left: 0.52rem;
            "
            v-if="
              (arrayContainsValuebyuser(item.hazardRandomCheckEventUsers, userinfo.id) ||
                arrayContainsValue(userinfo.roleCodes, 'controlRoomDutyPerson') ||
                arrayContainsValue(userinfo.roleCodes, 'workAreaSafetyPerson')) &&
              item.disposeState !== 1 &&
              item.reviewState === 1
            "
            @click.stop="ReviewCheck(item)"
            block
          >
            隐患整改
          </button>
        </view>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { pageForm } from '../type'
import { getFileURL } from '@/utils/index'
import { useUserStore } from '@/store'
import { debounce } from '@/utils'
const props = defineProps<{
  initialData: pageForm[]
  taskState?: string
}>()
import { postsaveUrgeRecordAPI, posthazardMegerqueryDetailAPI } from '../featch'
const userStore = useUserStore()
const userinfo: any = userStore.userInfo ? userStore.userInfo : {}
function openDetail(id, disposeId, flag) {
  // console.log(id, disposeId, flag)

  uni.navigateTo({
    url: `/pages/dangerlist/detail?id=${id}&disposeId=${disposeId}&flag=${flag}&isdispose=1`,
  })
}
function arrayContainsValue(arr, value: string) {
  // // console.log(arr, Object.prototype.toString.call(arr), '详情==是否是整改人===========arr')
  if (arr?.length > 0 && arr !== null) {
    return arr.includes(value)
  } else {
    return false
  }
}
function ReviewCheck(item) {
  // 根据权限跳转不同页面
  uni.navigateTo({
    url: `/pages/dangerlist/ReviewCheck?id=${item.disposeId}&randId=${item.id}`,
  })
}
const hazardInventoryDetail = ref<any>({})
function gethazardMegerqueryDetail() {
  posthazardMegerqueryDetailAPI(paramasData.value)
    .then((res: any) => {
      hazardInventoryDetail.value = res.data
    })
    .finally(() => {
      // console.log(111)
    })
}

function arrayContainsValuebyuser(arr, value: string) {
  // console.log(arr, Object.prototype.toString.call(arr), '详情==是否是整改人===========arr')
  if (arr?.length > 0 && arr !== undefined) {
    return arr.some((item) => item.reformUserId === value)
  } else {
    return false
  }
} /* 催促操作 */
const operatorModel = ref<any>({
  disposeId: '', // 处置id
  eventType: '4', // 事件类型
  operatorId: userinfo.id, // 操作人ID
  operatorName: userinfo.userName, // 操作人姓名
  operatorTel: userinfo.userTelphone, // 操作联系方式
  subCenterCode: userinfo.zhId, // 运营中心编码
})

const saveUrgeRecord = debounce((i) => {
  operatorModel.value.disposeId = i.disposeId
  uni.showModal({
    title: '提示',
    content: '请确认是否要进行催促?',
    success: function (res) {
      if (res.confirm) {
        // 在这里执行确定后的操作
        // console.log(JSON.stringify(operatorModel.value))
        postsaveUrgeRecordAPI(operatorModel.value)
          .then((res: any) => {
            uni.showToast({
              icon: 'none',
              title: res.message,
            })
          })
          .finally(() => {})
      } else if (res.cancel) {
        // 在这里执行取消后的操作
      }
    },
  })
}, 500)
</script>

<style lang="scss" scoped>
.muenitme {
  width: 21.5625rem;
  // height: 14.25rem;
  margin: 0.9375rem;
  padding: 0.8rem 0.5rem;
  background-color: #ebeef5;
  display: flex;
  border-radius: 0.6rem;
  font-size: 14px;
  position: relative;
  .rigth_top {
    position: absolute;
    right: 0;
    top: 0;
    background: red;
    padding-left: 0.5rem;
    padding-right: 0.5rem;
    border-radius: 0 0.6rem 0 0.6rem;
    color: white;
  }
  .rigth_top2 {
    position: absolute;
    right: 4.6rem;
    top: 0;
    background: #eb5b13;
    padding-left: 0.5rem;
    padding-right: 0.5rem;
    border-radius: 0 0.6rem 0 0.6rem;
    color: white;
  }
  .left {
  }
  .right {
    width: 70vw;
    margin-left: 0.5rem;

    view {
      text {
        color: gray;
      }
      margin-top: 0.5rem;
    }
  }
}
</style>
