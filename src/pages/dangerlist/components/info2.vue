<route lang="json5">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '隐患复查详情',
  },
}
</route>
<template>
  <view class="container">
    <view class="infobox">
      <view class="infoItme">
        <view class="infokey">检查内容</view>
        <view class="infovalue">{{ info2Itme.classItemVo?.inspectionItem || '--' }}</view>
      </view>
      <view class="infoItme">
        <view class="infokey">检查详情</view>
        <view class="infovalue">{{ info2Itme.classItemVo?.inspectionDescribe || '--' }}</view>
      </view>
      <view class="infoItme">
        <view class="infokey">法规依据</view>
        <view class="infovalue">{{ info2Itme.classItemVo?.inspectionItemBasis || '--' }}</view>
      </view>
      <view class="infoItme">
        <view class="infokey">合规要求</view>
        <view class="infovalue">{{ info2Itme.classItemVo?.inspectionAsk || '--' }}</view>
      </view>
      <view class="infoItme">
        <view class="infokey">法律原文</view>
        <view class="infovalue">{{ info2Itme.classItemVo?.legalText || '--' }}</view>
      </view>
      <view class="infoItme">
        <view class="infokey">法律责任</view>
        <view class="infovalue">{{ info2Itme.classItemVo?.legalLiability || '--' }}</view>
      </view>
      <view class="infoItme">
        <view class="infokey" style="min-width: 4.25rem">图例</view>
        <view class="infovalue info_img"
          style="display: flex; flex-direction: row; flex-wrap: wrap; justify-content: flex-start">
          <view class="fileList" v-for="(item, index) in info2Itme.classItemVo?.files" :key="index">
            <!-- <wd-img style="width: 100%; height: 90%" :src="VITE_PREVIEW_BASEURL + item.fileUrl" /> -->
            <!-- <wd-img
              style="width: 100%; height: 90%"
              :src="VITE_PREVIEW_BASEURL + item.fileUrl"
              :enable-preview="true"
            /> -->
            <wd-img style="width: 100%; height: 90%" :src="getFileURL(item.fileUrl, true)"
              @click="previewImage(getFileURL(item.fileUrl))" />
          </view>
        </view>
      </view>
    </view>
  </view>
  <comOverlay ref="overlay" v-if="imageshow" :imgurl="imgurl" @closeimg="closeimg"></comOverlay>
</template>

<script lang="ts" setup>
import { useAppStore, usefileConfigStore } from '@/store'
import { detailFrom } from '../type'
import comOverlay from '@/components/commonSelect/com-overlay.vue'
import { getFileURL } from '@/utils/index'
// const VITE_PREVIEW_BASEURL = import.meta.env.VITE_PREVIEW_BASEURL
defineProps({
  info2Itme: {
    type: Object,
  },
})
const overlay = ref(null)
const imageshow = ref(false)
const imgurl = ref('')
const previewImage = (url) => {
  imageshow.value = true
  imgurl.value = url
}
// 关闭查看图片
function closeimg(val) {
  imageshow.value = val
}
</script>

<style lang="scss" scoped>
body {
  background-color: #f9f9f9 !important;
}

.infobox {
  padding-bottom: 1.125rem;
  margin-bottom: 1rem;
  background-color: white;

  .infobox_title {
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 0.25rem 0 0.9375rem 1.125rem;

    .fasticon {
      width: 0.25rem;
      height: 0.875rem;
      margin-right: 0.1875rem;
      background-color: #597bf7;
      border-radius: 1.125rem;
    }
  }

  .infoItme {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    padding: 0.5rem 0rem 0rem 0rem;
    font-size: 0.875rem;

    .infokey {
      min-width: 4.25rem;
      color: #7f7f7f;
    }

    .fileList {
      width: 4.125rem;
      height: 4.125rem;
      margin: 0.5rem;
      text-align: center;
      border-radius: 0.375rem;
    }
  }
}

.fixed-bottom {
  position: fixed;
  bottom: 0;
  width: 100%;
  height: 3.375rem;
  line-height: 3.375rem;
  color: #fff;
  text-align: end;
  background-color: white;
  border-top: 0.3px solid #ccc;
}

.info_img {
  display: flex;
  align-items: center;
  justify-content: start !important;
}

.infovalue {
  width: 100%;
  text-align-last: left;
}
</style>
