<route lang="json5">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '隐患复查',
  },
}
</route>

<template>
  <SafetyNavbar title="隐患复查"></SafetyNavbar>
  <view class="container">
    <view>
      <wd-select-picker label="复查结果" v-model="reviewChecktModel.reviewState" align-right type="radio"
        :show-confirm="false" :columns="columns"></wd-select-picker>
    </view>
    <view>
      <view class="c_label">复查说明</view>
      <view>
        <wd-textarea v-model="reviewChecktModel.reviewDesc" placeholder="请输入复查说明" />
      </view>
    </view>
    <view>
      <view class="c_label">现场照片</view>
      <view style="padding: 10px 15px">
        <uploadButton @getFileObjList="getFilelist"></uploadButton>
      </view>
    </view>
    <view class="fixed-bottom">
      <wd-button type="primary" size="large" @click="handleClickRight" block>提交</wd-button>
    </view>
  </view>
  <!-- handleClickRight -->
</template>

<script lang="ts" setup>
import SafetyNavbar from '../components/safety-navbar.vue'
import uploadButton from '@/components/upload/upload-button.vue'
import { ResponsesData, CheckEventDealReviewForm } from './type'
import { posthazardReviewAPI } from './featch'
import { debounce } from '@/utils'

const reviewChecktModel = ref<CheckEventDealReviewForm>({
  files: [],
  id: '',
  randomCheckEventId: '',
  reviewDesc: '',
  reviewState: 0,
  reviewTime: '',
  reviewUserId: '',
  reviewUserName: '',
})
const columns = ref<any>([
  {
    value: 0,
    label: '待复查',
  },
  {
    value: 1,
    label: '已通过',
  },
  {
    value: 2,
    label: '未通过',
  },
])
onLoad((params) => {
  reviewChecktModel.value.randomCheckEventId = params.id
})
// 获取图片列表
function getFilelist(event) {
  reviewChecktModel.value.files = event
}
const handleClickRight = debounce(() => {
  // // console.log(reviewChecktModel.value)
  posthazardReviewAPI(reviewChecktModel.value)
    .then((res: any) => {
      if (res.code === 'success') {
        uni.showToast({
          icon: 'none',
          title: res.message,
        })
        uni.navigateBack({
          delta: 1,
        })
      }
    })
    .finally(() => {
      // console.log(111)
    })
}, 500)
</script>

<style lang="scss" scoped>
::v-deep {
  .wd-textarea__value {
    border-bottom: 0.0187rem solid #ebebeb;
  }
}

.container {
  .c_label {
    padding: 10px 15px;
    font-family: Source Han Sans-Medium;
    font-size: 0.875rem;
    color: #1a1a1a;
  }

  .c-switch {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px 15px;
  }
}

.fixed-bottom {
  position: fixed;
  bottom: 0;
  display: flex;
  flex-direction: row;
  align-items: center;
  width: 100%;
  height: 3.375rem;
  line-height: 3.375rem;
  color: #fff;
  text-align: center;
  background-color: white;
}
</style>
