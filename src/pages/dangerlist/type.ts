export type parameData = {
  hazardLevel: string
  likeParam: string
  pageNo: number
  pageSize: number
  reviewState: number
  unitName: string
  reformUserId: string
  unitId?: string
  likeFields: string
  likeFieldValue: string
  roleCodes: []
  businessId: string
  flag: string
  checkUnitId: string
  disposeState: string
  sign: string
  disposeStateArray: null | number
}
/* 隐患整改提交数据类型 */
export type reviewChecktype = {
  dealDesc: string
  dealIsSolved: number
  dealUserId: string
  dealUserName: string
  disposeId: string
  filePathList: []
  randomCheckEventId: string
  reviewState: number
  zhId: string
  dealUserPhone: string
}
/* 隐患复查提交数据类型 */
export type CheckEventDealReviewForm = {
  files: []
  id: string
  randomCheckEventId: string
  reviewDesc: string
  // 复查状态：0待复查、1已通过、2未通过
  reviewState: number
  reviewTime: string
  reviewUserId: string
  reviewUserName: string
}
export type pageForm = {
  id: string
  disposeId: string
  hazardSource: string
  hazardSourceName: string
  unitId: string
  unitName: string
  randomCheckId: string
  eventSourceId: string
  essentialFactorClassItemId: string
  hazardDesc: string
  hazardPosition: string
  hazardType: string
  hazardTypeName: string
  hazardLevel: string
  hazardLevelName: string
  remark: string
  timeoutDays: string
  disposeState: string
  disposeStateName: string
  eventTime: string
  createBy: string
  createByName: string
  createTime: string
  updateBy: string
  updateTime: string
  zhId: string
  buildingId: string
  floorId: string
  mapX: number
  mapY: number
  mapZ: number
  longitude: number
  latitude: number
  dealUserId: string
  dealId: string
  flag: string
}
export type detailFrom = {
  classItemId: string
  createBy: string
  createTime: string
  dealId: string
  disposeId: string
  hazardDesc: string
  hazardFiles: []
  hazardLevel: string
  hazardMendUsers: []
  hazardPosition: string
  hazardTypeName: string
  inspectionAsk?: string
  inspectionDescribe: string
  inspectionFiles: []
  inspectionItem: string
  inspectionItemBasis: string
  legalLiability?: string
  legalText: string
  randomCheckEventId: string
  remark: string
  unitName: string
  zhId: string
}
export type pageData<T> = {
  records: T[]
  total: number
  size: number
  current: number
  orders: T[]
  optimizeCountSql: boolean
  searchCount: boolean
  countId: string
  maxLimit: number
  pages: number
}
export type RandomCheckEvents<T> = {
  hazardRandomCheckEvents: T[]
  mendNumber: number
  notMendNumber: number
}
export type ResponsesData<T> = {
  code: string
  data: T[]
  dataType: string
  message: object
  status: string
  token: string
}
