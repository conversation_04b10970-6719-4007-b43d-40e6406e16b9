<route lang="json5">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '隐患详情',
  },
}
</route>

<template>
  <SafetyNavbar title="隐患整改"></SafetyNavbar>
  <view class="container">
    <view>
      <view class="c_label required">整改描述</view>
      <view>
        <wd-textarea v-model="reviewChecktModel.dealDesc" :maxlength="100" show-word-limit placeholder="请输入整改描述信息" />
      </view>
    </view>
    <view>
      <view class="c_label">现场照片</view>
      <view style="padding: 0.625rem 0.9375rem">
        <uploadButton @getFileObjList="getFilelist"></uploadButton>
      </view>
    </view>
    <view>
      <view class="c-switch">
        <view class="required">隐患是否解决</view>
        <!-- <wd-switch v-model="checked" /> -->
        <!-- button 按钮式单选 -->
        <wd-radio-group v-model="checked" shape="button" @change="change">
          <wd-radio :value="true">是</wd-radio>
          <wd-radio :value="false">否</wd-radio>
        </wd-radio-group>
      </view>
    </view>
    <view class="fixed-bottom">
      <wd-button type="primary" size="large" :loading="endLoading" @click="handleClickRight" block>
        提交
      </wd-button>
    </view>
    <!-- handleClickRight -->
  </view>
</template>

<script lang="ts" setup>
import SafetyNavbar from '../components/safety-navbar.vue'
import uploadButton from '@/components/upload/upload-button.vue'
import { ResponsesData, reviewChecktype } from './type'
import { postupdateHazardMendAPI } from './featch'
import { useUserStore } from '@/store'
import { debounce } from '@/utils'
const userStore = useUserStore()
const userinfo: any = userStore.userInfo ? userStore.userInfo : {}
const reviewChecktModel = ref<reviewChecktype>({
  dealDesc: '',
  dealIsSolved: 0,
  dealUserId: userinfo.id,
  dealUserName: userinfo.userName,
  dealUserPhone: userinfo.userTelphone,
  disposeId: '',
  filePathList: [],
  randomCheckEventId: '',
  reviewState: 0,
  zhId: userinfo.zhId || '',
})
const endLoading = ref(false)
onLoad((params) => {
  reviewChecktModel.value.disposeId = params.id
  reviewChecktModel.value.randomCheckEventId = params.randId
})
const checked = ref<boolean>(true)
// 获取图片列表
function getFilelist(event) {
  // console.log(event)
  reviewChecktModel.value.filePathList = event
}
function change(e) {
  // console.log(e)
}

const handleClickRight = debounce(() => {
  endLoading.value = true
  reviewChecktModel.value.dealIsSolved = checked.value ? 1 : 0
  // ========非空判断开始=======
  if (!reviewChecktModel.value.dealDesc) {
    uni.showToast({
      icon: 'none',
      title: '请填写整改描述',
    })
    endLoading.value = false
    return
  }
  // if (reviewChecktModel.value.filePathList.length === 0) {
  //   uni.showToast({
  //     icon: 'none',
  //     title: '请上传现场照片',
  //   })
  //   return
  // }
  // ======非空判断结束=====
  uni.showLoading({
    title: '提交中...',
    icon: 'none',
    mask: true,
  })
  postupdateHazardMendAPI(reviewChecktModel.value)
    .then((res: any) => {
      // // console.log(res)
      endLoading.value = false
      uni.hideLoading()
      if ((res.code as any) === 'success') {
        uni.showToast({
          icon: 'none',
          title: res.message,
        })
        uni.navigateBack({
          delta: 1,
        })
      } else {
        uni.showToast({
          icon: 'none',
          title: res.message,
        })
      }
    })
    .finally(() => {
      // // console.log(111)
      endLoading.value = false
      uni.hideLoading()
    })
}, 500)
</script>

<style lang="scss" scoped>
::v-deep {
  .wd-textarea__value {
    border-bottom: 0.2992px solid #ebebeb;
  }
}

.container {
  .c_label {
    padding: 0.625rem 0.9375rem;
    font-family: Source Han Sans-Medium;
    font-size: 14px;
    color: #1a1a1a;
  }

  .c-switch {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.625rem 0.9375rem;
  }

  .required::before {
    // position: absolute;
    // left: 0;
    top: 0.125rem;
    line-height: 1.1;
    color: #fa4350;
    content: '*';
  }
}

.fixed-bottom {
  position: fixed;
  bottom: 0;
  display: flex;
  flex-direction: row;
  align-items: center;
  width: 100%;
  height: 3.375rem;
  line-height: 3.375rem;
  color: #fff;
  text-align: center;
  background-color: white;
}
</style>
