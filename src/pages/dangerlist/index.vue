<route lang="json5">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '隐患整改',
  },
}
</route>
<template>
  <z-paging ref="paging" v-model="initialData" @query="getHazardInventory">
    <template #top>
      <SafetyNavbar title="隐患整改"></SafetyNavbar>
      <div style="background: white">
        <CustomTabs
          :tabs="tabs"
          :activeIndex="activeIndex"
          @handleClick="handleChange"
        ></CustomTabs>
      </div>
    </template>

    <view class="container">
      <!-- <SafetyNavbar title="隐患清单"></SafetyNavbar> -->

      <!-- 隐患检查情况 -->
      <!-- 去掉unitID -->
      <commonStatistics
        ref="commonStatisticsRef"
        :type="type"
        :unitId="parameDataModel.unitId"
        :topUnitId="topUnitId"
        :taskId="taskid"
        :createBy="createBy"
        :reformUserId="parameDataModel.reformUserId"
        :roleCodes="parameDataModel.roleCodes"
        :isPlanTask="taskState && taskState != '3'"
        :hazardLevel="parameDataModel.hazardLevel"
        :disposeState="parameDataModel.disposeState"
        :checkUnitId="parameDataModel.checkUnitId"
        :orgRes="userinfo.orgRes"
        @handleAbarbeitung="handleAbarbeitung"
      ></commonStatistics>
      <view class="infobox">
        <!-- 下拉菜单 -->
        <dorpMenu
          @getGrade="getGradevalue"
          @getUnit="getUnitvalue"
          :orgRes="userinfo.orgRes"
          @getDisposeState="getDisposeState"
          :isShowdisposeState="isdisposeState"
        ></dorpMenu>
        <!-- 分割线 -->
        <hr style="border: 0.3px solid rgb(235, 235, 235)" />
        <!-- 搜索框 -->
        <wd-search
          placeholder-left
          @change="search"
          placeholder="请输入隐患位置/类型模糊搜索"
          :hide-cancel="true"
          v-model="parameDataModel.likeFieldValue"
          @clear="clear"
        />
      </view>

      <div v-if="activeIndex === 0">
        <view v-if="initialData.length">
          <customList :initialData="initialData" :taskState="taskState"></customList>
        </view>
        <template v-else>
          <view class="list-null">
            <noData title="暂无数据~"></noData>
          </view>
        </template>
      </div>
      <div v-if="activeIndex === 1">
        <view v-if="initialData.length">
          <customList
            :initialData="initialData"
            :taskState="taskState"
            :orgRes="userinfo.orgRes"
          ></customList>
        </view>
        <template v-else>
          <view class="list-null">
            <noData title="暂无数据~"></noData>
          </view>
        </template>
      </div>
    </view>
  </z-paging>
  <!-- <view class="container">

  <scroll-view style="height: 45vh" scroll-into-view="bottom" @scrolltolower="handleScrollToLower" scroll-y="true">
    <customList :initialData="initialData" :taskState="taskState"></customList>
    <view style="color: #ccc; text-align: center" v-if="initialData.length === 0">
      暂无数据
    </view>
    <view id="bottom" v-if="total > initialData.length" style="font-size: 12px; color: #232323; text-align: center">
      加载更多数据
    </view>
  </scroll-view>
</view>
</view> -->
</template>

<script lang="ts" setup>
import commonStatistics from '@/components/commonSelect/common-statistics.vue'
import dorpMenu from '@/components/commonSelect/dorp-menu.vue'
import { useUserStore } from '@/store'
import { ref } from 'vue'
import CustomTabs from '../components/custom-Tabs.vue'
import SafetyNavbar from '../components/safety-navbar.vue'
import customList from './components/custom-list.vue'
import { posthazardMegerpageEventAPI } from './featch'
import { pageForm, parameData } from './type'
const userStore = useUserStore()
const userinfo: any = userStore.userInfo ? userStore.userInfo : {}
// // console.log(userinfo.unitId, '===========userinfo')
const parameDataModel = ref<parameData>({
  hazardLevel: '',
  likeFields: 'hazardPosition,hazardTypeName',
  likeFieldValue: '',
  pageNo: 1,
  pageSize: 10,
  reviewState: 0,
  unitName: '',
  reformUserId: '',
  likeParam: '',
  roleCodes: [],
  businessId: '',
  flag: '',
  unitId: '',
  checkUnitId: '',
  disposeState: '',
  sign: '0', // 一进页面传一个非空的任意值
  disposeStateArray: null,
})

const initialData = ref<pageForm[]>([])
const commonStatisticsRef = ref()
const total = ref(0)
const taskState = ref('')
const taskid = ref('')
const planId = ref('')
const unitId = ref('')
const createBy = ref('')
const topUnitId = ref('')
const type = ref(1)

const paging = ref()
const isdisposeState = ref(true)
// const isUnitvalue = ref(true)

const tabs = ref()
const activeIndex = ref(0)
// const paramas = ref<any>()
onLoad((paramas) => {
  // // console.log(paramas)
  // paramas.value = paramas
  if (paramas && Object.keys(paramas).length !== 0) {
    taskid.value = paramas.taskid
    taskState.value = paramas.taskState
    planId.value = paramas.planId
    unitId.value = userinfo.unitId
    createBy.value = paramas.createBy
    topUnitId.value = userinfo.orgRes === '1' ? userinfo.topUnitId : userinfo.associationTopUnitId
    if (paramas.taskState) {
      isdisposeState.value = false
    }
    // parameDataModel.value.unitId = userinfo.unitId
    if (taskid.value) {
      parameDataModel.value.flag = '02'
      type.value = 0
      createBy.value = userinfo.id
      parameDataModel.value.unitId = ''
      topUnitId.value = ''
    }
    parameDataModel.value.businessId = taskid.value
    tabs.value = ['全部隐患']
  } else {
    if (userinfo.orgRes === '1') {
      tabs.value = ['全部隐患', '待我整改']
      parameDataModel.value.roleCodes = userinfo.roleCodes
      parameDataModel.value.reformUserId = userinfo.id
      parameDataModel.value.unitId = userinfo.unitId
      topUnitId.value = userinfo.orgRes === '1' ? userinfo.topUnitId : userinfo.associationTopUnitId
    } else {
      tabs.value = ['待我整改']
      parameDataModel.value.reformUserId = userinfo.id
      topUnitId.value = userinfo.orgRes === '1' ? userinfo.topUnitId : userinfo.associationTopUnitId
    }
  }
  // // console.log(parameDataModel.value, '==========onload,parameDataModel.value')
  // parameDataModel.value.pageNo = 1
  // parameDataModel.value.pageSize = 10
  // setTimeout(() => {
  //   // paging.value.reload()
  //   // commonStatisticsRef.value.update()
  // }, 350)
})

onShow(() => {
  // console.log(parameDataModel.value)
  // parameDataModel.value.pageNo = 1
  // getHazardInventory()
  setTimeout(() => {
    // paging.value.reload()
    commonStatisticsRef.value.update()
  })
})

function handleChange(event) {
  // // console.log('父组件取子组件的值', event)
  activeIndex.value = event
  if (userinfo.orgRes === '1') {
    if (activeIndex.value === 0) {
      parameDataModel.value.roleCodes = userinfo.roleCodes
      parameDataModel.value.reformUserId = userinfo.id
      parameDataModel.value.unitId = userinfo.unitId
    } else if (activeIndex.value === 1) {
      parameDataModel.value.businessId = ''
      parameDataModel.value.reformUserId = userinfo.id
      parameDataModel.value.roleCodes = []
      parameDataModel.value.unitId = ''
    }
  } else {
    parameDataModel.value.businessId = ''
    parameDataModel.value.reformUserId = userinfo.id
    parameDataModel.value.roleCodes = []
    parameDataModel.value.unitId = ''
  }
  // if (activeIndex.value === 0) {
  //   parameDataModel.value.roleCodes = userinfo.roleCodes
  //   parameDataModel.value.reformUserId = userinfo.id
  //   parameDataModel.value.unitId = userinfo.unitId
  // } else {
  //   parameDataModel.value.businessId = ''
  //   parameDataModel.value.reformUserId = userinfo.id
  //   parameDataModel.value.roleCodes = []
  //   parameDataModel.value.unitId = ''
  // }
  setTimeout(() => {
    paging.value.reload()
    commonStatisticsRef.value.update()
  })
}

function handleAbarbeitung(value: number) {
  // console.log(value)
  parameDataModel.value.pageNo = 1
  parameDataModel.value.pageSize = 10
  parameDataModel.value.disposeStateArray = value
  // getHazardInventory()
  setTimeout(() => {
    paging.value.reload()
    commonStatisticsRef.value.update()
  })
}
/* 等级进行筛选 */
function getGradevalue(event) {
  parameDataModel.value.pageNo = 1
  parameDataModel.value.hazardLevel = event.id === '0000' ? '' : event.id
  setTimeout(() => {
    paging.value.reload()
    commonStatisticsRef.value.update()
  })
}
/* 单位进行筛选 */
function getUnitvalue(event) {
  parameDataModel.value.pageNo = 1
  // // console.log(event)
  // if (taskid.value !== '') {
  //   parameDataModel.value.unitId = event.id === '0000' ? '' : event.id
  // } else {
  //   parameDataModel.value.checkUnitId = event.id === '0000' ? '' : event.id
  //   // unitId.value = event.id === '0000' ? '' : event.id
  // }
  // if (activeIndex.value !== 0) {
  //   parameDataModel.value.checkUnitId = event.id === '0000' ? userinfo.unitId : event.id
  // } else {
  //   parameDataModel.value.unitId = event.id === '0000' ? userinfo.unitId : event.id
  // }
  // initialData.value = []
  // getHazardInventory()
  parameDataModel.value.checkUnitId = event.id === '0000' ? '' : event.id
  setTimeout(() => {
    paging.value.reload()
    commonStatisticsRef.value.update()
  })
}
/* 整改状态进行筛选 */
function getDisposeState(event) {
  // // console.log(event)
  // initialData.value = []
  parameDataModel.value.pageNo = 1
  parameDataModel.value.disposeState = event.id === '0000' ? '' : event.id
  // getHazardInventory()
  setTimeout(() => {
    paging.value.reload()
    commonStatisticsRef.value.update()
  })
}

function getHazardInventory(pageNo) {
  // 优先使用过滤的单位id
  // =======整合接口=======
  parameDataModel.value.pageNo = pageNo
  // console.log(parameDataModel.value, '==请求：===parameDataModel.value')
  uni.showLoading({
    mask: true,
  })
  try {
    posthazardMegerpageEventAPI(parameDataModel.value)
      .then((res: any) => {
        uni.hideLoading()
        total.value = res.data.total
        paging.value.completeByTotal(res.data.rows, total.value)
      })
      .finally(() => {
        uni.hideLoading()
      })
  } catch (error) {
    uni.hideLoading()
  }
}
// 输入框模糊查询
let timeoutId: ReturnType<typeof setTimeout> | null = null
function search(event) {
  if (timeoutId !== null) {
    clearTimeout(timeoutId)
    timeoutId = null
  }
  timeoutId = setTimeout(() => {
    parameDataModel.value.pageNo = 1
    // getHazardInventory()
    paging.value.reload()
    commonStatisticsRef.value.update()
  }, 500)
}
// 清空输入框
function clear() {
  parameDataModel.value.likeFieldValue = ''
}
</script>

<style lang="scss" scoped>
::v-deep {
  .navbar {
    position: unset !important;
  }
}

body {
  background-color: #f9f9f9 !important;
}

.container {
  // height: calc(100% - 2.25rem);
  // height: 100%;
  .infobox {
    //padding-bottom: 1.125rem;
    margin-top: 1.125rem;
    background-color: white;

    .infobox_title {
      display: flex;
      flex-direction: row;
      align-items: center;
      padding: 0.25rem 0 0.9375rem 1.125rem;

      .fasticon {
        width: 0.25rem;
        height: 0.875rem;
        margin-right: 0.1875rem;
        background-color: #597bf7;
        border-radius: 1.125rem;
      }
    }

    .box {
      display: flex;
      flex-direction: row;
      flex-wrap: nowrap;
      justify-content: space-between;
      padding: 0px 1.125rem 0.75rem 1.125rem;

      .box-itme {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 10.25rem;
        // height: 4.375rem;
        margin: 0.3125rem;
        background-color: rgba(188, 201, 218, 0.2);
        border-radius: 0.75rem;

        .imgbox {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 2.25rem;
          height: 2.25rem;

          .img {
            width: 100%;
            height: 100%;
          }
        }
      }

      .box-itme2 {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-around;
        width: 21.1875rem;
        height: 3.4375rem;
        margin: 0.3125rem;
        background-color: rgba(188, 201, 218, 0.2);
        border-radius: 12px;
      }
    }
  }
}
</style>
