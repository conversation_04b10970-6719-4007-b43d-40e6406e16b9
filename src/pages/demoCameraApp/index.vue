<route lang="json5">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '打卡拍照',
  },
}
</route>
<template>
  <!-- v-if="appEnv.plus" -->
  <view class="flex flex-col relative">
    <!-- <SafetyNavbar class="navbar" title="打卡拍照" transparent></SafetyNavbar> -->
    <view class="imgBox" v-if="img">
      <image :src="img" class="img w-100vw" mode="widthFix" @click="recapture"></image>
    </view>
    <view>
      <mumu-camera
        v-if="showCamera && !img"
        :exact="exactValue"
        @deny="handleDeny"
        @success="handlerSuccess"
      ></mumu-camera>
      <SafetySubmitApply @submit="submitForm" @cancel="handleCancel" v-else></SafetySubmitApply>
      <!-- <view v-if="showCamera && !img" class="absolute rounded-50% p-2 bg-black z-1000" @click="checkExact"
        :style="{ bottom: '90rpx', left: '70%' }">
        <wd-icon name="refresh1" size="30px" color="white"></wd-icon>
      </view> -->
    </view>
  </view>
</template>

<script setup lang="ts">
import { PermissionRes } from '@/hooks/useCamera'
import { useAppStore, useUserStore } from '@/store'
import SafetySubmitApply from '@/components/safety-submit-apply.vue'
import MumuCamera from '@/uni_modules/mumu-camera/components/mumu-camera/mumu-camera.vue'
import SafetyNavbar from '../components/safety-navbar.vue'
import { getCurrentPosition } from '@/utils'
import { getWatermarkBizData } from '@/components/upload/featch'
import axios from 'axios'
// import { taskAddClockIn } from './featch'
// const VITE_UPLOAD_IMAGE_BASEURL = import.meta.env.VITE_UPLOAD_BASEURL
const VITE_UPLOAD_IMAGE_BASEURL = import.meta.env.VITE_UPLOAD_WATERMARK_BASEURL

const VITE_SERVER_BASEURL = import.meta.env.VITE_SERVER_BASEURL
const VITE_APP_PROXY_PREFIX = import.meta.env.VITE_APP_PROXY_PREFIX

const userStore = useUserStore()
const userInfo: any = userStore.userInfo ? userStore.userInfo : {}

const recapture = () => {
  // console.log('re capture')
  prepareCamera()
}

const exactValue = ref('environment')

function checkExact() {
  exactValue.value = exactValue.value === 'user' ? 'environment' : 'user'
}

const fileInfo = ref({
  address: 'string',
  dateTime: 'string',
  logoImageUrl: 'string',
  projectName: 'string',
  sourceImageUrl: 'string',
  unitName: 'string',
  userName: 'string',
  waterTemplateName: 'string',
  weather: 'string',
  workContent: 'string',
})
const currentEnvInfo = useAppStore().appEnv

const getWatermark = async () => {
  // const position = await getCurrentPosition()
  let position = {}
  if (currentEnvInfo.wychApp) {
    position = { longitude: 0, latitude: 0 }
    try {
      const { data } = await window.waltz.call({
        // 或者通过 window.waltz.call
        module: 'WZLocation', // 模块名，对应下表的Module
        handlerName: 'getLoactionInfo', // 方法名，对应下表的HandlerName
      })
      if (+data.code === 10000) {
        // 成功回调的业务代码
        const resultObject = data.body // 接口返回的业务实参对象
        console.log(resultObject, '获取的经纬度')
        if (resultObject) {
          // 坐标系转换
          const pPoint = new GISShare.SMap.Geometry.Point(
            resultObject.longitude,
            resultObject.latitude,
          )
          GISShare.SMap.Fitting.FittingHelper.Fit(
            pPoint, // 目标点
            GISShare.SMap.SpatialReference.GeographicCoordinateSystemStyle.eGCS_WGS_1984_GCJ02, // 当前空间参考
            GISShare.SMap.SpatialReference.GeographicCoordinateSystemStyle.eGCS_WGS_1984_BD09LL, // 目标空间参考
          )
          console.log(pPoint.getX() + ',' + pPoint.getY())
          const _lon = pPoint.getX() || resultObject.longitude
          const _lat = pPoint.getY() || resultObject.latitude

          position = { longitude: _lon, latitude: _lat }
        }
      }
    } catch (error) {
      position = { longitude: 0, latitude: 0 }
      uni.showToast({
        title: 'GPS定位信号弱',
        icon: 'none',
      })
    }
  } else {
    position = await getCurrentPosition()
  }
  // // console.log(position, 'position=========')
  const info = {
    coordType: 'bd09ll',
    longitude: position.longitude,
    latitude: position.latitude,
    // longitude: 117.25657759776065,
    // latitude: 31.75372889151655,
    orgCode: userInfo.orgCode,
    // orgCode: 'bbeda7195cea46e8a5a63d5ee1b178db',
  }
  // coordType: any, longitude: any, latitude: any, orgCode
  const res = await getWatermarkBizData(info.coordType, info.longitude, info.latitude, info.orgCode)
  // // console.log('res---->:', res)
  if (res.code === 'success') {
    fileInfo.value = res.data
  }
}
onLoad(async () => {
  getWatermark()
  // const position = await getCurrentPosition()
  // // console.log(position, 'position=========')
})

const useAsyncUpload = () => {
  const loading = ref(false)
  const error = ref(false)
  /**
   * @param param0 参数为本地 imgurl
   * 只适合上传单张照片，
   */
  const asyncUploadFile = <T,>({ imgUrl }): Promise<T> => {
    // console.log('url')
    // 将 Base64 编码的数据转换为二进制数据
    function base64ToBlob(base64, contentType) {
      const byteCharacters = atob(base64.split(',')[1])
      const byteNumbers = new Array(byteCharacters.length)
      for (let i = 0; i < byteCharacters.length; i++) {
        byteNumbers[i] = byteCharacters.charCodeAt(i)
      }
      const byteArray = new Uint8Array(byteNumbers)
      return new Blob([byteArray], { type: contentType })
    }

    // 将 Base64 编码的图像数据转换为 Blob 对象
    const imageBlob = base64ToBlob(imgUrl, 'image/png')

    // 创建一个 File 对象（可选）
    const file = new File([imageBlob], 'image.png', { type: 'image/png' })
    // console.log(file, 'file')
    // 现在你可以使用这个文件对象进行上传或其他操作
    return new Promise((resolve, reject) => {
      const formData = new FormData()
      formData.append('address', fileInfo.value.address)
      formData.append('dateTime', fileInfo.value.dateTime)
      formData.append('logoImageUrl', fileInfo.value.logoImageUrl)
      formData.append('watermarkProjectName', fileInfo.value.projectName)
      formData.append('sourceImageUrl', fileInfo.value.sourceImageUrl)
      formData.append('unitName', fileInfo.value.unitName)
      formData.append('userName', fileInfo.value.userName)
      formData.append('waterTemplateName', fileInfo.value.waterTemplateName)
      formData.append('weather', fileInfo.value.weather)
      formData.append('workContent', '隐患排查')
      formData.append('files', file)
      const apiClient = axios.create({
        baseURL: VITE_SERVER_BASEURL, // 基础URL
        timeout: 30000,
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      })
      apiClient
        .post(
          VITE_APP_PROXY_PREFIX + 'ehs-clnt-hazard-service' + VITE_UPLOAD_IMAGE_BASEURL,
          formData,
          {
            headers: {
              'Content-Type': 'multipart/form-data',
              Accept: '*/*',
            },
          },
        )
        .then((response) => {
          // console.log('文件上传成功:', response.data)
          resolve(response.data as T)
        })
        .catch((error) => {
          reject(error)
        })
        .finally(() => {
          loading.value = false
        })
      // uni.uploadFile({
      //   url: VITE_UPLOAD_IMAGE_BASEURL,
      //   filePath: imgUrl,
      //   name: 'files',
      //   formData,
      //   success: (uploadFileRes) => {
      //     // data.value = uploadFileRes.data as T
      //     resolve(uploadFileRes as T)
      //   },
      //   fail: (err) => {
      //     reject(err)
      //   },
      //   complete: () => {
      //     loading.value = false
      //   },
      // })
    })
  }

  return { asyncUploadFile, error, loading }
}

const { asyncUploadFile, loading } = useAsyncUpload()

const img = ref('')

const handlerSuccess = (val) => {
  showCamera.value = false
  // // console.log(val, '=========img')
  img.value = val
}

function handleCancel() {
  uni.navigateBack()
}

function handleDeny(denied: number) {
  // console.log('denyed', denied)
  uni
    .showToast({
      title: '相机权限被拒绝，请手动开放相机权限',
    })
    .then(() => {
      handleCancel()
    })
}

async function submitForm() {
  // // console.log('submit', img.value)
  if (img.value) {
    try {
      const res = (await asyncUploadFile({
        imgUrl: img.value,
      })) as any
      // console.log(res.data, '=====上传完成')
      uni.setStorageSync('face-photo', res.data ?? {})
      // await uni.showToast({
      //   title: '照片已经提交',
      // })
      uni.navigateBack()
      // // console.log(res)
    } catch (e) {
      // console.log(e)
    }
  } else {
    await uni.showToast({
      title: '请拍照后再提交',
      icon: 'error',
    })
  }
}

const { checkCamera } = useCamera()
const showCamera = ref(false)

const prepareCamera = () => {
  img.value = ''
  if (currentEnvInfo.miniprogram ?? false) {
    showCamera.value = true
  } // relativeInfo.value.plus ?? false
  else if (currentEnvInfo.osAndroid ?? false) {
    showCamera.value = true
    // checkCamera()
    //   .then((res) => {
    //     if (res === PermissionRes.PASS) {
    //       showCamera.value = true
    //     } else if (res === PermissionRes.REJECTED) {
    //       uni
    //         .showToast({
    //           title: '请前往设置中打开权限',
    //         })
    //         .then(() => {
    //           uni.navigateBack()
    //         })
    //     } else if (res === PermissionRes.TOOPEN) {
    //       uni
    //         .showToast({
    //           title: '请重新进入应用，并打开相机权限',
    //         })
    //         .then(() => {
    //           uni.navigateBack()
    //         })
    //     } else {
    //       uni
    //         .showToast({
    //           title: '未知权限结果，请联系管理员',
    //         })
    //         .then(() => {
    //           uni.navigateBack()
    //         })
    //     }
    //   })
    //   .catch((e) => {
    //     uni
    //       .showToast({
    //         title: String(e),
    //       })
    //       .then(() => {
    //         uni.navigateBack()
    //       })
    //   })
  } else {
    // console.log('current is h5 web')
    showCamera.value = true
  }
}

onMounted(() => {
  prepareCamera()
})
</script>
<style lang="scss" scoped>
::v-deep {
  // .navbar {
  //   position: unset !important;
  // }
}

.imgBox {
  // border: 2px solid red;
  width: 100vw;
  height: auto;
}
</style>
