<template>
  <wd-card v-for="item in initialData" :key="item.id" @click="selinfo(item.id)">
    <template #title>
      <view class="title">
        <view
          style="
            overflow: hidden;
            font-family: 'Alibaba PuHuiTi 2-65 Medium';
            font-size: 1rem;
            color: #000000;
            text-overflow: ellipsis;
            white-space: nowrap;
          "
        >
          {{ item.hazardDescribe || '--' }}
        </view>
        <!-- <view class="title-tip">
          <wd-button
            :type="
              item.confirmState === 0 ? 'warning' : item.confirmState === 1 ? 'primary' : 'success'
            "
            size="small"
            plain
          >
            {{ item.confirmStateName }}
          </wd-button>
        </view> -->
      </view>
    </template>
    <view class="content">
      <view>
        <!-- <view class="content_one">
          <view style="width: 45%; min-width: 45%" class="fontoverflow">{{ item.unitName }}</view>
          <view style="width: 45%; min-width: 45%" class="fontoverflow">
            {{ item.hazarePlace }}
          </view>
        </view> -->
        <view class="content_info">隐患单位：{{ item.unitName || '--' }}</view>
        <view class="content_info">隐患位置：{{ item.hazarePlace || '--' }}</view>
        <view class="content_info">检查人：{{ item.createByName || '--' }}</view>
      </view>
    </view>
    <view class="custominfo">
      <view class="content_info">上报时间：{{ item.createTime }}</view>
    </view>
  </wd-card>
</template>

<script lang="ts" setup>
import { ResponsesData, pageData, pageForm, rowsForm } from '../type'
defineProps<{
  initialData: any[]
}>()
const loading = ref(false)

// 时间图标按钮
function selinfo(id: string) {
  uni.navigateTo({
    url: `/pages/hazardPhoto/detail?id=${id}`,
  })
}
</script>

<style lang="scss">
::v-deep {
  .wd-card__title-content {
    padding: 10px 0;
  }

  .wd-card__title-content {
    padding: 10px 0 !important;
  }

  .wd-card__footer {
    padding: 6px 0 !important;
  }

  // .wd-card {
  //   background-color: #ebeef5 !important;
  // }
}

.wd-card {
  background-color: #ebeef5 !important;
}

// .content,
// .title {
//   // display: flex;
//   // flex-direction: row;
//   // align-items: center;
//   // justify-content: flex-start;
// }
// .content {
//   justify-content: flex-start;
// }
// .title {
//   justify-content: space-between;
// }
.content_one {
  display: flex;
  justify-content: space-between;
  font-size: 14px;
  color: #222222;
}

.title-tip {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.25);
}

.custominfo {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}

.content_info {
  margin-top: 8px;
  overflow: hidden;
  font-size: 14px;
  color: #222222;
  text-overflow: ellipsis;
  white-space: nowrap;
}

// .fontoverflow {
//   overflow: hidden;
//   text-overflow: ellipsis;
//   white-space: nowrap;
// }
</style>
