<route lang="json5">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '隐患复查详情',
  },
}
</route>

<template>
  <view class="container">
    <view class="infobox">
      <view class="infobox_title">
        <view class="fasticon"></view>
        <view>{{ info1Itme.hazardDescribe || '--' }}</view>
      </view>
      <view class="infoItme">
        <view class="infokey">单位</view>
        <view class="infovalue">{{ info1Itme.unitName || '--' }}</view>
      </view>
      <view class="infoItme">
        <view class="infokey">楼栋</view>
        <view class="infovalue">{{ info1Itme.buildingName || '--' }}</view>
      </view>
      <view class="infoItme">
        <view class="infokey">楼层</view>
        <view class="infovalue">{{ info1Itme.floorName || '--' }}</view>
      </view>
      <view class="infoItme">
        <view class="infokey">隐患位置</view>
        <view class="infovalue">{{ info1Itme.hazardPlace || '--' }}</view>
      </view>
      <view class="infoItme">
        <view class="infokey">隐患来源</view>
        <view class="infovalue">{{ info1Itme.hazardSourceName || '--' }}</view>
      </view>
      <view class="infoItme">
        <view class="infokey">检查人</view>
        <view class="infovalue">{{ info1Itme.createByName || '--' }}</view>
      </view>
      <view class="infoItme">
        <view class="infokey">上报时间</view>
        <view class="infovalue">{{ info1Itme.createTime || '--' }}</view>
      </view>

      <view class="infoItme">
        <view class="infokey" style="min-width: 5.25rem">隐患图片</view>
        <view
          class="infovalue"
          style="display: flex; flex-direction: row; flex-wrap: wrap; justify-content: flex-end"
        >
          <view
            class="fileList"
            v-for="(item, index) in info1Itme.hazardEventAttachmentPoList"
            :key="index"
          >
            <!-- <wd-img
              style="width: 100%; height: 90%"
              :src="VITE_PREVIEW_BASEURL + item.attachPath"
              :enable-preview="true"
            /> -->
            <wd-img
              style="width: 100%; height: 90%"
              :src="getFileURL(item.attachPath, true)"
              @click="previewImage(getFileURL(item.attachPath))"
            />
          </view>
        </view>
      </view>
    </view>
  </view>
  <comOverlay ref="overlay" v-if="imageshow" :imgurl="imgurl" @closeimg="closeimg"></comOverlay>
</template>

<script lang="ts" setup>
import { useAppStore, usefileConfigStore } from '@/store'
import comOverlay from '@/components/commonSelect/com-overlay.vue'
import { getFileURL } from '@/utils/index'
// const VITE_PREVIEW_BASEURL = import.meta.env.VITE_PREVIEW_BASEURL
defineProps({
  info1Itme: {
    type: Object,
  },
})

const overlay = ref(null)
const imageshow = ref(false)
const imgurl = ref('')
const previewImage = (url) => {
  imageshow.value = true
  imgurl.value = url
}
// 关闭查看图片
function closeimg(val) {
  imageshow.value = val
}
</script>

<style lang="scss" scoped>
body {
  background-color: #f9f9f9 !important;
}

.infobox {
  padding-bottom: 1.125rem;
  margin-bottom: 1rem;
  background-color: white;

  .infobox_title {
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 0.25rem 0 0.9375rem 1.125rem;

    .fasticon {
      width: 0.25rem;
      height: 0.875rem;
      margin-right: 0.1875rem;
      background-color: #597bf7;
      border-radius: 1.125rem;
    }
  }

  .infoItme {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    padding: 0.5rem 1rem 0.5rem 1rem;
    font-size: 0.875rem;

    .infokey {
      min-width: 6.25rem;
      color: #7f7f7f;
    }
  }
}

.fixed-bottom {
  position: fixed;
  bottom: 0;
  width: 100%;
  height: 3.375rem;
  line-height: 3.375rem;
  color: #fff;
  text-align: end;
  background-color: white;
  border-top: 0.3px solid #ccc;
}

.fileList {
  width: 4.125rem;
  height: 4.125rem;
  margin: 0.5rem;
  text-align: center;
  border-radius: 0.375rem;
}
</style>
