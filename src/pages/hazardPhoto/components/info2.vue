<route lang="json5">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '隐患复查详情',
  },
}
</route>

<template>
  <view class="container">
    <view class="infobox">
      <view class="infoItme" style="padding: 0.875rem; border-bottom: 0.3px solid #ebebeb">
        <view class="infokey" style="font-size: 1rem; color: #1a1a1a">随手拍审核</view>
        <view class="infovalue" :style="info2Itme.confirmState === 1
          ? 'color: #F39600'
          : info2Itme.confirmState === 0
            ? ''
            : 'color: #0256FF'
          ">
          <!-- {{ info2Itme.confirmStateName }} -->
          {{
            info2Itme.confirmState === 1
              ? '隐患'
              : info2Itme.confirmState === 0
                ? info2Itme.confirmStateName
                : '非隐患'
          }}
        </view>
      </view>
      <view v-if="info2Itme.confirmState === 1">
        <view class="infoItme">
          <view class="infokey">隐患描述</view>
          <view class="infovalue">{{ info2Itme.hazardDescribe }}</view>
        </view>
        <view class="infoItme">
          <view class="infokey">隐患分类</view>
          <view class="infovalue">{{ info2Itme.hazardCategoryName }}</view>
        </view>
        <view class="infoItme">
          <view class="infokey">隐患级别</view>
          <view class="infovalue">{{ info2Itme.gradeName }}</view>
        </view>
        <view class="infoItme">
          <view class="infokey">隐患位置</view>
          <view class="infovalue">{{ info2Itme.hazardPlace }}</view>
        </view>
        <view class="infoItme">
          <view class="infokey">隐患来源</view>
          <view class="infovalue">{{ info2Itme.hazardSourceName }}</view>
        </view>
        <view class="infoItme">
          <view class="infokey">隐患整改人员</view>
          <view class="infovalue">
            <span v-for="(item, index) in info2Itme.hazardRandomCheckEventUsers" :key="index">
              {{ item.reformUserName }}
              {{ index === info2Itme.hazardRandomCheckEventUsers.length - 1 ? '' : ',' }}
            </span>
          </view>
        </view>
        <view class="infoItme" v-if="info2Itme.confirmState !== 0">
          <view class="infokey" style="min-width: 4.25rem">备注</view>
          <view class="infovalue">{{ info2Itme.remark }}</view>
          <!-- <view
            class="infovalue"
            style="display: flex; flex-direction: row; flex-wrap: wrap; justify-content: flex-end"
          >
            <view
              class="fileList"
              v-for="(item, index) in info2Itme.hazardEssentialFactorClassItemFiles"
              :key="index"
            >
              <wd-img
                style="width: 100%; height: 90%"
                :src="VITE_PREVIEW_BASEURL + item.fileUrl"
                :enable-preview="true"
              />
            </view>
          </view> -->
        </view>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { useAppStore, usefileConfigStore } from '@/store'

// const VITE_PREVIEW_BASEURL = import.meta.env.VITE_PREVIEW_BASEURL
// interface info1Itme {
//   id: number
//   name: string
// }
defineProps({
  info2Itme: {
    type: Object,
  },
})
// const VITE_PREVIEW_BASEURL = ref('')
const usefileStore = usefileConfigStore()
const VITE_PREVIEW_BASEURL = ref()
const currentEnvInfo = useAppStore().appEnv
if (!currentEnvInfo.wychApp) {
  VITE_PREVIEW_BASEURL.value = usefileStore.fileUrl.fileUrlPrefixMiniProgram + '/'
} else {
  VITE_PREVIEW_BASEURL.value = usefileStore.fileUrl.fileUrlPrefix + '/'
}

// const VITE_PREVIEW_BASEURL = ref(usefileStore.fileUrl + '/')
// getbaseurl()
// // eslint-disable-next-line camelcase
// function getbaseurl() {
//   const dynamicsUrl = location.origin
//   if (location.protocol === 'https:') {
//     return (VITE_PREVIEW_BASEURL.value = dynamicsUrl + import.meta.env.VITE_PREVIEW_BASEURL_dev)
//   } else {
//     return (VITE_PREVIEW_BASEURL.value = import.meta.env.VITE_PREVIEW_BASEURL)
//   }
// }
</script>

<style lang="scss" scoped>
body {
  background-color: #f9f9f9 !important;
}

.infobox {
  padding-bottom: 1.125rem;
  margin-bottom: 1rem;
  background-color: white;

  .infobox_title {
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 0.25rem 0 0.9375rem 1.125rem;

    .fasticon {
      width: 0.25rem;
      height: 0.875rem;
      margin-right: 0.1875rem;
      background-color: #597bf7;
      border-radius: 1.125rem;
    }
  }

  .infoItme {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    padding: 0.5rem 1rem 0.5rem 1rem;
    font-size: 0.875rem;

    .infokey {
      min-width: 4.25rem;
      color: #7f7f7f;
    }
  }
}

.fixed-bottom {
  position: fixed;
  bottom: 0;
  width: 100%;
  height: 3.375rem;
  line-height: 3.375rem;
  color: #fff;
  text-align: end;
  background-color: white;
  border-top: 0.3px solid #ccc;
}

.fileList {
  width: 4.125rem;
  height: 4.125rem;
  margin: 0.5rem;
  text-align: center;
  border-radius: 0.375rem;
}
</style>
