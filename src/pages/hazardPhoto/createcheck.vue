<route lang="json5">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '新增隐患',
  },
}
</route>

<template>
  <SafetyNavbar title="新增隐患"></SafetyNavbar>
  <view class="flex flex-col">
    <scroll-view style="height: 100vh" scroll-y="true">
      <wd-form ref="form" :model="model">
        <!-- 隐患单位 -->
        <commonUnit
          :selectvalue="model.unitId"
          :selecttext="model.unitName"
          @getGrade="getUnit"
          :types="'1'"
        ></commonUnit>

        <comHazardSource :title="'隐患来源'" @senddate="handleConfirm2"></comHazardSource>
        <!-- <wd-select-picker label="隐患来源" @open="openviws" placeholder="请选择隐患来源" z-index="999" type="radio"
        :show-confirm="false" align-right v-model="model.hazardSourceNamevalue" value-key="hazardSourceName"
        label-key="hazardSourceName" required :columns="hazardSourcecolumns" @confirm="handleConfirm2" /> -->

        <wd-cell required title="隐患图片"></wd-cell>
        <view style="display: flex">
          <!-- <view style="width: 100px; min-width: 100px" class="required">隐患图片</view> -->
          <uploadButton @getFilelist="getFilelist" :imginfo="[]"></uploadButton>
        </view>
        <wd-cell-group border>
          <!-- 隐患描述 -->
          <!-- <commonHazardDescribe></commonHazardDescribe> -->
          <!-- <view class="com-cell" style="margin-left: 1rem">
          <wd-textarea v-model="model.hazardDescribe" auto-height placeholder="隐患描述" />
        </view> -->
          <wd-textarea
            label="隐患描述"
            type="textarea"
            label-width="100px"
            :maxlength="100"
            auto-height
            clearable
            show-word-limit
            prop="hazardDescribe"
            required
            v-model="model.hazardDescribe"
            placeholder="请输入隐患描述信息"
          />
          <!-- <wd-select-picker filterable label="隐患单位" v-model="model.unitId" align-right type="radio" @change="changeUnit"
          @confirm="getUnit" :columns="unitcolumns" value-key="id" label-key="unitName" placeholder="请选择" required
          auto-height></wd-select-picker> -->
          <!-- <wd-cell required title="楼栋楼层" :value="buildinglabel" is-link @click="handleposition" clickable /> -->
          <wd-cell
            title="楼栋楼层"
            title-width="100px"
            class="text"
            :value="buildinglabel"
            is-link
            @click="handleposition"
            clickable
            required
          />
          <wd-cell-group border>
            <div style="display: flex">
              <div style="padding-left: 15px; color: red">*</div>
              <div style="padding-left: 5px; font-size: 14px">隐患位置</div>
            </div>
            <wd-input
              style="padding-left: 20px; padding-right: 20px"
              prop="hazardPlace"
              label-width="100px"
              :maxlength="50"
              required
              show-word-limit
              v-model="model.hazardPlace"
              placeholder="请描述隐患详细位置"
              use-prefix-slot
            ></wd-input>
          </wd-cell-group>
        </wd-cell-group>

        <view class="fixed-bottom">
          <wd-button type="info" size="large" @click="goBack" block>取消</wd-button>
          <wd-button type="primary" size="large" @click="handleSubmit" :disabled="isdispaly" block>
            提交
          </wd-button>
        </view>
      </wd-form>
    </scroll-view>
  </view>
</template>

<script lang="ts" setup>
import SafetyNavbar from '../components/safety-navbar.vue'
import uploadButton from '@/components/upload/upload-button.vue'
import commonHazardDescribe from '@/components/commonSelect/common-hazardDescribe.vue'
import { postAllUnitAPI, posthazardPhotoCasuallyAddAPI, posthazardSourceListAPI } from './featch'
import { useUserStore } from '@/store'
import { debounce } from '@/utils'
import commonUnit from '@/components/commonSelect/common-Unit.vue'
import comHazardSource from '@/components/commonSelect/com-hazardSource.vue'
// import indoorMap from '../components/indoorMap/dom.vue'
/* 获取登录人信息 */
const userStore = useUserStore()
const userinfo: any = userStore.userInfo ? userStore.userInfo : {}
const model = ref<any>({
  hazardDescribe: '', // 隐患描述
  hazardPlace: '', // 隐患位置
  buildingId: '', // 楼栋id
  floorId: '', // 楼层id
  filePathList: [], // 文件地址
  unitId: '', // 单位id
  unitName: '', // 单位
  createBy: userinfo.id, // 创建人
  createByName: userinfo.userName, // 创建人名称
  createUserDepartmentId: userinfo.deptId, // 创建人组织机构id
  createUserDepartmentName: userinfo.deptName, // 创建人组织机构
  superviseId: userinfo.parentOrgCode, // 分管id
  mapX: '',
  mapY: '',
  hazardSourceName: '',
  hazardSourceNamevalue: '',
})
const show = ref(false)
const buildinglabel = ref()
const isdispaly = ref<any>(false)
onShow(() => {
  // if (uni.getStorageSync('FactorClassItem')) {
  //   model.value.hazardDescribe = uni.getStorageSync('FactorClassItem').hazardDescribe
  // }
  if (uni.getStorageSync('Building')) {
    model.value.floorId = uni.getStorageSync('Building').value
    // model.value.hazardPlace =
    // uni.getStorageSync('BuildingFool').label + uni.getStorageSync('Building').label
    model.value.buildingId = uni.getStorageSync('Building').parentId
    buildinglabel.value =
      uni.getStorageSync('BuildingFool').label + uni.getStorageSync('Building').label
  }
  if (uni.getStorageSync('pointerinfo')) {
    model.value.mapX = uni.getStorageSync('pointerinfo').x
    model.value.mapY = uni.getStorageSync('pointerinfo').y
  }
})
// 获取图片列表
function getFilelist(event) {
  // // console.log(event.value, '=============filelist.value')
  model.value.filePathList = event.value
}
/* 获取位置 */
function handleposition() {
  if (model.value.unitId) {
    uni.navigateTo({
      url: `/pages/hazardPhoto/common-position?selunitid=${model.value.unitId}`,
    })
  } else {
    uni.showToast({
      icon: 'none',
      title: '请先选择隐患单位',
    })
  }
}
const unitcolumns = ref<any>([])
if (userinfo.orgRes === '1' && userinfo.unitOrgType === '1') {
  getAllUnit()
}
function getAllUnit() {
  model.value.unitId = userinfo.unitId
  model.value.unitName = userinfo.unitName
  // postAllUnitAPI({ orgCode: userinfo.unitId, pageSize: -1 })
  //   .then((res: any) => {
  //     if (res.data.rows) {
  //       unitcolumns.value.push(...res.data.rows)
  //     } else {
  //       unitcolumns.value.push({
  //         unitName: userinfo.unitName,
  //         id: userinfo.unitId,
  //       })
  //     }
  //   })
  //   .finally(() => { })
}
// 隐患来源
// const hazardSourcecolumns = ref<any>([])
// hazardSourceList()
// function openviws() {
//   // console.log(111)
//   if (hazardSourcecolumns.value.length === 0) {
//     uni.showToast({
//       title: '无可用选项，请联系管理员在参数配置中进行配置。',
//       icon: 'none',
//     })
//   }
// }
// function hazardSourceList() {
//   // console.log(unitIdval, '================调用获取隐患等级')
//   posthazardSourceListAPI({})
//     .then((res: any) => {
//       hazardSourcecolumns.value = res.data
//     })
//     .finally(() => {
//       // console.log(111)
//     })
// }

// 选择隐患来源
function handleConfirm2(data) {
  // types
  // model.value.hazardSourceName = `随手拍-${data.hazardSourceName}-${data.sourceType === '1' ? '内部隐患' : '外部隐患'}`
  model.value.hazardSourceName = `随手拍-${data.hazardSourceName}`
  // console.log(selectedItems, 'selectedItems选择隐患来源')
}

const oldvalue = ref('')
function changeUnit(event) {
  // newvalue.value = event.value
}

function getUnit(data) {
  console.log(data, '=====data')
  model.value.unitId = data.id
  model.value.unitName = data.text
  if (model.value.unitId !== oldvalue.value) {
    model.value.floorId = ''
    model.value.hazardPlace = ''
    model.value.buildingId = ''
    buildinglabel.value = ''
    model.value.mapX = ''
    model.value.mapY = ''
  }
  oldvalue.value = data.id
}
// 选点
function SelectXY() {
  if (model.value.buildingId) {
    uni.navigateTo({
      url: `/pages/map/index?buildId=${model.value.buildingId}&floorId=${model.value.floorId}&x=${model.value.mapX}&y=${model.value.mapY}&status=1&unitId=${model.value.unitId}`,
    })
  } else {
    uni.showToast({
      icon: 'none',
      title: '请先选择楼栋楼层',
    })
  }
}
// 关闭页面
const goBack = () => {
  uni.navigateBack()
}
// 提交操作
const handleSubmit = debounce(() => {
  // console.log(model.value)
  isdispaly.value = true
  // console.log(model.value, '=====tijiao ')
  // ========非空判断开始=======
  if (!model.value.unitId) {
    uni.showToast({
      icon: 'none',
      title: '请选择隐患单位',
    })
    isdispaly.value = false
    return
  }
  if (!model.value.hazardSourceName) {
    uni.showToast({
      icon: 'none',
      title: '请选择隐患来源',
    })
    isdispaly.value = false
    return
  }
  if (model.value.filePathList.length === 0) {
    uni.showToast({
      icon: 'none',
      title: '请上传隐患图片',
    })
    isdispaly.value = false
    return
  }
  if (!model.value.hazardDescribe) {
    uni.showToast({
      icon: 'none',
      title: '请输入隐患描述信息',
    })
    isdispaly.value = false
    return
  }
  if (!model.value.hazardPlace) {
    uni.showToast({
      icon: 'none',
      title: '请输入隐患位置',
    })
    isdispaly.value = false
    return
  }
  if (!model.value.mapX) {
    uni.showToast({
      icon: 'none',
      title: '请选择位置点位',
    })
    isdispaly.value = false
    return
  }

  // ======非空判断结束=====
  model.value.zhId = userinfo.zhId
  posthazardPhotoCasuallyAddAPI(model.value)
    .then((res: any) => {
      // // console.log(res.code)
      if (res.code === 'error') {
        uni.showToast({
          icon: 'none',
          title: res.message || '请求错误',
        })
        isdispaly.value = false
        return
      }
      // uni.removeStorageSync('FactorClassItem')
      uni.removeStorageSync('Building')
      uni.removeStorageSync('pointerinfo')
      uni.showToast({
        icon: 'none',
        title: '新增成功',
      })
      uni.navigateBack({
        delta: 1,
      })
    })
    .finally(() => {
      isdispaly.value = false
    })
}, 500)
onUnload(() => {
  uni.removeStorageSync('Building')
  uni.removeStorageSync('BuildingFool')
  uni.removeStorageSync('pointerinfo')
})
</script>

<style lang="scss">
::v-deep {
  .wd-cell__left {
    flex: none;
  }

  .wd-select-picker__value {
    color: #767676;
    text-align: end;
  }

  .wd-select-picker__cell {
    border-bottom: 0.3px solid #ebebeb;
  }

  .wd-button.is-block {
    width: 100% !important;
    height: 100% !important;
    border-radius: 0 !important;
  }

  .wd-textarea ::after {
    height: 0;
    background-color: #dadada;
  }

  // .wd-textarea ::after {
  //   background: #dadada;
  // }
}

.required::before {
  // position: absolute;
  // left: 0;
  top: 0.125rem;
  line-height: 1.1;
  color: #fa4350;
  content: '*';
}

.fixed-bottom {
  position: fixed;
  bottom: 0;
  display: flex;
  flex-direction: row;
  align-items: center;
  width: 100%;
  height: 3.375rem;
  line-height: 3.375rem;
  color: #fff;
  text-align: center;
  background-color: white;
}
</style>
