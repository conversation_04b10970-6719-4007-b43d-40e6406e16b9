export type pageForm = {
  essentialFactorClassId: string
  essentialFactorClassItemId: string
  essentialFactorId: string
  hazardGradeId: string
  likeParam: string
  pageNo: number
  pageSize: number
  superviseIds: string
  unitIds: string
  startTime: string
  endTime: string
  /* 当前登录人id */
  createBy: string
  appLikeParam: string
  orgCode: string
  roleCodes: []
}
export type rowsForm = {
  essentialFactorId: string
  approveStateName: string
  essentialFactorClassItemId: string
  superviseId: string
  essentialFactorClassId: string
  deviceId: string
  confirmUserId: string
  approveUserId: string
  floorId: string
  hazarePlace: string
  hazardGradeId: string
  updateBy: string
  unitId: string
  id: string
  hazardGradeName: string
  approveTime: string
  approveState: number
  gradeName: string
  gradeId: string
  confirmState: number
  confirmTime: string
  updateTime: string
  buildingId: string
  hazardCategoryName: string
  createBy: string
  createTime: string
  hazardDescribe: string
  confirmStateName: string
  filePathList: []
  hazardPlace: string
  remark: string
  userIdList: string
  confirmUserName: string
}
export type pageData<T> = {
  pageNo: number
  pageSize: number
  pages: number
  rows: T[]
  total: number
}
export type ResponsesData<T> = {
  code: string
  data: T[]
  dataType: string
  message: object
  status: string
  token: string
}
