<route lang="json5">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '随手拍隐患详情',
  },
}
</route>

<template>
  <SafetyNavbar title="随手拍隐患详情"></SafetyNavbar>
  <view class="container">
    <scroll-view class="scroll-content" scroll-y="true">
      <info1 :info1Itme="hazardPhotoCasuallyDetail"></info1>
      <info2 :info2Itme="hazardPhotoCasuallyDetail"></info2>
    </scroll-view>
  </view>
</template>

<script lang="ts" setup>
import SafetyNavbar from '../components/safety-navbar.vue'
import info1 from './components/info1.vue'
import info2 from './components/info2.vue'
import { posthazardPhotoCasuallyDetailAPI } from './featch'
onLoad((params) => {
  gethazardPhotoCasuallyDetail(params.id)
})

const hazardPhotoCasuallyDetail = ref<any>({})
// 获取详情
function gethazardPhotoCasuallyDetail(id) {
  posthazardPhotoCasuallyDetailAPI(id)
    .then((res) => {
      // console.log(res)
      hazardPhotoCasuallyDetail.value = res.data
    })
    .finally(() => {
      // console.log(111)
    })
}
</script>

<style lang="scss" scoped>
body {
  background-color: #f9f9f9 !important;
}

.sheetbom {
  display: flex;
}
</style>
