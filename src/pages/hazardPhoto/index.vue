<route lang="json5">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '随手拍列表',
  },
}
</route>
<template>
  <z-paging ref="paging" v-model="initialData" @query="GethazardPhotoCasually">
    <template #top>
      <view style="">
        <SafetyNavbar title="随手拍列表" :searchShow="false"></SafetyNavbar>
        <wd-datetime-picker label="时间选择" :formatter="formatter" :display-format="displayFormat" @confirm="confirm"
          type="date" v-model="datevalue" :default-value="[Date.now(), '']"></wd-datetime-picker>
      </view>
      <!-- 13316 【app隐患随手拍】-页面设计与原型图不符，多了个搜索框 -->
      <!-- <wd-search
        placeholder-left
        placeholder="请输入隐患描述模糊搜索"
        @change="search"
        :hide-cancel="true"
        v-model="Parameters.appLikeParam"
        @clear="clear"
      /> -->
    </template>
    <view class="container">
      <view v-if="initialData.length">
        <customCard :initialData="initialData"></customCard>
      </view>
      <template v-else>
        <view class="list-null">
          <noData title="暂无数据~"></noData>
        </view>
      </template>
    </view>
  </z-paging>
  <view class="addbtn" @click="handleClickRight">
    <view>+</view>
  </view>
</template>

<script lang="ts" setup>
import SafetyNavbar from '../components/safety-navbar.vue'
import customCard from './components/custom-card.vue'
import { posthazardPhotoCasuallyAPI } from './featch'
import { pageForm, rowsForm } from './type'
import { useAppStore, useUserStore } from '@/store'
const { appInfo, appEnv } = useAppStore()
// const userinfo = {
//   userId: '1814204295333539841',
// }
const userStore = useUserStore()
const userinfo: any = userStore.userInfo ? userStore.userInfo : {}
// console.log(uni.getStorageSync('userinfo'))
const datevalue = ref(['', ''])
const Parameters = ref<pageForm>({
  essentialFactorClassId: '',
  essentialFactorClassItemId: '',
  essentialFactorId: '',
  hazardGradeId: '',
  likeParam: '',
  pageNo: 1,
  pageSize: 5,
  superviseIds: '',
  unitIds: '',
  startTime: '',
  endTime: '',
  createBy: userinfo.id,
  orgCode: userinfo.orgRes === '1' ? userinfo.unitId : '',
  roleCodes: userinfo.roleCodes,
  appLikeParam: '',
})
const paging = ref()
onLoad((params) => {
  paging.value.reload()
})
onShow(() => {
  paging.value.reload()
})
const total = ref(0)
const initialData = ref<rowsForm[]>([])
// GethazardPhotoCasually()
function GethazardPhotoCasually(pageNo) {
  uni.showLoading({ mask: true })
  Parameters.value.pageNo = pageNo
  posthazardPhotoCasuallyAPI(Parameters.value)
    .then((res: any) => {
      uni.hideLoading()
      total.value = res.data.total
      paging.value.completeByTotal(res.data.rows, total.value)
    })
    .finally(() => {
      uni.hideLoading()
    })
}

function handleClickRight() {
  // 新增
  uni.removeStorageSync('Building')
  uni.removeStorageSync('BuildingFool')
  uni.navigateTo({
    url: `/pages/hazardPhoto/createcheck`,
  })
}
// 选择时间confirm
function confirm() {
  Parameters.value.startTime = formatTimestamp(datevalue.value[0] as any)
  Parameters.value.endTime = formatTimestamp(datevalue.value[1] as any)
  Parameters.value.pageNo = 1
  GethazardPhotoCasually(Parameters.value.pageNo)
}
// 传值时间格式
const formatTimestamp = (timestamp: number): string => {
  const date = new Date(timestamp)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  return `${year}-${month}-${day}`
}
// 选择器显示时间格式
function displayFormat(items) {
  return `${items[0].label}-${items[1].label}-${items[2].label}`
}
// 选择器内部时间格式
function formatter(type, value) {
  switch (type) {
    case 'year':
      return value
    case 'month':
      return value
    case 'date':
      return value
  }
}
</script>

<style lang="scss">
::v-deep {
  .wd-button.is-medium.is-round {
    min-width: 0;
  }

  .navbar {
    position: unset !important;
  }
}

.addbtn {
  position: flex;
  position: absolute;
  right: 10%;
  bottom: 15%;
  z-index: 99;
  width: 2.5rem;
  height: 2.5rem;
  font-size: 3rem;
  font-weight: bold;
  line-height: 2.5rem;
  color: white;
  text-align: center;
  background-color: #2765d2b8;
  border-radius: 100%;
  box-shadow: 5px 5px 11px 0 rgba(0, 0, 0, 0.5);
}
</style>
