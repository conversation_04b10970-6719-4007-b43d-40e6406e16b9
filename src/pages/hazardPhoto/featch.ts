import { http } from '@/utils/http'
import { ResponsesData, pageData, pageForm } from './type'
import { $api } from '@/api'

/** post 请求 获取随手拍列表 hazardPhotoCasually/page   */
export const posthazardPhotoCasuallyAPI = (Parameters: pageForm) => {
  return http.post($api.type.hazard + '/hazardPhotoCasually/page', {
    ...Parameters,
  })
}

/** post 请求 获取隐患分类列表 hazardEssentialFactorClass/treeList   */
export const posthazardEssentialFactorClassAPI = (Parameters: any) => {
  return http.post($api.type.hazard + '/hazardEssentialFactorClass/treeList ', {
    ...Parameters,
  })
}

/** post 请求 新增随手拍隐患 /hazardPhotoCasually/add   */
export const posthazardPhotoCasuallyAddAPI = (Parameters: any) => {
  return http.post($api.type.hazard + '/hazardPhotoCasually/add', {
    ...Parameters,
  })
}

/** post 请求 随手拍隐患详情 /hazardPhotoCasually/detail?id=5dcb4621b82e4478930e09a6c6fbb454   */
export const posthazardPhotoCasuallyDetailAPI = (Parameters: any) => {
  return http.post($api.type.hazard + '/hazardPhotoCasually/detail?id=' + Parameters)
}

/** post 请求 随手拍隐患分类 /hazardEssentialFactorClass/treeList  */
export const postEssentialFactorClassAPI = (Parameters: any) => {
  return http.post($api.type.hazard + '/hazardEssentialFactorClass/treeList', {
    ...Parameters,
  })
}

/** post 请求 获取单位列表 hazardGrade/list   */
export const postAllUnitAPI = (Parameters: any) => {
  return http.post($api.type.hazard + '/ehsUpms/getAllUnit', {
    ...Parameters,
  })
}
// /eBuilding/getBuildingTreeByUnitId
/* 获取楼栋楼层列表 /eBuilding/getUnitBuildingInfoList */
export const postBuildingTreeByUnitIdAPI = (Parameters: any) => {
  return http.post($api.type.hazard + '/eBuilding/getBuildingTreeByUnitId?unitId=' + Parameters)
}
// /* 获取楼层列表 eBuilding/getFloorListByUnitIdAndBuilding?buildId=610000DW1823561724595273728_001&orgCode=888 */
// export const postFloorListByUnitIdAndBuildingAPI = (Parameters: any) => {
//   return http.post(
//     `/eBuilding/getFloorListByUnitIdAndBuilding?orgCode=${Parameters.orgCode}&buildId=${Parameters.buildId}`,
//   )
// }
// 获取隐患来源列表
export const posthazardSourceListAPI = (Parameters: any) => {
  return http.post($api.type.hazard + '/hazardSourceConfig/list', Parameters)
}
