<route lang="json5">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '选择楼栋楼层',
  },
}
</route>
<template>
  <safetyNavbar title="选择楼栋楼层"></safetyNavbar>
  <div class="steps-box">
    <div :class="[activeindex == 0 ? 'steps_active' : '', 'steps']">1.选择楼栋楼层</div>
    <div :class="[activeindex == 1 ? 'steps_active' : '', 'steps']">2.选择坐标点位</div>
  </div>
  <view v-if="activeindex == 0" class="wraper">
    <wd-sidebar v-model="active" @change="handleChange">
      <wd-sidebar-item v-for="(item, index) in unitBuildingInfoList" :key="index" :value="index" :label="item.label" />
    </wd-sidebar>
    <view class="content" :style="`transform: translateY(-${active * 100}%)`">
      <scroll-view v-for="(item, index) in unitBuildingInfoList" :key="index" class="category" scroll-y>
        <wd-cell-group border>
          <wd-cell v-for="(cell, index) in item.children" :key="index" :title="cell.label" clickable
            @click="selBuilding(cell)">
            {{ BuildingFool?.value === cell.value ? '√' : '' }}
          </wd-cell>
        </wd-cell-group>
      </scroll-view>
    </view>
  </view>
  <view v-else>
    <floorMap :floor-info="floorData" :isAddMark="true" :pointer="pointer" @add-mark="addMark"></floorMap>
    <view class="fixed-bottom">
      <wd-button type="info" size="medium" @click="activeindex = 0">返回上一步</wd-button>
      <wd-button size="medium" @click="handleClickRight">确定选择</wd-button>
    </view>
  </view>
</template>
<script lang="ts" setup>
import safetyNavbar from '@/pages/components/safety-navbar.vue'
import { postBuildingTreeByUnitIdAPI } from './featch'
import { useUserStore } from '@/store'
import floorMap from '../../components/indoorMap/index.vue'
import { postqueryErecordBuildingFloorListAPI } from '../map/featch'

const userStore = useUserStore()
const userinfo: any = userStore.userInfo ? userStore.userInfo : {}
const activeindex = ref<number>(0)
const active = ref<number>(0)
const unitBuildingInfoList = ref<any>([])
// getunitBuildingInfoList()
const unitid = ref<any>('')
const BuildingFool = ref<any>()
const pointer = ref<any>({ x: '', y: '' }) // 回显点位
const getBuilding = ref<any>()


const floorData = ref({
  unitId: '',
  buildingId: '',
  floorId: '',
  floorAreaImg: '',
})

onLoad((paramas) => {
  const id = paramas.selunitid ? paramas.selunitid : userinfo.unitId
  unitid.value = id
  getunitBuildingInfoList(id)
  getBuilding.value = uni.getStorageSync('Building') || {}
  if (getBuilding.value) {
    active.value = getBuilding.value.active
    BuildingFool.value = getBuilding.value
  }
})

function getunitBuildingInfoList(id) {
  postBuildingTreeByUnitIdAPI(id)
    .then((res: any) => {
      // // console.log(res)
      if (res.data && res.data.options.length > 0) {
        unitBuildingInfoList.value = res.data.options
        uni.setStorageSync('BuildingFool', res.data.options[0])
      } else {
        uni.showToast({
          title: '该单位尚未开通',
          icon: 'none',
        })
      }

      // active.value = res.data.options[0].value
    })
    .finally(() => { })
}
/* 选择楼栋 */
function handleChange(event) {
  uni.setStorageSync('BuildingFool', event)
  active.value = event.value
}


/* 选择楼层 */
function selBuilding(event) {
  BuildingFool.value = event
  console.log(BuildingFool.value)
  uni.setStorageSync('Building', { ...event, active: active.value })
  const getpoint = uni.getStorageSync('pointerinfo') || {}
  if (getBuilding.value?.value === floorData.value.floorId) {
    pointer.value.x = getpoint.x
    pointer.value.y = getpoint.y
    console.log('getpoint', pointer)
  }
  postqueryErecordBuildingFloorListAPI({
    unitId: unitid.value,
    buildingId: event.parentId,
    floorId: event.value,
  })
    .then((res: any) => {
      // console.log(res)
      if (res.data.rows.length === 0) {
        uni.showToast({
          icon: 'none',
          title: '没有找到该信息数据',
        })
        return
      }
      floorData.value.unitId = res.data.rows[0].unitId
      floorData.value.buildingId = res.data.rows[0].buildingId
      floorData.value.floorId = res.data.rows[0].floorId
      floorData.value.floorAreaImg = res.data.rows[0].floorAreaImg
      activeindex.value = 1
      console.log(getBuilding.value?.value, floorData.value.floorId)
      if (getBuilding.value?.value === floorData.value.floorId) {
        pointer.value.x = getpoint.x
        pointer.value.y = getpoint.y
        console.log('getpoint', pointer)
      } else {
        pointer.value.x = ''
        pointer.value.y = ''
      }
    })
    .finally(() => { })
}

let pointeinfo = null
function handleClickRight() {
  uni.setStorageSync('pointerinfo', pointeinfo)
  uni.navigateBack()
}
const addMark = (val: { x: number; y: number; text: string }) => {
  // console.log('🚀 ~ addMark ~ val:', val)
  pointeinfo = val
}
</script>
<style lang="scss">
.steps-box {
  display: flex;
  justify-content: space-evenly;
  align-items: center;
  padding: 10px 0px;

  .steps {
    background: #ccc;
    padding: 4px 16px;
    font-size: 14px;
  }

  .steps::after {
    content: '';
    position: absolute;
    // top: 54px;
    margin: -4px 16px;
    // margin-left: 16px;
    border-top: 16px solid transparent;
    border-left: 15px solid #ccc;
    border-bottom: 13px solid transparent;
  }
}

.steps_active {
  background: #0703be !important;
  // background-color: #0703be;
  color: #fff;
}

.steps_active::after {
  border-left: 15px solid #0703be !important;
}

.fixed-bottom {
  position: fixed;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: space-around;
  width: 100%;
  height: 3.375rem;
  line-height: 3.375rem;
  // color: #fff;
  background-color: white;
}

::v-deep {
  .wd-sidebar {
    width: 200px;
    min-width: 200px;
  }

  .wd-badge {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.wraper {
  display: flex;
  height: calc(88vh - var(--window-top));
  height: calc(88vh - var(--window-top) - constant(safe-area-inset-bottom));
  height: calc(88vh - var(--window-top) - env(safe-area-inset-bottom));
  overflow: hidden;
}

.content {
  flex: 1;
  background: #fff;
  transition: transform 0.3s ease;
}

.category {
  box-sizing: border-box;
  height: 100%;
}

.selcet-hover {
  background-color: #0703be;
}
</style>
