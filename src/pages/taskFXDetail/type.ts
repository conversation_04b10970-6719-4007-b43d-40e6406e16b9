import { IPageRes } from '@/types/custom'

export interface IStatistics {
  deviceTotalNum: number
  checkedNum: number
  uncheckedNum: number
  abnormaNum: number
  normaNum: number
}

export interface IPointItem {
  checkInState: string
  checkInStateName: string
  checkInTime: string
  checkTableId: string
  checkTableName: string
  hazardNum: number
  id: string
  location: string
  photos: string
  pointFullName: string
  pointName: string
  building: string
  checkPointId: string
  checkUserName: string
  floor: string
  indexNo: number
  [props: string]: any
}

export type IPointList = IPointItem[]

export interface IHazardTaskDetailsData {
  uncheckedItemNum?: number // 未查数量
  checkItemedNum?: number // 已查数量
  totalNum?: number // 已查数量
  checkRange?: '1' | '2' | '3'
  isNeedClock?: '1' | '2' // 是否需要检查人现场打卡(1:是;2:否)
  planType?: string // 检查状态
  planTypeName?: string // 检查类型
  createByName?: string // 创建人
  createTime?: string // 创建时间
  frequency?: string // 检查频次
  planStartDate?: string // 计划开始时间
  planEndDate?: string // 计划结束时间
  planStartTime?: string // 任务开始时间
  planEndTime?: string // 任务结束时间
  beginTime?: string // 执行开始时间
  finishTime?: string // 执行结束时间
  superviseUnitName?: string // 分管单位
  unitNames?: string // 检查对象
  timeStateName?: string // 任务时效
  timeState?: 1 | 2 // 1 正常；2 逾期
  fzrs?: string // 专家组组长
  cyrs?: string // 专家组成员
  checkTable?: string // 检查表
  checkItemNum?: number // 检查表数量
  clockNum?: number // 打卡
  planName: string // 计划名称
  taskState?: 1 | 2 | 3
  checkAreaFormGrade?: number // 检查表层级
  checkTableId?: string // 检查表id
  checkExecuteMethod?: '1' | '2' // 2 需要逐一检查
  planCheckPointList?: any // 巡查点位列表
  taskStateName: string
  taskName: string
  checkDemand: string
  pointCheckType: number | string
}

// 隐患检查情况
export interface IChartsData {
  total: number
  myReport: number
  levelNum: ILevelNum[]
}
export interface ILevelNum {
  total: number
  hazardLevel: string
  hazardLevelName: string
}

// 隐患清单
export type IHazardPage = IPageRes<IHazardItem>
export interface IHazardItem {
  id: string
  disposeId: string
  hazardSource: number
  hazardSourceName: string
  unitId: string
  unitName: string
  randomCheckId: string
  planId: string
  eventSourceId: string
  essentialFactorClassItemId: string
  hazardDesc: string
  hazardPosition: string
  hazardType: string
  hazardTypeName: string
  hazardLevel: string
  remark: string
  timeoutDays: number
  disposeState: number
  disposeStateName: string
  eventTime: string
  createBy: string
  createByName: string
  createTime: string
  updateBy: string
  updateTime: string
  zhId: string
  buildingId: string
  floorId: string
  mapX: number | undefined
  mapY: number | undefined
  mapZ: number | undefined
  hazardLevelName: string
  subCenterCode: string
  correctionTime: string
  rectificationNum: string
  reviewState: number
  hazardTypeTopName: string
  flag: string
  hazardRandomCheckEventUsers?: ICheckEventUser[]
  hazardRandomCheckEventFiles?: ICheckEventFile[]
}

export interface ICheckEventUser {
  id: string
  randomCheckEventId: string
  reformUserId: string
  reformUserName: string
  reformType: number
  createTime: string
  updateTime: string
  zhId: string
  userType: string
  userUnitId: string
  userUnitName: string
  userMasterUnitId: string
  userMasterUnitName: string
}
export interface ICheckEventFile {
  id: string
  randomCheckEventId: string
  fileName: string
  saveName: string
  fileUrl: string
  createTime: string
}

export interface ICheckTable {
  checkAreaList: any[]
  [props: string]: any
}
