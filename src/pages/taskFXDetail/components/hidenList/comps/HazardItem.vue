<template>
  <view class="hazard-item">
    <view>
      <wd-img class="hazard-img" v-if="files.length" :src="getFileURL(files[0]?.fileUrl)" />
    </view>
    <view class="right">
      <view class="right_top" v-if="hazard.timeoutDays > 0">超期{{ hazard.timeoutDays }}天</view>
      <view :class="hazard.timeoutDays > 0 ? 'right_top2' : 'right_top'">
        {{ hazard.disposeStateName }}
      </view>

      <view class="hazard-name_wrap">
        <text class="level-tag">{{ hazard.hazardLevelName }}</text>
        <text class="hazard-name">{{ hazard.hazardDesc }}</text>
      </view>
      <view class="hazard-attr_item">
        <text>分类：</text>
        <text>
          {{ hazard.hazardTypeName }}
          <text v-if="hazard.hazardTypeTopName">({{ hazard.hazardTypeTopName || '' }})</text>
        </text>
      </view>
      <view class="hazard-attr_item">
        <text>点位位置：</text>
        <text>{{ hazard.hazardPosition }}</text>
      </view>
      <view
        class="hazard-attr_item"
        v-if="![1, 3, 14, 15, 16, 17, 18, 19, 20].includes(+hazard.hazardSource)"
      >
        <text>检查人：</text>
        <text>{{ hazard.createByName || '--' }}</text>
      </view>
      <view class="hazard-attr_item" v-if="+hazard.hazardSource !== 1">
        <text>上报时间：</text>
        <text>{{ hazard.createTime }}</text>
      </view>
      <view class="hazard-attr_item">
        <text>单位名称：</text>
        <text>{{ hazard.unitName }}</text>
      </view>
    </view>
  </view>
</template>
<script setup lang="ts">
import { IHazardItem } from '@/pages/taskFXDetail/type'
import { getFileURL } from '@/utils'

const props = defineProps<{ hazard: IHazardItem }>()

const files = computed(() => {
  return props.hazard.hazardRandomCheckEventFiles || []
})

defineOptions({ name: 'HiddenItem' })
</script>

<style scoped lang="scss">
.hazard-item {
  width: 100%;
  background: #ffffff;
  border-radius: 12rpx;
  padding: 50rpx 20rpx 24rpx;

  display: grid;
  grid-template-columns: auto minmax(0, 1fr);
  column-gap: 20rpx;
  margin-bottom: 20rpx;

  &:last-child {
    margin-bottom: 0;
  }

  .hazard-img {
    width: 180rpx;
    height: 330rpx;
    border-radius: 10px;
    margin-top: 18rpx;
  }

  .right {
    position: relative;

    .right_top {
      position: absolute;
      right: -20rpx;
      top: -50rpx;
      background: red;
      padding: 0 10rpx;
      border-radius: 0 12rpx 0 12rpx;
      color: white;
    }

    .right_top2 {
      position: absolute;
      right: 120rpx;
      top: -50rpx;
      background: #eb5b13;
      padding: 0 10rpx;
      border-radius: 0 12rpx 0 12rpx;
      color: white;
    }
  }

  .hazard-name_wrap {
    @apply flex items-center truncate;

    .hazard-name {
      @apply flex-1 font-bold text-[#000000] truncate;
      font-size: 32rpx;
      margin-right: 20rpx;
    }

    .level-tag {
      @apply w-[118rpx] text-center text-[#ffffff] bg-[#e23b50];
      padding: 4rpx 12rpx;
      border-radius: 20rpx;
      line-height: 28rpx;
      margin-right: 20rpx;
      font-size: 12px;
    }
  }

  .hazard-attr_item {
    @apply text-[#666666] truncate flex items-center;
    font-size: 28rpx;
    margin-top: 20rpx;

    text:last-child {
      @apply text-[#222] flex-1 truncate;
    }
  }
}
</style>
