<template>
  <wd-collapse v-model="openChart" accordion>
    <wd-collapse-item name="1">
      <template #title>
        <view class="flex items-center">
          <view class="title-tag"></view>
          <text class="font-bold text-[32rpx]">隐患检查情况</text>
        </view>
        <wd-icon
          :name="+openChart === 1 ? 'chevron-down' : 'chevron-right'"
          size="22px"
          color="#404040"
        ></wd-icon>
      </template>
      <view class="top-grid_box">
        <view>
          <view>
            <text>任务隐患总数</text>
            <text>{{ chartsData.total }}</text>
          </view>
          <view>
            <text>我上报的隐患</text>
            <text>{{ chartsData.myReport }}</text>
          </view>
        </view>
        <view>
          <view v-for="(item, index) in chartsData.levelNum" :key="index">
            <text>{{ item.hazardLevelName }}</text>
            <text>{{ item.total }}</text>
          </view>
        </view>
      </view>
    </wd-collapse-item>
  </wd-collapse>
</template>
<script setup lang="ts">
import { getTaskStatistics } from '@/pages/taskFXDetail/fetchData'
import { DetailSerivce } from '@/pages/taskFXDetail/detailService'
import { IChartsData } from '@/pages/taskFXDetail/type'

const openChart = ref('') // 折叠面板

const chartsData = ref<Partial<IChartsData>>({})
async function getStatistics(taskId: string) {
  const { code, data } = await getTaskStatistics({ taskId })
  if (code === 'success') {
    chartsData.value = data
    openChart.value = '1'
  }
}

onLoad((params) => {
  const { taskId } = params || DetailSerivce.detailInfo.value
  getStatistics(taskId)
})
defineOptions({ name: 'TopCharts' })
</script>

<style scoped lang="scss">
.title-tag {
  width: 8rpx;
  height: 28rpx;
  background: #597bf7;
  border-radius: 36rpx 36rpx 36rpx 36rpx;
  margin-right: 8rpx;
}

.top-grid_box {
  display: grid;
  grid-template-rows: repeat(2, 124rpx);
  row-gap: 20rpx;

  > view:first-child {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    column-gap: 20rpx;

    view {
      @apply flex flex-col items-center justify-center;
      border-radius: 16rpx;
      font-size: 24rpx;
      background: #dee9ff;
      color: #0256ff;

      text:last-child {
        @apply font-bold;
        font-size: 44rpx;
      }
    }
  }

  > view:nth-child(2) {
    display: flex;
    column-gap: 20rpx;

    view {
      @apply flex-1 flex flex-col items-center justify-center;
      border-radius: 16rpx;
      font-size: 24rpx;

      &:first-child {
        color: #e23b50;
        background: rgba(226, 59, 80, 0.1);
      }

      &:nth-child(2) {
        color: #f59a23;
        background: rgba(245, 154, 35, 0.1);
      }

      &:nth-child(3) {
        color: #2bb0dd;
        background: rgba(43, 176, 221, 0.1);
      }
      &:nth-child(4) {
        color: #9f9f00;
        background: rgba(191, 191, 0, 0.1);
      }

      text:last-child {
        @apply font-bold;
        font-size: 44rpx;
      }
    }
  }
}
:deep(.wd-collapse-item__body) {
  padding: 0 30rpx 10rpx !important;
}
</style>
