<template>
  <z-paging ref="pageRef" :fixed="false" v-model="listData" @query="getDataList">
    <template #top>
      <view class="page-top">
        <TopCharts />

        <wd-search
          placeholder-left
          @change="handleSearch"
          placeholder="搜索隐患位置/类型模糊搜索"
          :hide-cancel="true"
          v-model="query.keywords"
          @clear="clear"
        />
        <view class="select-menu_wrap">
          <wd-select-picker
            use-default-slot
            v-model="query.hazardLevel"
            :columns="levelList"
            label-key="gradeName"
            value-key="id"
            @change="handleSearch"
            :show-confirm="false"
            type="radio"
          >
            <view class="select-label">
              <view class="truncate mr-[10rpx]">{{ levelLabel() }}</view>
              <wd-icon v-if="!query.hazardLevel" name="caret-down-small" size="22px"></wd-icon>
              <wd-icon
                v-else
                name="close-circle-filled"
                size="16px"
                color="#999"
                @click.stop="clear('hazardLevel')"
              ></wd-icon>
            </view>
          </wd-select-picker>

          <wd-select-picker
            use-default-slot
            v-model="query.checkUnitId"
            :columns="checkUnitIdListAll"
            label-key="unitName"
            value-key="id"
            @change="handleSearch"
            :show-confirm="false"
            filterable
            type="radio"
          >
            <view class="select-label">
              <view class="truncate mr-[10rpx]">{{ unitLabel() }}</view>
              <wd-icon v-if="!query.checkUnitId" name="caret-down-small" size="22px"></wd-icon>
              <wd-icon
                v-else
                name="close-circle-filled"
                size="16px"
                color="#999"
                @click.stop="clear('checkUnitId')"
              ></wd-icon>
            </view>
          </wd-select-picker>
        </view>
      </view>
    </template>

    <view class="container">
      <HazardItem v-for="item in listData" :key="item.id" :hazard="item" />
    </view>
    <template #empty>
      <EmptyComp text="暂无数据" />
    </template>
  </z-paging>
</template>

<script lang="ts" setup>
import { debounce } from '@/utils'
import { useUserStore } from '@/store'
import TopCharts from '@/pages/taskFXDetail/components/hidenList/comps/TopCharts.vue'
import HazardItem from './comps/HazardItem.vue'
import { getHazardGrade, getUnits, pageEvent } from '@/pages/taskFXDetail/fetchData'
import { IHazardItem } from '@/pages/taskFXDetail/type'
import EmptyComp from '@/components/empty/index.vue'

const userStore = useUserStore()
const userInfo: any = userStore.userInfo ? userStore.userInfo : {}

const pageRef = ref()

// 等级列表
const levelList = ref<any[]>([])
// 全部单位列表
const checkUnitIdListAll = ref([])

// 查询条件
const query = ref({
  pageNo: 1,
  pageSize: 10,
  keywords: '',
  checkUnitId: '',
  hazardLevel: '',
  businessId: '',
  flag: '02',
})

const levelLabel = () => {
  const levelObj: any = levelList.value.find((item: any) => +item.id === +query.value.hazardLevel)
  return levelObj ? levelObj.gradeName : '等级'
}
const unitLabel = () => {
  const unitObj: any = checkUnitIdListAll.value.find(
    (item: any) => item.id === query.value.checkUnitId,
  )
  return unitObj ? unitObj.unitName : '单位名称'
}

const listData = ref<IHazardItem[]>([])
async function getDataList() {
  const { data, code } = await pageEvent(query.value)
  if (code === 'success') {
    pageRef.value.completeByTotal(data.rows, data.total)
  } else {
    listData.value = []
    pageRef.value.setTotal(0) // 设置总数为0
  }
}

//  组织机构获取单位列表
function getAllUnitAPI() {
  getUnits({ orgCode: userInfo.unitId, pageSize: -1 }).then((res: any) => {
    checkUnitIdListAll.value = res.data.rows
  })
}

async function hazardGrade() {
  // 调整获取隐患等级接口参数
  const { code, data } = await getHazardGrade({
    unitId: userInfo.orgRes === '1' ? userInfo.topUnitId : userInfo.serverUnitId,
    pageSize: -1,
    delFlag: 0,
  })
  if (code === 'success') {
    levelList.value = data as any[]
  }
}

// 清空输入框
function clear(key?: string) {
  if (!key) {
    query.value.keywords = ''
  } else {
    query.value[key] = ''
    handleSearch()
  }
}

// 输入框模糊查询
const handleSearch = debounce(() => {
  query.value.pageNo = 1
  listData.value = []
  pageRef.value.reload() // 刷新
}, 500)

onBeforeMount(() => {
  getAllUnitAPI()
  hazardGrade()
})

onLoad((params) => (query.value.businessId = params.taskId))

defineOptions({ name: 'TaskFXHidenList' })
</script>

<style scoped lang="scss">
.page-top {
  background: #ffffff;

  .select-menu_wrap {
    display: grid;
    grid-template-columns: repeat(2, minmax(0, 1fr));

    .select-label {
      @apply flex items-center justify-center;
      padding: 0 30rpx;
      height: 100rpx;
    }
  }
}
.container {
  @apply bg-[#F8FAFF];
  padding: 20rpx 30rpx;

  .hazard-list {
    @apply flex flex-col;
    row-gap: 20rpx;
  }
}

:deep(.wd-search__block) {
  height: 80rpx;
  line-height: 80rpx;
}
</style>
