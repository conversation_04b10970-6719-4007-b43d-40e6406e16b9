<template>
  <view class="base-info">
    <view class="task-name">检查任务：{{ info.taskName }}</view>
    <view class="task-tag">
      <wd-tag
        v-if="info.taskState"
        custom-class="tag-item"
        :bg-color="statusBg[info.taskState]"
        mark
      >
        {{ taskstatus[info.taskState] }}
      </wd-tag>
      <wd-tag
        v-if="info.timeState"
        custom-class="tag-item"
        :type="+info.timeState === 1 ? 'success' : 'danger'"
        plain
      >
        {{ info.timeStateName }}
      </wd-tag>
    </view>
    <view class="info-item">
      <text class="label">检查对象：</text>
      <text class="value">{{ info.unitNames || '--' }}</text>
    </view>
    <view class="info-item">
      <text class="label">检查负责人：</text>
      <text class="value">{{ info.fzrs || '--' }}</text>
    </view>
    <view class="info-item">
      <text class="label">检查参与人：</text>
      <view class="value value-cyr">
        <text class="cyr">{{ cyrText || '--' }}</text>
        <view v-if="showCyrOpen" class="value-more" @click="cyrOpen = !cyrOpen">
          {{ cyrOpen ? '收起' : '展开' }}
          <wd-icon
            :name="cyrOpen ? 'arrow-up' : 'arrow-down'"
            size="22px"
            color="#527cff"
          ></wd-icon>
        </view>
      </view>
    </view>
    <view class="info-item">
      <text class="label">任务起止时间：</text>
      <text class="value">{{ info.planStartTime || '--' }}~{{ info.planEndTime || '--' }}</text>
    </view>
    <view class="info-item" v-if="info.checkDemand">
      <text class="label">检查要求：</text>
      <text class="value">{{ info.checkDemand || '--' }}</text>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { DetailSerivce } from '../../detailService'

defineOptions({ name: 'TaskFXBaseInfo' })

enum taskstatus {
  待开始 = 1,
  进行中,
  已完成,
  已停用,
}

/*
 * pointCheckType
 * 1 - 每日风险点 2 - 每日风险点-自定义  99 - 其他（专项、全面、专业）
 */
const info = computed(() => DetailSerivce.detailInfo.value)

const statusBg = {
  1: '#E6A23C',
  2: '#527CFF',
  3: '#67C23A',
  4: '#F56C6C',
}

const showCyrOpen = computed(() => {
  return info.value?.cyrs?.split(',').length > 3
})
const cyrOpen = ref(false)
const cyrText = computed(() => {
  const cyr = info.value?.cyrs ? info.value.cyrs.split(',') : []

  return cyrOpen.value
    ? cyr.join('、')
    : cyr.length > 3
      ? cyr.slice(0, 3).join('、') + '...'
      : cyr.slice(0, 3).join('、')
})
</script>

<style scoped lang="scss">
.base-info {
  @apply w-full bg-[#fff];
  padding: 0 30rpx 20rpx 30rpx;

  .task-name {
    font-size: 32rpx;
    line-height: 1.5em;
    font-weight: 700;
    color: #000;
    margin-bottom: 20rpx;
  }

  .task-tag {
    @apply flex flex-row items-center justify-start;
    gap: 20rpx;
    margin-bottom: 20rpx;

    .tag-item {
      font-size: 28rpx;
      line-height: 1.4em;
    }
  }

  .info-item {
    @apply flex flex-row flex-nowrap items-start;
    gap: 20rpx;
    margin-bottom: 20rpx;
    font-size: 28rpx;
    color: #333;
    .label {
      white-space: nowrap;
      color: #7f7f7f;
    }
    .value: {
      flex: 1;
    }

    .value-cyr {
      @apply w-full flex flex-row flex-nowrap items-center justify-between;
      gap: 18rpx;
    }
    .value-more {
      @apply self-end flex flex-row flex-nowrap items-center;
      white-space: nowrap;
      font-size: 28rpx;
      color: #527cff;
    }
  }
}
</style>
