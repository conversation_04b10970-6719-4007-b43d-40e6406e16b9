<template>
  <z-paging
    ref="paging"
    :fixed="false"
    :auto="false"
    v-model="listData"
    loading-more-no-more-text="我也是有底线的！"
    @query="getDataList"
  >
    <BaseInfo />
    <CheckBar @action="actionHandle" />

    <view class="container">
      <view v-if="listData.length > 0">
        <CheckPointInfo
          v-for="(item, index) in listData"
          :key="index"
          :index="index + 1"
          :info="item"
          @action="actionHandle"
        />
      </view>
      <template v-else>
        <EmptyComp />
      </template>
    </view>
  </z-paging>

  <CheckTablePopup v-model:show="checkTableShow" />

  <PreViewImgs v-model:show="previewImgShow" :index="imgIndex" :imgs="previewImgs" />
</template>

<script lang="ts" setup>
import BaseInfo from './baseInfo.vue'
import CheckBar from './checkBar.vue'
import CheckPointInfo from './checkPointInfo.vue'
import EmptyComp from '@/components/empty/index.vue'
import CheckTablePopup from './checkTablePopup.vue'
import PreViewImgs from '@/components/previewImg/index.vue'
import { ACTION } from '../../constant'
import { DetailSerivce } from '../../detailService'
import { pageCheckPointList } from '../../fetchData'

defineOptions({ name: 'TaskFXCheckDetail' })

let routerParams: any = null

const paging = ref()
const listData = ref([])

const checkInState = ref('')

function getDataList(pageNo, pageSize) {
  console.log(pageNo, pageSize)

  if (pageNo === 1) {
    listData.value = []
  }
  uni.showLoading({
    mask: true,
  })
  pageCheckPointList({
    taskId: routerParams.taskId,
    checkInState: checkInState.value,
    pageNo,
    pageSize,
  })
    .then(({ code, data }) => {
      if (code === 'success' && data.rows) {
        paging.value.completeByTotal(data.rows, data.total)
      }
    })
    .catch(() => {
      paging.value.complete(false)
    })
    .finally(() => {
      uni.hideLoading()
    })

  // setTimeout(() => {
  //   console.log('>>>>', listData.value)

  //   for (let i = 0; i < pageSize; i++) {
  //     const id = (pageNo - 1) * pageSize + i
  //     console.log(id)
  //     listData.value.push({
  //       checkInState: i % 2,
  //       checkInStateName: '已排查',
  //       hazardNum: 12,
  //       pointFullName: '压缩机房',
  //       location: '8865集气站A生活区北',
  //       checkInTime: '2025-06-13',
  //       checkTableId: '123434',
  //       checkTableName: '压缩机房检查表',
  //       photos: [
  //         'https://registry.npmmirror.com/wot-design-uni-assets/*/files/redpanda.jpg',
  //         'https://registry.npmmirror.com/wot-design-uni-assets/*/files/capybara.jpg',
  //         'https://registry.npmmirror.com/wot-design-uni-assets/*/files/panda.jpg',
  //         'https://registry.npmmirror.com/wot-design-uni-assets/*/files/meng.jpg',
  //       ],
  //     })
  //   }
  //   paging.value.completeByTotal(listData.value, 15)
  // }, 600)
}

// checkTable
const checkTableShow = ref(false)

// preview-img
const previewImgShow = ref(false)
const previewImgs = ref([])
const imgIndex = ref(0)

function actionHandle(res) {
  const { action, data } = res
  console.log(action, data)

  if (action === ACTION.CHECKSTATE) {
    checkInState.value = data.checkInState === '2' ? '' : data.checkInState
    paging.value.reload()
  }
  if (action === ACTION.IMGPREVIEW) {
    previewImgs.value = data.imgs
    imgIndex.value = data.index
    previewImgShow.value = true
  }
  if (action === ACTION.CHECKTABLE) {
    checkTableShow.value = true
  }
}

onLoad((params) => {
  routerParams = params
})
onMounted(() => {
  paging.value.reload()
})
</script>

<style scoped lang="scss">
.container {
  padding: 0 30rpx;
}

// .list-item {
//   width: 100%;
//   background-color: #fff;
//   margin-bottom: 20rpx;
//   font-size: 28rpx;
//   color: #333;
//   padding: 16rpx;
// }
</style>
