<template>
  <view class="check-bar">
    <view
      v-for="item in barList"
      :key="item.id"
      :class="[
        'bar-item',
        `bar-item_${item.id}`,
        curId === item.id ? `bar-item_${item.id}_actived` : '',
      ]"
      @click="barHandle(item.id)"
    >
      <text class="bar-name">{{ item.name }}</text>
      <text class="bar-value">{{ item.value }}</text>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { checkPointBars } from '../../fetchData'
import { ACTION } from '../../constant'

defineOptions({ name: 'TaskFXCheckBar' })

const emits = defineEmits(['action'])

const curId = ref('2')
const barList = ref([
  {
    id: '2',
    name: '排查点位数',
    value: 0,
  },
  {
    id: '1',
    name: '已排查检查点',
    value: 0,
  },
  {
    id: '0',
    name: '待开始检查点',
    value: 0,
  },
])

const barHandle = (id: string) => {
  curId.value = id
  emits('action', {
    action: ACTION.CHECKSTATE,
    data: {
      checkInState: id,
    },
  })
}

function getData(taskId: string) {
  checkPointBars(taskId).then((res: any) => {
    const { code, data } = res
    if (code === 'success') {
      barList.value[0].value = data.deviceTotalNum || 0
      barList.value[1].value = data.checkedNum || 0
      barList.value[2].value = data.uncheckedNum || 0
    }
  })
}

onLoad(async (params) => {
  const { taskId } = params
  getData(taskId)
})
</script>

<style scoped lang="scss">
.check-bar {
  @apply flex flex-row flex-nowrap items-center justify-between;
  gap: 16rpx;
  width: 100%;
  padding: 20rpx;
  margin: 20rpx 0 30rpx 0;
  background: #fff;
}
.bar-item {
  @apply flex flex-col items-center;
  padding: 16rpx 40rpx;
  border-radius: 16rpx;
  font-size: 24rpx;
  line-height: 36rpx;
  .bar-name {
    white-space: nowrap;
  }
  .bar-value {
    font-weight: 700;
    font-size: 44rpx;
    line-height: 48rpx;
  }
}
.bar-item_2 {
  background: #dee9ff;
  color: #0256ff;
}
.bar-item_2_actived {
  background: #0256ff;
  color: #fff;
}
.bar-item_1 {
  background: #dfffe9;
  color: #00b277;
}
.bar-item_1_actived {
  background: #00b277;
  color: #fff;
}
.bar-item_0 {
  background: #fff1ec;
  color: #ff9757;
}
.bar-item_0_actived {
  background: #ff9757;
  color: #fff;
}
</style>
