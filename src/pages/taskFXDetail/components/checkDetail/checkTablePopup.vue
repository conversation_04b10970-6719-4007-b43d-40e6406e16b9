<template>
  <view class="check-table-box">
    <wd-popup
      v-model="popupShow"
      position="bottom"
      custom-style="height: 85vh;border-radius: 32rpx;"
      @close="handleClose"
    >
      <view class="check-table">
        <view class="popup-title">
          <text>检查表详情</text>
          <wd-icon
            class="popup-close"
            name="close"
            size="32rpx"
            color="#999"
            @click="handleClose"
          ></wd-icon>
        </view>
        <view class="list">
          <wd-collapse ref="collapseRef" v-model="collapseValue">
            <wd-collapse-item v-for="item in listData" :key="item.id" :name="item.id">
              <template #title="{ expanded }">
                <view class="collapse-item-header">
                  <view class="header-title">
                    <wd-img class="header-title-icon" :src="dangerIcon"></wd-img>
                    <text>{{ item.title }}</text>
                  </view>
                  <wd-icon
                    :name="expanded ? 'arrow-up' : 'arrow-down'"
                    size="32rpx"
                    color="#999"
                  ></wd-icon>
                </view>
              </template>
              <view class="check-item" v-for="(checkItem, index) in item.list" :key="index">
                {{ checkItem.content }}
              </view>
            </wd-collapse-item>
          </wd-collapse>
        </view>
      </view>
    </wd-popup>
  </view>
</template>

<script lang="ts" setup>
import dangerIcon from '../../assets/dangerIcon.png'

defineOptions({ name: 'CheckTablePopup' })

interface IProps {
  show: boolean
}
const props = withDefaults(defineProps<IProps>(), {
  show: false,
})

const emits = defineEmits(['update:show'])
const popupShow = computed({
  get: () => {
    return props.show
  },
  set: (v) => {
    emits('update:show', v)
  },
})

const handleClose = () => {
  popupShow.value = false
}

const collapseRef = ref()
const collapseValue = ref([])
const listData = ref([
  {
    title: '危险源1：XXXXX',
    id: '1-1',
    list: [
      {
        content: '检查事项1：防雷接地、检测仪、报警器、安全附件设备基础牢固，护栏可靠实用',
      },
      {
        content: '检查事项2：防雷接地、检测仪、报警器、安全附件设备基础牢固，护栏可靠实用',
      },
    ],
  },
  {
    title: '危险源2：XXXXX',
    id: '1-2',
    list: [
      {
        content: '检查事项1：防雷接地、检测仪、报警器、安全附件设备基础牢固，护栏可靠实用',
      },
      {
        content: '检查事项2：防雷接地、检测仪、报警器、安全附件设备基础牢固，护栏可靠实用',
      },
    ],
  },
  {
    title: '危险源3：XXXXX',
    id: '1-3',
    list: [
      {
        content: '检查事项1：防雷接地、检测仪、报警器、安全附件设备基础牢固，护栏可靠实用',
      },
    ],
  },
])
</script>

<style scoped lang="scss">
.check-table-box {
  position: fixed;
  z-index: 9999;
}
.check-table {
  width: 100%;
  height: 100%;

  .popup-title {
    @apply relative w-full flex items-center justify-center;
    height: 110rpx;
    font-size: 32rpx;
    line-height: 2em;
    font-weight: 700;
    border: 1px solid #eee;
  }
  .popup-close {
    position: absolute;
    top: 50%;
    right: 20rpx;
    transform: translateY(-50%);
  }

  .collapse-item-header {
    @apply w-full flex flex-row flex-nowrap items-center justify-between;

    .header-title {
      @apply flex flex-row flex-nowrap items-center;
      gap: 20rpx;
      font-size: 32rpx;
      line-height: 1.5em;
      color: #333;
      font-weight: 700;
    }
    .header-title-icon {
      width: 32rpx;
      height: 32rpx;
    }
  }

  .check-item {
    font-size: 28rpx;
    line-height: 1.5em;
    color: #333;
    background: #f7f7f7;
    border-radius: 12rpx;
    padding: 12rpx;
    margin-bottom: 16rpx;
    &:last-of-type {
      margin-bottom: 0;
    }
  }
}
</style>
