<template>
  <view class="check-table-box">
    <wd-popup
      v-model="popupShow"
      position="bottom"
      custom-style="height: 85vh;border-radius: 32rpx;"
      @close="handleClose"
    >
      <view class="check-table">
        <view class="popup-title">
          <text>检查表详情</text>
          <wd-icon
            class="popup-close"
            name="close"
            size="32rpx"
            color="#999"
            @click="handleClose"
          ></wd-icon>
        </view>
        <view class="list">
          <wd-collapse ref="collapseRef" v-model="collapseValue">
            <wd-collapse-item v-for="(item, index) in listData" :key="item.id" :name="item.id">
              <template #title="{ expanded }">
                <view class="collapse-item-header">
                  <view class="header-title">
                    <wd-img class="header-title-icon" :src="dangerIcon"></wd-img>
                    <text>{{ `检查类别${index + 1}: ${item.checkContent}` }}</text>
                  </view>
                  <wd-icon
                    :name="expanded ? 'arrow-up' : 'arrow-down'"
                    size="32rpx"
                    color="#999"
                  ></wd-icon>
                </view>
              </template>
              <view
                class="check-item"
                v-for="(checkItem, cIndex) in item.childCheckAreaList"
                :key="checkItem.id"
              >
                {{ `检查内容${cIndex + 1}: ${checkItem.checkDetail}` }}
              </view>
            </wd-collapse-item>
          </wd-collapse>
        </view>
      </view>
    </wd-popup>
  </view>
</template>

<script lang="ts" setup>
import dangerIcon from '../../assets/dangerIcon.png'
import { getCheckTableDetail } from '../../fetchData'

defineOptions({ name: 'CheckTablePopup' })

interface IProps {
  show: boolean
  id: string
}
const props = withDefaults(defineProps<IProps>(), {
  show: false,
  id: '',
})

const emits = defineEmits(['update:show'])
const popupShow = computed({
  get: () => {
    return props.show
  },
  set: (v) => {
    emits('update:show', v)
  },
})

const handleClose = () => {
  popupShow.value = false
}

const collapseRef = ref()
const collapseValue = ref([])
const listData = ref([])

function getData() {
  getCheckTableDetail(props.id).then(({ code, data }) => {
    if (code === 'success' && data.checkAreaList) {
      collapseValue.value = data.checkAreaList.map((item) => item.id)
      listData.value = data.checkAreaList
    }
  })
}

watchEffect(() => {
  if (props.id) getData()
})
</script>

<style scoped lang="scss">
.check-table-box {
  position: fixed;
  z-index: 9999;
}
.check-table {
  width: 100%;
  height: 100%;

  .popup-title {
    @apply relative w-full flex items-center justify-center;
    height: 110rpx;
    font-size: 32rpx;
    line-height: 2em;
    font-weight: 700;
    border: 1px solid #eee;
  }
  .popup-close {
    position: absolute;
    top: 50%;
    right: 20rpx;
    transform: translateY(-50%);
  }

  .collapse-item-header {
    @apply w-full flex flex-row flex-nowrap items-center justify-between;

    .header-title {
      @apply flex flex-row flex-nowrap items-center;
      gap: 20rpx;
      font-size: 32rpx;
      line-height: 1.5em;
      color: #333;
      font-weight: 700;
    }
    .header-title-icon {
      width: 32rpx;
      height: 32rpx;
    }
  }

  .check-item {
    font-size: 28rpx;
    line-height: 1.5em;
    color: #333;
    background: #f7f7f7;
    border-radius: 12rpx;
    padding: 12rpx;
    margin-bottom: 16rpx;
    &:last-of-type {
      margin-bottom: 0;
    }
  }
}
</style>
