<template>
  <view class="check-point">
    <view class="point-index">
      <text class="index">{{ indexNum }}</text>
      <text>检查点</text>
    </view>
    <view class="point-info-main">
      <view class="point-status-box">
        <text :class="['check-status', `check-status_${info.checkInState}`]">
          {{ info.checkInStateName }}
        </text>
        <text class="hiden-num">隐患数：{{ info.hazardNum || 0 }}</text>
      </view>

      <view class="point-info-item">
        <view class="label">点位名称：</view>
        <view class="value">{{ info.pointFullName || '--' }}</view>
      </view>
      <view class="point-info-item">
        <view class="label">点位位置：</view>
        <view class="value">{{ location || '--' }}</view>
      </view>
      <view class="point-info-item">
        <view class="label">巡检时间：</view>
        <view class="value">{{ info.checkInTime || '--' }}</view>
      </view>
      <view class="point-info-item">
        <view class="label">检查人：</view>
        <view class="value">{{ info.checkUserName || '--' }}</view>
      </view>
      <view class="point-info-item">
        <view class="label">检查表：</view>
        <view class="value value-link" @click="checkTableHadnle">
          {{ info.checkTableName || '--' }}
        </view>
      </view>
      <view :class="['point-info-item', imgList?.length > 0 ? 'point-info-item_col' : '']">
        <view class="label">检查过程照片：</view>
        <view v-if="imgList?.length > 0" class="item-value_imgs">
          <wd-img
            class="img-item"
            v-for="(img, index) in imgList"
            :key="img"
            :width="100"
            :height="100"
            :src="img"
            @click="previewHandle(index)"
          />
        </view>
        <view v-else class="value">暂无照片</view>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { ACTION } from '../../constant'
import { IPointItem } from '../../type'
import { getFileURL } from '@/utils/index'
import { DetailSerivce } from '../../detailService'

defineOptions({ name: 'TaskFXCheckPoint' })

interface IProps {
  info: Partial<IPointItem> | null
}
const props = withDefaults(defineProps<IProps>(), {
  info: null,
})
const indexNum = computed(() => {
  return props.info.indexNo < 10 ? '0' + props.info.indexNo : props.info.indexNo
})
const location = computed(() => {
  return +DetailSerivce.detailInfo.value?.pointCheckType === 2
    ? `${props.info.building}${props.info.floor}${props.info.location}`
    : props.info.location
})

const imgList = computed(() => {
  let _list: string[] = []
  if (props.info.photos) {
    _list = props.info.photos.split(',').map((item) => getFileURL(item))
  }
  return _list
})

const emits = defineEmits(['action'])

function checkTableHadnle() {
  if (!props.info.checkTableId) return
  emits('action', {
    action: ACTION.CHECKTABLE,
    data: {
      id: props.info.checkTableId,
    },
  })
}
function previewHandle(index: number) {
  emits('action', {
    action: ACTION.IMGPREVIEW,
    data: {
      imgs: toRaw(imgList.value),
      index,
    },
  })
}
</script>

<style scoped lang="scss">
.check-point {
  @apply bg-[#fff] flex flex-row;
  width: 100%;
  padding: 20rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 0 16rpx 0 rgba(0, 0, 0, 0.1);
  border-radius: 12rpx;

  .point-index {
    @apply relative text-[#fff] flex flex-col items-center justify-center;
    width: 100rpx;
    height: 100rpx;
    font-size: 24rpx;
    line-height: 1.25em;
    z-index: 0;
    &::before {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      z-index: -1;
      width: 120rpx;
      height: 120rpx;
      background: url('@/pages/taskFXDetail/assets/checkPoint_bg.png') 0 0 no-repeat;
      background-size: 100% 100%;
    }
    .index {
      font-size: 40rpx;
    }
  }

  .point-info-main {
    min-width: 0;
    flex: 1;
    padding-left: 16rpx;
    .point-status-box {
      @apply flex flex-row;
      margin: 20rpx 0;
      gap: 20rpx;

      .check-status {
        font-size: 24rpx;
        padding: 8rpx 16rpx;
        line-height: 1.5em;
        border-radius: 12rpx;
        &.check-status_0 {
          color: #a5a5a5;
          background: #f3f3f3;
        }
        &.check-status_1 {
          color: #00ad74;
          background: #dfffe9;
        }
      }

      .hiden-num {
        font-size: 24rpx;
        padding: 8rpx 16rpx;
        border-radius: 12rpx;
        color: #ec770f;
        background: #fff3df;
      }
    }

    .point-info-item {
      @apply text-[#333] flex flex-row items-start;
      font-size: 28rpx;
      line-height: 2em;
      &.point-info-item_col {
        @apply flex-col;
        gap: 20rpx;
      }

      .label {
        color: #666;
      }

      .value {
        flex: 1;
      }
      .value-link {
        @apply text-[#527CFF] cursor-pointer;
      }
    }

    .item-value_imgs {
      @apply flex flex-row flex-nowrap items-center flex-1 w-full;
      gap: 20rpx;
      overflow-x: scroll;

      .img-item {
        @apply bg-[#fff] flex-none flex items-center justify-center;
        width: 160rpx;
        height: 160rpx;
        border: 1px solid #eee;

        &:nth-of-type(n + 4) {
          display: none;
        }
      }
      //   :deep(.n-image img) {
      //     height: 100%;
      //   }
    }
  }
}
</style>
