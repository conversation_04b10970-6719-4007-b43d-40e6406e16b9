<route lang="json5">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '任务详情',
  },
}
</route>

<template>
  <view class="taskFx-detail-page">
    <SafetyNavbar title="任务详情"></SafetyNavbar>
    <CustomTabs
      class="w-full"
      :tabs="tabs"
      :activeIndex="activeIndex"
      @handleClick="handleChange"
    ></CustomTabs>
    <view class="page-main">
      <keep-alive>
        <component :is="curComp" />
      </keep-alive>
    </view>
    <view class="page-bottom" v-if="showBottom"></view>
  </view>
</template>

<script lang="ts" setup>
import SafetyNavbar from '../components/safety-navbar.vue'
import CustomTabs from '../components/custom-Tabs.vue'
import CheckDetail from './components/checkDetail/index.vue'
import HidenList from './components/hidenList/index.vue'

import { DetailSerivce } from './detailService'
import { getTaskDetail } from './fetchData'

import { useUserStore } from '@/store'

defineOptions({ name: 'TaskFXDetail' })

const userStore = useUserStore()
const userInfo: any = userStore.userInfo ? userStore.userInfo : {}

const showBottom = computed(() => {
  return false
  // tabindex === '0' &&
  // info.taskState !== '3' &&
  // ((info.checkEndOperate === '2' && arrayContainsValue(fzrlist, userInfo.id)) ||
  //   arrayContainsValue(cyrsIds, userInfo.id))
})

const tabs = ['检查详情', '隐患清单']
const activeIndex = ref(0)

const curComp = computed(() => {
  return activeIndex.value === 0 ? markRaw(CheckDetail) : markRaw(HidenList)
})

function handleChange(value) {
  activeIndex.value = value
}

onLoad(async (params) => {
  const { taskId } = params
  try {
    const { code, data } = await getTaskDetail(taskId)
    if (code === 'success') {
      DetailSerivce.detailInfo.value = data
    }
  } catch (error) {}
})
</script>

<style scoped lang="scss">
.taskFx-detail-page {
  @apply w-full h-full flex flex-col items-start gap-0;

  .page-main {
    @apply w-full min-h-0 flex-1 overflow-hidden;
    background: #f9f9f9;
  }

  .page-bottom {
    @apply w-full bg-yellow-400;
    height: 100rpx;
  }
}
</style>
