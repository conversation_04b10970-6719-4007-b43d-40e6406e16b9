<!-- <route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: 'From表单',
  },
}
</route> -->
<template>
  <view>
    <div v-for="(form, index) in modelArray" :key="index">
      <view class="form-title" style="">
        <view class="form-title-icon" v-if="index !== 0"></view>
        <!-- <view style="margin-left: 4pt" v-if="index === 0">隐患上报</view> -->
        <view style="
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: space-between;
            width: 93%;
          ">
          <view style="margin-left: 4pt" v-if="index !== 0">隐患位置{{ index }}</view>
          <view v-if="index !== 0">
            <wd-button size="small" plain @click="delmodelArraybyindex(index)">删除</wd-button>
          </view>
        </view>
      </view>
      <wd-cell-group class="group" title="" border>
        <wd-form ref="form" :model="form">
          <!-- 从隐患库中选择 -->
          <commonHazardDescribe @click="getindex(index)" v-if="model.pagetype === 0" :unitId="topUnitId">
          </commonHazardDescribe>
          <view class="com-cell" v-if="model.pagetype === 0">
            <wd-textarea type="textarea" label-width="100px" :maxlength="200" show-word-limit prop="remark" clearable
              auto-height v-model="form.hazardDesc" placeholder="请输入隐患描述" />
          </view>
          <wd-textarea label="隐患描述" v-if="model.pagetype === 1" type="textarea" label-width="100px" prop="hazardDesc"
            clearable required :maxlength="200" show-word-limit v-model="form.hazardDesc" placeholder="请输入隐患描述" />

          <!-- ======隐患分类===根据pagetype类型 显示下拉框（0）或输入框（其它）  :placeholder="FactorClassFullName !== '' ? FactorClassFullName : '请输入隐患分类'"
            :class="FactorClassFullName !== '' ? 'FactorClassFullNamecalss' : ''==== -->
          <wd-select-picker z-index="999" :readonly="idreadonly" label="隐患分类" v-model="form.hazardType"
            :columns="EssentialFactor" align-right type="radio" @confirm="handleConfirm" value-key="id"
            label-key="className" required @click="getindex(index)"></wd-select-picker>
          <!-- < wd-col-picker v-model=" deviceTypeArr" label="隐患分类"
            :placeholder="FactorClassFullName !== '' ? FactorClassFullName : '请输入隐患分类'" :columns="EssentialFactor"
            :column-change="columnChange" :display-format="displayFormat" @confirm="handleConfirm" align-right
            auto-complete required @click="getindex(index)" value-key="id" label-key="className"
            :class="FactorClassFullName !== '' ? 'FactorClassFullNamecalss' : ''"></wd-col-picker> -->
          <!-- 隐患分类结束 -->
          <!-- 隐患等级 -->
          <!-- <commonGrade ref="graderef" @click="getindex(index)" @getGrade="getGrade" :type="1" :getUnitId="form.unitId"
            :selectvalue="form.hazardLevel"></commonGrade> -->
          <wd-select-picker z-index="999" :readonly="idreadonly" label="隐患等级" placeholder="请选择隐患等级"
            v-model="form.hazardLevel" :columns="gradeList" value-key="id" label-key="gradeName" align-right
            type="radio" required @confirm="handleConfirm1"></wd-select-picker>
          <!-- 隐患单位 -->
          <commonUnit v-if="hiddenType !== '0'" @click="getindex(index)" @getGrade="getUnit" :types="types"
            :selectvalue="form.unitId"></commonUnit>
          <wd-cell title="楼栋楼层" class="text" :value="form.morehazardPlace" is-link @click="handleposition(index)"
            clickable required />
          <wd-input label="隐患位置" label-width="100px" prop="hazardDesc" :maxlength="50" required show-word-limit
            v-model="form.hazardPosition" placeholder="点击图标选择具体位置" use-prefix-slot>
            <template #suffix>
              <wd-icon name="location" @click="SelectXY(index)" color="#4d80f0" size="25px">
                选点
              </wd-icon>
            </template>
          </wd-input>
          <view style="padding: 1rem">
            <view class="required">隐患图片</view>
            <!-- <upload-img :fileList="blFileList" @upAllImage="upAllImage" /> -->
            <uploadButton @click="getindex(index)" ref="uploadButtonRef" :imginfo="form.files"
              @getFileObjList="getFilelist" @deleteImg="deleteImg"></uploadButton>
          </view>
          <commonSelectpicker :title="'隐患整改人员'" :orgUnitId="userinfo.unitId" :status="1" @send="reception"
            @click="getindex(index)"></commonSelectpicker>
          <wd-textarea label="备注" type="textarea" label-width="100px" :maxlength="500" show-word-limit prop="remark"
            clearable v-model="form.remark" placeholder="此处填写隐患相关备注的内容" />
        </wd-form>
      </wd-cell-group>
    </div>
    <view class="add-hazard" @click="addfrom">新增隐患</view>
  </view>
  <info1 v-if="pagetype === 1" :info1Itme="info"></info1>

</template>

<script lang="ts" setup>
import info1 from '../../task/components/info1.vue'
import commonSelectpicker from '@/components/commonSelect/common-selectpicker-new.vue'
import commonEssentialFactor from '@/components/commonSelect/common-EssentialFactor.vue'
import commonHazardDescribe from '@/components/commonSelect/common-hazardDescribe.vue'
import commonGrade from '@/components/commonSelect/common-Grade.vue'
import commonUnit from '@/components/commonSelect/common-Unit.vue'
import uploadButton from '@/components/upload/upload-button.vue'
import { useColPickerData } from '@/hooks/useColPickerData'
import UploadImg from '@/components/uploadImg/uploadImg.vue'
import { addForms } from '../type'
import { ref } from 'vue'
import {
  inspectDetail,
  posthazardEssentialFactorClassAPI,
  posthazardPlanTaskEventaddAPI,
  posthazardRandomCheckDetailAPI,
  posthazardRecordAddEventAPI,
  postTopLevelOrgCodeAPI,
} from '../featch'
import { useUserStore } from '@/store'
import { debounce } from '@/utils'
import { taskDetail } from '@/pages/task/featch'
import { posthazardGradeAPI } from '@/components/commonSelect/featch'
const idreadonly = ref(true)
const loading = ref(false)
const userStore = useUserStore()
// console.log(Object.keys(userStore.userInfo).length, '======userinfo=======')
const userinfo: any =
  userStore.userInfo && Object.keys(userStore.userInfo).length > 5
    ? userStore.userInfo
    : uni.getStorageSync('userInfoObj')
// console.log(userinfo, '======userinfo3333=======')
const info = ref<any>({})
const pagetype = ref(0) // 页面输入框显示判断
const checkId = ref<any>('')
const taskId = ref<any>('')
const apitype = ref<any>('') // 请求接口判断
const hiddenType = ref<any>('') // 判断是否从随机检查入口进入
const getplanId = ref<any>('')
const classItemId = ref<any>('') // 隐患检查项ID
const modelArray = ref<addForms<any>[]>([])
const uploadButtonRef = ref()

const topUnitId = ref<any>('') // 从详情获取topUnitId，用于查询隐患库

const FactorClassFullName = ref('') // 从隐患库中选择的分类
const model = ref<addForms<any>>({
  pagetype: pagetype.value,
  typePid: '',
  buildingId: '',
  createBy: userinfo.id,
  createTime: '',
  disposeId: '',
  disposeState: '0',
  disposeStateName: '待整改',
  endTime: '',
  essentialFactorClassItemId: '', // 检查项ID----从隐患库中选择--检查表id
  checkAreaEssentialFactorId: '', // 检查项--检查表id---非快捷，不从隐患库选--从任务中获取检查表id
  eventTime: '',
  files: [],
  floorId: '',
  hazardDesc: '',
  hazardLevel: '',
  hazardLevelName: '',
  hazardPosition: '',
  hazardSource: 2, // 检查id为空 类型为人工上报
  hazardSourceName: '',
  hazardType: null,
  hazardTypeName: '',
  latitude: 0,
  longitude: 0,
  mapX: 0,
  mapY: 0,
  mapZ: 0,
  randomCheckId: checkId.value,
  reformUserJson: [],
  remark: '',
  search: '',
  startTime: '',
  superviseUnitId: userinfo.deptId,
  superviseUnitName: userinfo.deptName,
  unitId: '',
  unitName: '',
  updateBy: '',
  zhId: userinfo.zhId,
  timeoutDays: 0,
  id: '',
  morehazardPlace: '',
  planId: '',
  createByName: userinfo.userName,
  taskId: '',
  hazardRandomCheckEventUsers: [],
})
const syscode = ref('')
const types = ref<string>('')
const disabled = ref(true)
const orgUnitId = ref('')
onLoad((params) => {
  hiddenType.value = params.hiddenType
  types.value = params.types

  if (Object.keys(params).length !== 0) {
    // checkAreaEssentialFactorId====params.checkContentId  点击上报带过来 默认等于 检查项id
    classItemId.value = params.checkContentId
    taskId.value = params.taskId
    checkId.value = params.checkId
    //
    if (params.unitId) {
      model.value.unitId = params.unitId
      getGradeAndLeve(params.unitId)
    }
    if (types.value === '1') {
      model.value.hazardSource = 2
      // modelArray.value.push(model.value)
    } else {
      taskDetail(taskId.value).then((res: any) => {
        if (res.code === 'success') {
          topUnitId.value = res.data.topUnitId
        }
      })
    }

    // 获取隐患上报来源
    if (params.hazardSource) {
      model.value.hazardSource = params.hazardSource
    }
    if (params.syscode) {
      syscode.value = params.syscode
    }

    // getplanId.value = taskId.value ? uni.getStorageSync('planId') : '' // 当任务ID有值时取计划ID 否者为空
    getplanId.value = uni.getStorageSync('planId') ? uni.getStorageSync('planId') : ''
    if (params.planId) {
      getplanId.value = params.planId
    }
    model.value.checkAreaEssentialFactorId = classItemId.value // 不从隐患库选，非快捷上报获取任务中的检查表id
    model.value.taskId = taskId.value
    model.value.randomCheckId = checkId.value
    model.value.planId = getplanId.value
    const unitList = uni.getStorageSync('unitList') ? uni.getStorageSync('unitList') : []
    if (unitList.length > 0) {
      model.value.unitId = unitList[0].id
      model.value.unitName = unitList[0].unitName
      getGradeAndLeve(unitList[0].id)
    }
    if (params.FactorId) {
      pagetype.value = 1
      const paramsinfo = { FactorId: params.FactorId, id: params.checkId }
      getcheckinfo(paramsinfo)
      disabled.value = false
    } else {
      pagetype.value = 0
      modelArray.value.push(model.value)
    }
  } else {
    apitype.value = '1'
    model.value.hazardSource = 2
    modelArray.value.push(model.value)
  }

  if (taskId.value) {
    orgUnitId.value = uni.getStorageSync('unitList').data[0].id
  } else {
    orgUnitId.value = userinfo.unitId
  }
})

const descmodel = ref<any>({})
function getcheckinfo(params) {
  // console.log('getcheckinfo===============')
  inspectDetail(params)
    .then((res: any) => {
      info.value = res.data
      if (res.data.essentialFactorList.length > 0) {
        /* 隐患等级 */
        // essentialFactorClassId
        descmodel.value.defaulhazardLevel = res.data.essentialFactorList[0].essentialFactorGradeId
        descmodel.value.defaulhazardLevelName = res.data.essentialFactorList[0].essentialFactorGrade
        /* 隐患分类 */
        descmodel.value.defaulhazardTypeId = res.data.essentialFactorList[0].essentialFactorClassId
        descmodel.value.defaulhazardTypeName = res.data.essentialFactorList[0].essentialFactorClass

        FactorClassFullName.value = res.data.essentialFactorList[0].essentialFactorClass
        // 隐患描述
        descmodel.value.defauldesc = res.data.essentialFactorList[0].essentialFactorDescribe
        // 检查项id
        // checkAreaEssentialFactorId
        descmodel.value.essentialFactorClassItemId = res.data.essentialFactorList[0].checkAreaId
        // essentialFactorClassItemId
      }
      // console.log(descmodel.value, '==========descmodel.value')
      model.value = {
        pagetype: pagetype.value,
        typePid: '',
        buildingId: '',
        createBy: userinfo.id,
        createTime: '',
        disposeId: '',
        disposeState: '0',
        disposeStateName: '待整改',
        endTime: '',
        essentialFactorClassItemId: descmodel.value.essentialFactorClassItemId,
        checkAreaEssentialFactorId: classItemId.value,
        eventTime: '',
        files: [],
        floorId: '',
        hazardDesc: descmodel.value.defauldesc,
        hazardLevel: descmodel.value.defaulhazardLevel,
        hazardLevelName: descmodel.value.defaulhazardLevelName,
        hazardPosition: '',
        hazardSource: 2, // 检查id为空 类型为人工上报
        hazardSourceName: '',
        hazardType: descmodel.value.defaulhazardTypeId,
        hazardTypeName: descmodel.value.defaulhazardTypeName,
        latitude: 0,
        longitude: 0,
        mapX: 0,
        mapY: 0,
        mapZ: 0,
        randomCheckId: checkId.value,
        reformUserJson: [],
        remark: '',
        search: '',
        startTime: '',
        superviseUnitId: userinfo.deptId,
        superviseUnitName: userinfo.deptName,
        unitId: '',
        unitName: '',
        updateBy: '',
        zhId: userinfo.zhId,
        timeoutDays: 0,
        id: '',
        morehazardPlace: '',
        planId: getplanId.value,
        createByName: userinfo.userName,
        taskId: taskId.value,
        hazardRandomCheckEventUsers: [],
      }
      // // console.log(model.value, '========================model.value')
    })
    .finally(() => {
      modelArray.value.push(model.value)
    })
}
// 新增隐患
function addfrom() {
  modelArray.value.push({
    pagetype: model.value.pagetype,
    typePid: modelArray.value[0].typePid,
    buildingId: '',
    createBy: userinfo.id,
    createTime: '',
    disposeId: '',
    disposeState: '0',
    disposeStateName: '待整改',
    endTime: '',
    essentialFactorClassItemId: modelArray.value[0].essentialFactorClassItemId,
    checkAreaEssentialFactorId: modelArray.value[0].checkAreaEssentialFactorId,
    eventTime: '',
    files: [],
    floorId: '',
    hazardDesc: modelArray.value[0].hazardDesc,
    hazardLevel: modelArray.value[0].hazardLevel,
    hazardLevelName: '',
    hazardPosition: '',
    hazardSource: model.value.hazardSource,
    hazardSourceName: '',
    hazardType: modelArray.value[0].hazardType,
    hazardTypeName: modelArray.value[0].hazardTypeName,
    latitude: 0,
    longitude: 0,
    mapX: 0,
    mapY: 0,
    mapZ: 0,
    randomCheckId: modelArray.value[0].randomCheckId,
    reformUserJson: [],
    remark: '',
    search: '',
    startTime: '',
    superviseUnitId: userinfo.deptId,
    superviseUnitName: userinfo.deptName,
    unitId: modelArray.value[0].unitId,
    unitName: modelArray.value[0].unitName,
    updateBy: '',
    zhId: userinfo.zhId,
    timeoutDays: 0,
    id: '',
    morehazardPlace: '',
    planId: getplanId.value,
    createByName: userinfo.userName,
    taskId: taskId.value,
    hazardRandomCheckEventUsers: [],
  })
}
// const blFileList = ref([])
// // 图片列表
// function upAllImage(files) {
//   // console.log(files, '=========files===获取图片列表')
//   blFileList.value = files
// }

/* 获取分类 */

// const deviceTypeArr = ref([])
const deviceTypeArr = ref('')

const EssentialFactor = ref<any>([])
function gethazardEssentialFactorClass(unitIdval) {
  // console.log(unitIdval, '================调用获取隐患分类')
  posthazardEssentialFactorClassAPI({ unitId: unitIdval })
    .then((res: any) => {
      EssentialFactor.value = res.data
      // // console.log(EssentialFactor.value, '=========================res.data')
    })
    .finally(() => {
      // console.log(111)
    })
}
// 隐患等级
const gradeList = ref<any>([])
function getgradeList(unitIdval) {
  // console.log(unitIdval, '================调用获取隐患等级')
  posthazardGradeAPI({ unitId: unitIdval })
    .then((res) => {
      gradeList.value = res.data as any[]
    })
    .finally(() => {
      // console.log(111)
    })
}

// const { findChildrenByCode } = useColPickerData()
// const columnChange = ({ selectedItem, resolve, finish }) => {
//   const areaData = findChildrenByCode(colPickerData.value, selectedItem.value)
//   if (areaData && areaData.length) {
//     resolve(
//       areaData.map((item) => {
//         return {
//           value: item.id,
//           label: item.className,
//           ...item,
//         }
//       }),
//     )
//   } else {
//     finish()
//   }
// }

// 格式化方法

const displayFormat = (selectedItems) => {
  // console.log(selectedItems)
  if (!selectedItems.length) return
  return selectedItems.map((item) => item.label).join('>')
}

const indexnum: any = ref(0)
// const oldposition = ref('')
const BuildingObj = ref({})
onShow(() => {
  if (uni.getStorageSync('checkFormIndex')) {
    indexnum.value = uni.getStorageSync('checkFormIndex')
  }
  // // console.log(indexnum.value, '===================indexnum.value')
  if (modelArray.value.length > 0) {
    if (uni.getStorageSync('FactorClassItem')) {
      // 如果选择的隐患库中的检查项不一样，则重新赋值
      if (
        modelArray.value[indexnum.value].essentialFactorClassItemId !==
        uni.getStorageSync('FactorClassItem').essentialFactorClassItemId
      ) {
        // 隐患描述
        modelArray.value[indexnum.value].hazardDesc =
          uni.getStorageSync('FactorClassItem').hazardDescribe
        // 隐患库iD
        modelArray.value[indexnum.value].typePid =
          uni.getStorageSync('FactorClassItem').essentialFactorId
        disabled.value = false
        // if (EssentialFactor.value.length <= 0) {
        //   gethazardEssentialFactorClass(modelArray.value[indexnum.value].typePid, '')
        // }
        // 隐患分类iD
        modelArray.value[indexnum.value].hazardType =
          uni.getStorageSync('FactorClassItem').essentialFactorClassId

        modelArray.value[indexnum.value].hazardTypeName =
          uni.getStorageSync('FactorClassItem').essentialFactorClassName

        // 隐患等级id
        modelArray.value[indexnum.value].hazardLevel =
          uni.getStorageSync('FactorClassItem').hazardGradeId

        // deviceTypeArr.value = modelArray.value[indexnum.value].hazardType
        //   ? modelArray.value[indexnum.value].hazardType
        //   : uni.getStorageSync('FactorClassItem').essentialFactorClassId
        // 从隐患库中带出隐患分类
        FactorClassFullName.value = uni.getStorageSync('FactorClassItem').essentialFactorClassName
      }
      modelArray.value[indexnum.value].essentialFactorClassItemId =
        uni.getStorageSync('FactorClassItem').essentialFactorClassItemId

      // 如果从隐患库中选择检查项ID了，任务默认检查表id就赋空
      if (modelArray.value[indexnum.value].essentialFactorClassItemId) {
        modelArray.value[indexnum.value].checkAreaEssentialFactorId = ''
      }
    }
    if (uni.getStorageSync('Building') && uni.getStorageSync('BuildingFool')) {
      // 添加楼栋楼层id
      BuildingObj.value = uni.getStorageSync('Building')
      if (modelArray.value[indexnum.value].morehazardPlace === '') {
        modelArray.value[indexnum.value].floorId = uni.getStorageSync('Building').value
        modelArray.value[indexnum.value].buildingId = uni.getStorageSync('Building').parentId
        modelArray.value[indexnum.value].morehazardPlace =
          uni.getStorageSync('Building').parentName + uni.getStorageSync('Building').label
        modelArray.value[indexnum.value].hazardPosition =
          uni.getStorageSync('Building').parentName + uni.getStorageSync('Building').label
      }
    }
    if (uni.getStorageSync('pointerinfo')) {
      modelArray.value[indexnum.value].mapX = uni.getStorageSync('pointerinfo').x
      modelArray.value[indexnum.value].mapY = uni.getStorageSync('pointerinfo').y
    }
  }
})

// 监听楼栋楼层
// watch(
//   () => BuildingObj.value.parentName,
//   (val) => {
//     // console.log(val, 'aaaaaa=========val')
//     modelArray.value[indexnum.value].floorId = uni.getStorageSync('Building').value
//     modelArray.value[indexnum.value].buildingId = uni.getStorageSync('Building').parentId
//     modelArray.value[indexnum.value].morehazardPlace =
//       uni.getStorageSync('Building').parentName + uni.getStorageSync('Building').label
//     modelArray.value[indexnum.value].hazardPosition =
//       uni.getStorageSync('Building').parentName + uni.getStorageSync('Building').label
//   },
// )
// // 监听楼栋楼层
// watch(
//   () => BuildingObj.value.label,
//   (val) => {
//     // console.log(val, 'bbbbb=========val')
//     modelArray.value[indexnum.value].floorId = uni.getStorageSync('Building').value
//     modelArray.value[indexnum.value].buildingId = uni.getStorageSync('Building').parentId
//     modelArray.value[indexnum.value].morehazardPlace =
//       uni.getStorageSync('Building').parentName + uni.getStorageSync('Building').label
//     modelArray.value[indexnum.value].hazardPosition =
//       uni.getStorageSync('Building').parentName + uni.getStorageSync('Building').label
//   },
// )

// 监听单位id
// watch(
//   () => modelArray.value[indexnum.value].unitId,
//   (val) => {
//     // console.log(val, 'aaaaaa=========val')
//   },
// )
/* 获取位置 */
function handleposition(index) {
  uni.setStorageSync('checkFormIndex', index)
  indexnum.value = index
  if (modelArray.value[indexnum.value].unitId) {
    uni.navigateTo({
      url: `/pages/hazardPhoto/common-position?selunitid=${modelArray.value[indexnum.value].unitId}`,
    })
  } else {
    uni.showToast({
      icon: 'none',
      title: '请先选择隐患单位',
    })
  }
}
// 获取图片列表
function getFilelist(event) {
  setTimeout(() => {
    // console.log('============第' + uni.getStorageSync('checkFormIndex') + '条数据图片列表')
    indexnum.value = uni.getStorageSync('checkFormIndex')
    modelArray.value[indexnum.value].files = event
  }, 100)
}
function deleteImg() {
  // console.log(modelArray.value[indexnum.value].files)
  // // console.log(event)
}
// 选择隐患分类
// function handleConfirm({ value, selectedItems }) {
//   modelArray.value[indexnum.value].hazardType = selectedItems[1].value
//   modelArray.value[indexnum.value].hazardTypeName =
//     selectedItems[0].label + '>' + selectedItems[1].label
// }
// 选择隐患分类
function handleConfirm({ value, selectedItems }) {
  // console.log(selectedItems, 'selectedItems选择隐患分类')
  modelArray.value[indexnum.value].hazardType = selectedItems.id
  modelArray.value[indexnum.value].hazardTypeName = selectedItems.className
}
// 选择隐患等级
function handleConfirm1({ value, selectedItems }) {
  // console.log(selectedItems, 'selectedItems选择隐患等级')
  modelArray.value[indexnum.value].hazardLevel = selectedItems.id
  // modelArray.value[indexnum.value].hazardType = selectedItems.id
  // modelArray.value[indexnum.value].hazardTypeName = selectedItems.className
}

const getindex = (index: number) => {
  // console.log(index)
  uni.setStorageSync('checkFormIndex', index)
  indexnum.value = index
}

// const getindex1 = (index: number) => {
//   // console.log(index)
//   uni.setStorageSync('checkFormIndex', index)
//   indexnum.value = index
//   if (!modelArray.value[indexnum.value].hazardType) {
//     // 可以在这里添加提示信息
//     uni.showToast({
//       title: '请先选择隐患库',
//       icon: 'none',
//     })
//     disabled.value = true
//   }
// }

// 获取选择的隐患等级
// function getGrade(event) {
//   modelArray.value[indexnum.value].hazardLevel = event.id
//   // modelArray.value[indexnum.value].hazardLevelName = event.gradeName
// }
// 调用获取隐患等级和分类
function getGradeAndLeve(code) {
  idreadonly.value = false
  postTopLevelOrgCodeAPI({ orgCode: code })
    .then((_res: any) => {
      // console.log(_res.data.orgCode, '=========================topUnitId')
      gethazardEssentialFactorClass(_res.data.orgCode)
      getgradeList(_res.data.orgCode)
    })
    .catch(() => {
      // console.log(111)
    })
}

// const orgUnitId = ref('')
const getUnit = (data) => {
  // 获取隐患分类
  if (modelArray.value[indexnum.value].unitId !== data.id) {
    getGradeAndLeve(data.id)
    uni.removeStorageSync('Building')
    uni.removeStorageSync('pointerinfo')
    uni.removeStorageSync('BuildingFool')
    modelArray.value[indexnum.value].floorId = ''
    modelArray.value[indexnum.value].buildingId = ''
    modelArray.value[indexnum.value].hazardPosition = ''
    modelArray.value[indexnum.value].morehazardPlace = ''
    modelArray.value[indexnum.value].mapX = null
    modelArray.value[indexnum.value].mapY = null
  }
  modelArray.value[indexnum.value].unitId = data.id
  modelArray.value[indexnum.value].unitName = data.unitName
}
// 获取整改人
function reception(event) {
  modelArray.value[indexnum.value].reformUserJson = convertToreformUserJson(
    JSON.parse(JSON.stringify(event)),
  )
}

// 选点
function SelectXY(index) {
  if (modelArray.value[index].buildingId) {
    // hiddenType== '0' 从随机检查进入
    // 隐患上报取orgUnitId   随机检查取model.value.unitId
    let id
    if (hiddenType.value === '0') id = model.value.unitId
    else id = modelArray.value[index].unitId
    uni.navigateTo({
      url: `/pages/map/index?status=1&unitId=${id}&buildId=${modelArray.value[index].buildingId}&floorId=${modelArray.value[index].floorId}&x=${modelArray.value[index].mapX}&y=${modelArray.value[index].mapY}`,
    })
  } else {
    uni.showToast({
      icon: 'none',
      title: '请先选择楼栋楼层',
    })
  }
}
// 删除新增隐患
function delmodelArraybyindex(index) {
  modelArray.value.splice(index, 1)
  // 重新渲染页面
  modelArray.value = [...modelArray.value]
  // console.log(modelArray.value, '删除后的modelArray.value')
}
// const handleBeforeConfirm = (event) => {
//   // // console.log(index)
//   // console.log(event, '==================// console.log(event)')
//   // uni.setStorageSync('checkFormIndex', index)
//   // indexnum.value = index
//   if (!modelArray.value[indexnum.value].typePid) {
//     // 可以在这里添加提示信息
//     uni.showToast({
//       title: '请先选择隐患库',
//       icon: 'none',
//     })
//   }
// }
/* 提交按钮 */
const $emit = defineEmits(['loading'])
const handleSubmit = debounce(() => {
  // console.log(modelArray.value)
  // $emit('loading', true)
  // loading.value = true
  // 非空判断======开始
  for (let i = 0; i < modelArray.value.length; i++) {
    // if (modelArray.value[i].essentialFactorClassItemId) {
    //   modelArray.value[i].checkAreaEssentialFactorId = ''
    // }
    if (!modelArray.value[i].unitId) {
      uni.showToast({
        icon: 'none',
        title: '请选隐患单位',
      })
      // loading.value = false
      $emit('loading', false)
      return
    }
    if (!modelArray.value[i].hazardDesc) {
      uni.showToast({
        icon: 'none',
        title: '请输入隐患描述',
      })
      // loading.value = false
      $emit('loading', false)
      return
    }
    if (model.value.pagetype === 0 || apitype.value !== '') {
      if (!modelArray.value[i].hazardType) {
        uni.showToast({
          icon: 'none',
          title: '请选择隐患分类',
        })
        // loading.value = false
        $emit('loading', false)
        return
      }
    }
    if (model.value.pagetype === 1) {
      if (!modelArray.value[i].hazardTypeName) {
        uni.showToast({
          icon: 'none',
          title: '请输入隐患分类',
        })
        // loading.value = false
        $emit('loading', false)
        return
      }
    }

    if (!modelArray.value[i].hazardLevel) {
      uni.showToast({
        icon: 'none',
        title: '请选择隐患等级',
      })
      // loading.value = false
      $emit('loading', false)
      return
    }
    if (!modelArray.value[i].morehazardPlace) {
      uni.showToast({
        icon: 'none',
        title: '请输入隐患位置',
      })
      // loading.value = false
      $emit('loading', false)
      return
    }
    if (modelArray.value[i].files.length === 0) {
      uni.showToast({
        icon: 'none',
        title: '请选择上传隐患图片',
      })
      // loading.value = false
      $emit('loading', false)
      return
    }
    if (modelArray.value[i].reformUserJson.length === 0) {
      uni.showToast({
        icon: 'none',
        title: '请选择整改人员',
      })
      // loading.value = false
      $emit('loading', false)
      return
    }
    if (modelArray.value[i].mapX === 0 || modelArray.value[i].mapY === 0) {
      uni.showToast({
        icon: 'none',
        title: '请选择点位',
      })
      // loading.value = false
      $emit('loading', false)
      return
    }
  }

  // uni.showLoading({
  //   title: '提交中...',
  //   icon: 'none',
  // })
  if ((!checkId.value && !taskId.value) || apitype.value !== '') {
    // 隐患上报
    // console.log('隐患上报')
    posthazardRecordAddEventAPI(JSON.parse(JSON.stringify(modelArray.value)))
      .then((res: any) => {
        uni.hideLoading()
        if (res.code === 'error') {
          uni.showToast({
            icon: 'none',
            title: res.message || '请求错误',
          })
          return
        }
        uni.removeStorageSync('FactorClassItem')
        uni.removeStorageSync('Building')
        uni.removeStorageSync('pointerinfo')
        uni.removeStorageSync('BuildingFool')
        uni.removeStorageSync('userInfoObj')
        uni.showToast({
          icon: 'none',
          title: '添加成功',
        })
        $emit('loading', true)
        // uni.navigateBack({
        //   delta: 1,
        // })
        // goBack()
      })
      .catch(() => {
        uni.showToast({
          icon: 'none',
          title: '网络错误',
        })
        // loading.value = false
        $emit('loading', false)
      })
      .finally(() => {
        // loading.value = false
        $emit('loading', false)
      })
  } else {
    // 任务隐患记录表
    // console.log('任务隐患记录表')
    posthazardPlanTaskEventaddAPI(JSON.parse(JSON.stringify(modelArray.value)))
      .then((res: any) => {
        if (res.code === 'error') {
          uni.showToast({
            icon: 'none',
            title: res.message || '请求错误',
          })
          return
        }
        uni.removeStorageSync('FactorClassItem')
        uni.removeStorageSync('Building')
        uni.removeStorageSync('pointerinfo')
        uni.removeStorageSync('BuildingFool')
        uni.removeStorageSync('userInfoObj')
        uni.showToast({
          icon: 'none',
          title: '添加成功',
        })
        $emit('loading', true)
        // goBack()
        // uni.navigateBack({
        //   delta: 1,
        // })
      })
      .catch(() => {
        uni.showToast({
          icon: 'none',
          title: '网络错误',
        })
        // loading.value = false
        $emit('loading', false)
      })
      .finally(() => {
        // loading.value = false
        // $emit('loading', false)
      })
  }
}, 500)

defineExpose({ handleSubmit })
// handleSubmit

function convertToreformUserJson(items) {
  return items.map((item) => ({
    reformUserName: item.userName,
    reformUserId: item.id,
    reformType: 0,
  }))
}
</script>

<style lang="scss" scoped>
::v-deep {
  .wd-cell__value {
    font-size: 0.8rem;
    color: #999999 !important;
  }

  .text .wd-cell__value {
    color: rgba($color: #000000, $alpha: 0.8) !important;
  }

  .wd-button.is-block {
    width: 100% !important;
    height: 100% !important;
    border-radius: 0 !important;
  }

  .wd-col-picker__cell.is-align-right .wd-col-picker__value {
    width: 100px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.FactorClassFullNamecalss {
  ::v-deep {
    .wd-select-picker__value--placeholder {
      color: black !important;
    }

    .wd-col-picker__value--placeholder {
      color: black !important;
    }
  }
}

.required::before {
  // position: absolute;
  // left: 0;
  top: 0.125rem;
  line-height: 1.1;
  color: #fa4350;
  content: '*';
}

.com-cell {
  margin: 0 1rem 0 1rem;
  // border: 0.0187rem solid #ccc;
}

.fixed-bottom {
  position: fixed;
  bottom: 0;
  z-index: 2;
  display: flex;
  flex-direction: row;
  align-items: center;
  width: 100%;
  height: 3.375rem;
  line-height: 3.375rem;
  // color: #fff;
  text-align: center;
  // background-color: white;
}

.form-title {
  display: flex;
  align-items: center;
  padding-top: 0.625rem;
  padding-left: 0.875rem;

  .form-title-icon {
    width: 4pt;
    height: 14pt;
    background-color: #597bf7;
    border-radius: 18pt;
  }
}

.select-address {
  display: flex;
  justify-content: flex-end;
  padding-right: 10px;
  margin: 10px 0px;

  .select-address-button {
    padding: 8px 14px;
    color: #fff;
    background-color: #597bf7;
    border-radius: 10px;
  }
}

.add-hazard {
  width: 96vw;
  height: 35px;
  margin: 10px auto 20px;
  line-height: 35px;
  color: #fff;
  text-align: center;
  background-color: #0256f7;
  border-radius: 6px;
}
</style>
