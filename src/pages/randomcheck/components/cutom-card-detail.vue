<template>
  <wd-card v-for="item in initialData" :key="item.id" @click="openDetail(item)">
    <template #title>
      <view class="title">
        <view style="overflow: hidden; text-overflow: ellipsis; white-space: nowrap">
          {{ item.hazardDesc }}
        </view>
        <view class="title-tip">
          <wd-tag style="min-width: 4rem; font-size: 0.675rem; text-align: center;padding: 0.3rem 0rem;" type="warning"
            mark>
            {{ item.hazardLevelName }}
          </wd-tag>
          <!-- :type="
              item.disposeState === '0'
                ? 'warning'
                : item.disposeState === '1'
                  ? 'primary'
                  : 'success'
            " -->
          <!-- <wd-button size="small" type="success" plain>
            {{ item.hazardLevelName }}
          </wd-button> -->
        </view>
      </view>
    </template>
    <!-- <view class="custominfo">
      <view
        class="c-font14"
        style="max-width: 20ch; overflow: hidden; text-overflow: ellipsis; white-space: nowrap"
      >
        {{ item.hazardPosition }}
      </view>
      <view
        class="c-font14"
        style="max-width: 20ch; overflow: hidden; text-overflow: ellipsis; white-space: nowrap"
      >
        {{ item.hazardTypeName }}
      </view>
    </view> -->
    <!-- <view class="custominfo">
      <view class="c-font14">
        <span v-for="(info, i) in item.hazardRandomCheckEventUsers" :key="i">
          {{ info.reformUserName }},
        </span>
      </view>
    </view>  custominfo2-->
    <view class="custominfo">
      <view class="c-font14">{{ item.hazardPosition }}</view>
    </view>
    <!-- <view class="custominfo">
      <view class="c-font14">
        <span v-for="(info, i) in item.hazardRandomCheckEventUsers" :key="i">
          {{ info.reformUserName }},
        </span>
      </view>
    </view> -->
    <view class="custominfo">
      <view class="c-font14">检查人: {{ item.createByName || '--' }}</view>
    </view>
    <view class="custominfo">
      <view class="c-font14">检查时间: {{ item.createTime }}</view>
    </view>
  </wd-card>
</template>

<script lang="ts" setup>
import { addForms } from '../type'
defineProps<{
  initialData: addForms<any>[]
}>()
const checkStatus = ref<any>('')
onLoad((paramas) => {
  checkStatus.value = paramas.checkStatus
  // console.log(paramas.checkStatus, '==========checkStatus===============')
})

function openDetail(item) {
  // console.log(item)
  // checkStatus
  uni.navigateTo({
    url: `/pages/dangerlist/detail?id=${item.id}&flag=${item.flag}&disposeId=${item.disposeId}`,
  })
}
</script>

<style lang="scss" scoped>
::v-deep {
  .wd-card__title-content {
    padding: 8px 0 !important;
  }

  .wd-card {
    background-color: #ebeef5;
  }

  .wd-card__footer {
    padding: 0.375rem 0 0.375rem !important;
  }
}

.content,
.title {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
}

.content {
  justify-content: flex-start;
}

.title {
  justify-content: space-between;
}

.title-tip {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.25);
}

.custominfo {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}

.custominfo2 {
  view {
    width: 49%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.c-font14 {
  padding: 0.125rem 0;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.85);
}
</style>
