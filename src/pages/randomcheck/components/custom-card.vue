<template>
  <wd-card v-for="item in initialData" :key="item.id" @click="selinfo(item.id, item.checkStatus, item.objId)">
    <template #title>
      <view class="title">
        <view style="font-weight: 600">{{ item.checkName }}</view>
        <view class="title-tip">
          <!-- 状态（0未开始 1进行中 2已完成） -->
          <wd-button :type="item.checkStatus === '0'
              ? 'warning'
              : item.checkStatus === '1'
                ? 'primary'
                : 'success'
            " size="small" plain style="background-color: #dce6fa">
            {{ item.checkStatusName }}
          </wd-button>
        </view>
      </view>
    </template>
    <view class="content">
      <view>
        <view class="content_itme">
          {{ item.objName }}
        </view>
      </view>
    </view>
    <view class="custominfo">
      <view class="content_itme">
        {{ item.createTime }}
      </view>
    </view>
  </wd-card>
</template>

<script lang="ts" setup>
import { ResponsesData, pageData, pageForm } from '../type'
defineProps<{
  initialData: pageForm[]
}>()
// defineProps({
//   initialData: {
//     type: Array<pageData<pageForm>>,
//     default: [],
//   },
// })
const loading = ref(false)

// 时间图标按钮
function selinfo(id: string, checkStatus, objId: string) {
  // // console.log(id)
  // console.log('---------点击操作')
  uni.navigateTo({
    url: `/pages/randomcheck/detail?id=${id}&checkStatus=${checkStatus}&objId=${objId}`,
  })
}
</script>

<style lang="scss">
::v-deep {
  .wd-card {
    background: #ebeef5 !important;
  }

  .wd-card__footer {
    padding: 4px 2px !important;
  }

  .wd-button.is-plain.is-primary {
    background: #4d80f02b;
  }
}

.content,
.title {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
}

.content {
  justify-content: flex-start;
}

.title {
  justify-content: space-between;
}

.title-tip {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.25);
}

::v-deep .wd-card {
  background-color: #f5f6f9 !important;
}

.custominfo {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}

::v-deep .wd-card__title-content {
  padding-bottom: 0 !important;
}

.content_itme {
  padding: 0px 0px 4px 0px;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.85);
}
</style>
