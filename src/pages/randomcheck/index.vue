<route lang="json5">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '随机检查',
  },
}
</route>
<template>
  <z-paging ref="paging" v-model="initialData" @query="LoadList">
    <template #top>
      <SafetyNavbar title="随机检查"></SafetyNavbar>
      <!-- 下拉菜单 -->
      <view class="tabs-content">
        <wd-drop-menu style="z-index: 99">
          <wd-drop-menu-item
            title="任务状态"
            v-model="checkStatusvaule"
            :options="checkStatusList"
            @change="handleChange1"
          />
          <!-- <wd-drop-menu-item title="检查对象" v-model="unitvalue" value-key="id" label-key="unitName" :options="UnitList"
            @change="handleChange2" /> -->
          <wd-drop-menu-item title="检查对象" ref="dropMenu">
            <wd-search
              placeholder-left
              @change="searchunitname"
              placeholder="检查对象"
              :hide-cancel="true"
              v-model="likeFieldValue"
            />
            <view style="width: 90%; margin: auto">
              <wd-radio-group
                v-if="UnitList.length > 0"
                v-model="unitvalue"
                @change="handleChangeCheckUnitId"
              >
                <scroll-view style="height: 30vh" scroll-y="true">
                  <wd-radio v-for="(itme, index) in UnitList" :key="index" :value="itme.id">
                    {{ itme.unitName }}
                  </wd-radio>
                </scroll-view>
              </wd-radio-group>
              <view v-else class="flex items-center justify-center mt-[2%]">
                <view class="relative">
                  <img src="@/static/images/no-data.png" width="237px" height="165px" />
                  <text
                    class="absolute bottom-[20px] left-[0] w-[237px] text-center color-[#969799]"
                  >
                    暂无数据
                  </text>
                </view>
              </view>
            </view>
          </wd-drop-menu-item>
        </wd-drop-menu>
      </view>
    </template>
    <view class="container">
      <view v-if="initialData.length">
        <customCard :initialData="initialData"></customCard>
      </view>
      <template v-else>
        <view class="list-null">
          <noData title="暂无数据~"></noData>
        </view>
      </template>
    </view>
  </z-paging>
  <view class="addbtn" @click="handleClickRight">
    <view>+</view>
  </view>
</template>

<script lang="ts" setup>
import SafetyNavbar from '../components/safety-navbar.vue'
import CustomTabs from '../components/custom-Tabs.vue'
import customCard from './components/custom-card.vue'
import { ResponsesData, pageData, pageForm } from './type'
import { postAllUnitAPI, postHazardRandomCheckAPI } from './featch'
import { useUserStore } from '@/store'
import { debounce } from '@/utils'
const userStore = useUserStore()
const userinfo: any = userStore.userInfo ? userStore.userInfo : {}
const Parameters = ref<any>({
  objId: userinfo.unitId,
  pageNo: 1,
  pageSize: 10,
  objName: '',
  checkStatus: '',
  checkStatusName: '',
  // unitId: userinfo.unitId,
  createBy: userinfo.id,
  roleCodes: userinfo.roleCodes,
})
const paging = ref()
const total = ref(0)
const initialData = ref<pageForm[]>([])

const likeFieldValue = ref('')
const searchunitname = debounce(() => {
  UnitList.value = UnitListAll.value.filter((itme) => {
    return itme.unitName.includes(likeFieldValue.value)
  })
}, 500)

function LoadList(pageNo) {
  uni.showLoading({ mask: true })
  Parameters.value.pageNo = pageNo
  postHazardRandomCheckAPI(Parameters.value)
    .then((res: any) => {
      uni.hideLoading()
      total.value = res.data.total
      paging.value.completeByTotal(res.data.rows, total.value)
    })
    .finally(() => {
      uni.hideLoading()
    })
}
onShow(() => {
  paging.value.reload()
})
function handleChangeCheckUnitId() {
  Parameters.value.pageNo = 1
  unitvalue.value === '0000'
    ? (Parameters.value.objId = userinfo.unitId)
    : (Parameters.value.objId = unitvalue.value)
  paging.value.reload()
}
function handleClickRight() {
  uni.navigateTo({
    url: `/pages/randomcheck/createcheck`,
  })
}
// 下拉菜单
const checkStatusvaule = ref<number>(0)
const checkStatusList = ref<any>([
  { label: '全部', value: 0 },
  { label: '进行中', value: 1 },
  { label: '已完成', value: 2 },
])
const UnitList = ref<any[]>([{ unitName: '全部', id: '0000' }])
const unitvalue = ref<string>('0000')
const UnitListAll = ref<any[]>([])
AllUnit()
function AllUnit() {
  // userinfo.unitId
  // 处理相关方登录 查询检查对象列表
  uni.showLoading({ mask: true })
  postAllUnitAPI({
    orgCode: +userinfo.orgRes === 1 ? userinfo.unitId : userinfo.serverUnitId,
    pageSize: -1,
  })
    .then((res: any) => {
      uni.hideLoading()
      UnitList.value.push(...res.data.rows)
      UnitListAll.value = UnitList.value
    })
    .finally(() => {
      uni.hideLoading()
    })
}
function handleChange1({ value }) {
  Parameters.value.pageNo = 1
  value === 0 ? (Parameters.value.checkStatus = '') : (Parameters.value.checkStatus = value)
  paging.value.reload()
}
// function handleChange2({ value }) {
//   Parameters.value.pageNo = 1
//   value === '0000' ? (Parameters.value.objId = userinfo.unitId) : (Parameters.value.objId = value)
//   paging.value.reload()
// }
</script>

<style lang="scss" scoped>
::v-deep {
  .wd-search {
    background: none !important;
    border: none !important;
  }

  .wd-card {
    background-color: #ebeef5 !important;
  }

  .navbar {
    position: unset !important;
  }
}

.wd-drop-item {
  z-index: 99 !important;
  // top: var(--window-top: 44px);
}

.borderBox {
  border-top: 0.0625rem solid #ebebeb;
}

.wd-drop-menu {
  margin-top: 8px;
}

.tabs-content {
  z-index: 99;
  // margin-top: -0.4375rem;
  // background-color: white;
  // border-radius: 10px;
}

.custominfo {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.addbtn {
  position: flex;
  position: absolute;
  right: 10%;
  bottom: 15%;
  z-index: 11;
  width: 2.5rem;
  height: 2.5rem;
  font-size: 3rem;
  font-weight: bold;
  line-height: 2.5rem;
  color: white;
  text-align: center;
  background-color: #2765d2b8;
  border-radius: 100%;
  box-shadow: 5px 5px 11px 0 rgba(0, 0, 0, 0.5);
}
</style>
