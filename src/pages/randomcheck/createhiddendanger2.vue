<route lang="json5">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '隐患上报',
  },
}
</route>

<template>
  <SafetyNavbar @click-left="handleClickLeft" :title="detailInfo?.hazardRandomCheck?.checkName || '隐患上报'">
  </SafetyNavbar>
  <view class="flex flex-col">
    <scroll-view style="height: 95vh" scroll-y="true">
      <customForm style="z-index: 99" @loading="getIscode" ref="customFormref"></customForm>
      <view class="container-placeholder"></view>
    </scroll-view>
    <div class="fixed-bottom">
      <wd-button type="info" size="large" @click="goBack" block>取消</wd-button>
      <wd-button type="primary" size="large" :loading="loading" @click="handleSubmit" block>
        保存
      </wd-button>
    </div>
  </view>
</template>

<script lang="ts" setup>
import SafetyNavbar from '../components/safety-navbar.vue'
import customForm from './components/custom-form.vue'
import { posthazardRandomCheckDetailAPI } from './featch'
const syscode = ref('')
const customFormref = ref(null)
const loading = ref(false)
// const apitype = ref<any>('') // 请求接口判断
onLoad((params) => {
  if (Object.keys(params).length !== 0) {
    if (params.checkId) {
      setTimeout(async () => {
        await getInfo(params.checkId)
      })
    }
    if (params.syscode) {
      syscode.value = params.syscode
    }
  }
})
// 显示头部
const detailInfo = ref()
const getInfo = async (id: string) => {
  const { data } = await posthazardRandomCheckDetailAPI(id)
  detailInfo.value = data
  // console.log(detailInfo)
}
function handleClickLeft() {
  // uni.navigateBack() || window.history.back()
  if (syscode.value) {
    uni.removeStorageSync('FactorClassItem')
    uni.removeStorageSync('Building')
    uni.removeStorageSync('pointerinfo')
    uni.removeStorageSync('BuildingFool')
    uni.removeStorageSync('userInfoObj')
    window.history.back()
  } else {
    uni.navigateBack()
  }
}
onUnload(() => {
  uni.removeStorageSync('FactorClassItem')
  uni.removeStorageSync('Building')
  uni.removeStorageSync('pointerinfo')
  uni.removeStorageSync('BuildingFool')
  // BuildingFool
})

// function goBack() {
//   customFormref.value.goBack()
// }
// 取消上报
const goBack = () => {
  // uni.navigateBack() || window.history.back()
  if (syscode.value) {
    window.history.back()
  } else {
    uni.navigateBack()
  }
}
const iscode = ref(false)
function getIscode(val) {
  // console.log(val, "======1111")
  iscode.value = val
}
function handleSubmit() {
  loading.value = true
  customFormref.value.handleSubmit()
  setTimeout(function () {
    // console.log(iscode.value, "=======222")
    if (iscode.value) {
      goBack()
    } else {
      loading.value = false
    }
  }, 100)
  // loading.value = false
  // if (loading.value)
  // goBack()
}
</script>

<style lang="scss">
// .container-box {
//   height: calc(100vh - 86px);
//   overflow: scroll;
// }
::v-deep {
  .wd-cell__value {
    font-size: 0.8rem;
    color: #999999 !important;
  }

  .wd-button.is-block {
    width: 100% !important;
    height: 100% !important;
    border-radius: 0 !important;
  }
}

.com-cell {
  margin: 0 1rem 0 1rem;
  border: 0.0187rem solid #ccc;
}

.fixed-bottom {
  position: fixed;
  bottom: 0;
  display: flex;
  flex-direction: row;
  align-items: center;
  width: 100%;
  height: 3.375rem;
  line-height: 3.375rem;
  color: #fff;
  text-align: center;
  background-color: white;
  z-index: 2;
}

.container-placeholder {
  width: 100%;
  height: 3.375rem;
}
</style>
