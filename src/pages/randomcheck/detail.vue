<route lang="json5">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '任务详情及隐患上报',
  },
}
</route>

<template>
  <SafetyNavbar
    :title="
      detailInfo?.hazardRandomCheck.checkName && detailInfo?.hazardRandomCheck.checkName.length > 9
        ? `${detailInfo?.hazardRandomCheck.checkName.substring(0, 9)}...`
        : detailInfo?.hazardRandomCheck.checkName || '任务详情及隐患上报'
    "
  ></SafetyNavbar>

  <wd-collapse v-model="value">
    <wd-collapse-item title="检查情况" name="item1">
      <commonStatistics
        ref="commonStatisticsRef"
        :taskId="paramsForm.randomCheckId"
        :isPlanTask="detailInfo?.hazardRandomCheck.checkStatus != '2'"
        :type="3"
        :unitId="userinfo.unitId"
        :topUnitId="userinfo.topUnitId"
        :createBy="userinfo.id"
      ></commonStatistics>
    </wd-collapse-item>
  </wd-collapse>

  <view class="container">
    <wd-search
      placeholder-left
      placeholder="请输入隐患位置/类型模糊搜索"
      v-model="paramsForm.likeFieldValue"
      :hide-cancel="true"
      clearable
      @change="handleChange"
      @clear="clear"
    />
    <scroll-view
      style="height: 54vh; padding-bottom: 5rem"
      scroll-into-view="bottom"
      @scrolltolower="handleScrollToLower"
      scroll-y="true"
    >
      <cutomCardDetail :initialData="initialData"></cutomCardDetail>
      <view style="color: #ccc; text-align: center" v-if="initialData.length === 0">暂无数据</view>
      <view
        id="bottom"
        v-if="total > initialData.length"
        style="font-size: 12px; color: #232323; text-align: center"
      >
        加载更多数据
      </view>
    </scroll-view>
    <!-- 底部按钮 -->
    <view class="fixed-bottom">
      <wd-button
        style="min-width: 90px"
        v-if="
          checkStatus !== '2' &&
          (arrayContainsValuebyuser(commandUserList, userinfo.id) ||
            arrayContainsValuebyuser(checkUserList, userinfo.id))
        "
        :loading="loading"
        @click="goend"
      >
        结束任务
      </wd-button>
      <wd-button @click="checkHistoryHidden" plain hairline>检查对象历史隐患</wd-button>
      <wd-button
        style="min-width: 90px"
        v-if="
          checkStatus !== '2' &&
          (arrayContainsValuebyuser(commandUserList, userinfo.id) ||
            arrayContainsValuebyuser(checkUserList, userinfo.id))
        "
        size="medium"
        type="error"
        @click="Reporthiddendanger"
      >
        上报隐患
      </wd-button>
    </view>
  </view>
</template>

<script lang="ts" setup>
import SafetyNavbar from '../components/safety-navbar.vue'
import cutomCardDetail from './components/cutom-card-detail.vue'
import commonStatistics from '@/components/commonSelect/common-statistics.vue'
import {
  postchangeRandomCheckAPI,
  posthazardMegerpageEventAPI,
  posthazardRandomCheckDetailAPI,
  postHazardRecordPageEventAPI,
} from './featch'
import { addForms, pageForm } from './type'
import { useUserStore } from '@/store'
import { debounce } from '@/utils'

function arrayContainsValuebyuser(arr, value: string) {
  // console.log(arr, Object.prototype.toString.call(arr), '详情==是否是检查人===========arr')
  if (arr?.length > 0 && arr !== undefined) {
    return arr.some((item) => item.checkUserId === value)
  } else {
    return false
  }
}
const userStore = useUserStore()
const userinfo: any = userStore.userInfo ? userStore.userInfo : {}
const commonStatisticsRef = ref()
const paramsForm = ref<any>({
  pageNo: 1,
  pageSize: 5,
  randomCheckId: '',
  webSearch: '',
  search: '',
  likeFieldValue: '',
  taskId: '',
  likeFields: 'hazardPosition,hazardTypeName',
  businessId: '',
  flag: '',
})
const value = ref<string[]>(['item1'])
const detailInfo = ref()
const commandUserList = ref([]) // 负责人
const checkUserList = ref([]) // 检查人
const getInfo = async (id: string) => {
  const { data } = await posthazardRandomCheckDetailAPI(id)
  detailInfo.value = data
  // console.log(detailInfo.value, '=======================detailInfo')
  commandUserList.value = detailInfo.value.commandUserList
  checkUserList.value = detailInfo.value.checkUserList
}
const checkStatus = ref('')
const objId = ref('')
// const taskId = ref<any>('')
onLoad((params) => {
  checkStatus.value = params.checkStatus
  objId.value = params.objId
  paramsForm.value.randomCheckId = params.id
  paramsForm.value.taskId = params.id
  paramsForm.value.businessId = params.id
  paramsForm.value.flag = '01'
  // getInfo(params.id)
})

onShow(() => {
  setTimeout(async () => {
    await getInfo(paramsForm.value.taskId)
    paramsForm.value.pageNo = 1
    getListData()
  })
})
const total = ref(0)
/* 隐患分页列表-web */
const initialData = ref<addForms<any>[]>([])
const getListData = async () => {
  // console.log('commonStatisticsRef.value', commonStatisticsRef.value)
  commonStatisticsRef.value.update()
  // 任务完成请求的统计接口
  initialData.value = []
  // if (checkStatus.value === '2') {
  //   const { data } = (await postHazardRecordPageEventAPI(paramsForm.value)) as any
  //   // console.log(data, '===========data')
  //   initialData.value = data.rows
  //   total.value = data.total
  // } else {
  //   const { data } = (await hazardPlanTaskEventAPI(paramsForm.value)) as any
  //   initialData.value = data.rows
  //   total.value = data.total
  // }
  // =======整合接口=======
  // encodeURIComponent(paramsForm.value.likeFieldValue)
  posthazardMegerpageEventAPI(paramsForm.value)
    .then((res: any) => {
      if (res.data) {
        initialData.value = res.data.rows
      } else {
        initialData.value = []
      }
    })
    .finally(() => {})
}

// 结束任务
const loading = ref(false)
const goend = () => {
  loading.value = true
  uni.showModal({
    title: '提示',
    content: '请确认是否要结束任务?',
    success: function (res) {
      if (res.confirm) {
        postchangeRandomCheckAPI({
          checkStatus: '2',
          checkStatusName: '已完成',
          id: paramsForm.value.randomCheckId,
        })
          .then((res: any) => {
            if (res.code === 'error') {
              uni.showToast({
                icon: 'none',
                title: res.data.message || '操作失败',
              })
              loading.value = false
              return
            }
            uni.showToast({
              icon: 'none',
              title: '操作成功',
            })
            checkStatus.value = '2'
            // getListData()
          })
          .finally(() => {})
      } else if (res.cancel) {
        // 在这里执行取消后的操作
      }
    },
  })
}
/* 搜索 */
// let timeoutId: ReturnType<typeof setTimeout> | null = null
// function handleChange() {
//   if (timeoutId !== null) {
//     clearTimeout(timeoutId)
//     timeoutId = null
//   }
//   timeoutId = setTimeout(() => {
//     paramsForm.value.pageNo = 1
//     getListData()
//   }, 500)
// }
const handleChange = debounce(() => {
  paramsForm.value.pageNo = 1
  getListData()
}, 500)

function clear() {
  paramsForm.value.likeFieldValue = ''
}
/* 滚动加载更多 */
const state = ref<string>('loading')
function handleScrollToLower() {
  if (total.value === initialData.value.length) {
    state.value = 'nodata'
    return
  }
  setTimeout(() => {
    // ++ 更改页码
    paramsForm.value.pageNo++
    // encodeURIComponent(paramsForm.value.likeFieldValue)
    posthazardMegerpageEventAPI(paramsForm.value)
      .then((res: any) => {
        if (res.data) {
          initialData.value = [...initialData.value, ...res.data.rows]
        } else {
          initialData.value = []
        }
      })
      .finally(() => {})
    // if (checkStatus.value === '2') {
    //   postHazardRecordPageEventAPI(paramsForm.value)
    //     .then((res: any) => {
    //       if (res.data) {
    //         initialData.value = [...initialData.value, ...res.data.rows]
    //       } else {
    //         initialData.value = []
    //       }
    //     })
    //     .finally(() => {})
    // } else {
    //   hazardPlanTaskEventAPI(paramsForm.value)
    //     .then((res: any) => {
    //       if (res.data) {
    //         initialData.value = [...initialData.value, ...res.data.rows]
    //       } else {
    //         initialData.value = []
    //       }
    //     })
    //     .finally(() => {})
    // }
  }, 500)
}
/* 上报隐患 */
function Reporthiddendanger() {
  // 随机检查上报=====hazardSource=9
  // // console.log(detailInfo.value.hazardRandomCheck.unitId)
  uni.navigateTo({
    url: `/pages/randomcheck/createhiddendanger?taskId=${paramsForm.value.randomCheckId}&apitype=1&hazardSource=9&hiddenType=0&unitId=${detailInfo.value.hazardRandomCheck.objId}&types=0`,
  })
}
// 跳转历史隐患
function checkHistoryHidden() {
  uni.navigateTo({
    // url: `/pages/dangerlist/index?unitId=${detailInfo.value?.hazardRandomCheck.objId}&createBy=${userinfo.id}`,
    url: `/pages/task/historyList?apiTag=0&taskId=${paramsForm.value.randomCheckId}&objId=${objId.value}&unitIds=${detailInfo.value.hazardRandomCheck.objId}`,
  })
}
</script>

<style lang="scss">
::v-deep {
  .wd-collapse-item__arrow {
    color: #0052d9;
  }

  .wd-collapse-item__body {
    padding: 0 !important;
  }

  .wd-card {
    background-color: #ebeef5 !important;
  }
  .wd-button.is-medium.is-round {
    // min-width: 110px;
    // width: 100px;
  }
}

// 底部按钮部分
.fixed-bottom {
  position: fixed;
  bottom: 0;
  z-index: 2;
  box-sizing: border-box;
  display: flex;
  gap: 0.5rem;
  // flex-direction: row;
  align-items: center;
  // justify-content: space-around;
  justify-content: center;
  width: 100vw;
  height: 3.375rem;
  padding: 0 10px;
  line-height: 3.375rem;
  color: #fff;
  text-align: center;
  background-color: white;

  .wd-button {
    // width: 10%;
    flex: 1;
    // max-width: 50%;
    border-radius: 4px !important;
  }
}

.wd-collapse-item__title ::before {
  display: inline-block;
  width: 4px;
  height: 20px;
  margin-right: 3px;
  color: blue;
  content: '';
  background-color: blue;
  transform: translateY(4px);
}
</style>
