import { http } from '@/utils/http'
import { ResponsesData, addForms, pageData, pageForm } from './type'
import { $api } from '@/api'

/** post 请求 /hazardRandomCheck/page */
export const postHazardRandomCheckAPI = (Parameters: pageForm) => {
  return http.post<ResponsesData<pageData<pageForm>>>(
    $api.type.hazard + '/hazardRandomCheck/page',
    { ...Parameters },
  )
}

/** post 请求 /hazardRandomCheck/add */
export const posthazardRandomCheckAddAPI = (Parameters: pageForm) => {
  return http.post<ResponsesData<pageData<pageForm>>>($api.type.hazard + '/hazardRandomCheck/add', {
    ...Parameters,
  })
}

export const posthazardRandomCheckDetailAPI = (id: string) => {
  return http.post<ResponsesData<pageData<pageForm>>>(
    $api.type.hazard + `/hazardRandomCheck/detail?id=${id}`,
  )
}

/* post 隐患上报 /hazardRecord/addEvent */
export const posthazardRecordAddEventAPI = (Parameters: any) => {
  return http.post($api.type.hazard + '/hazardRecord/addEvent', Parameters)
}
/* post 隐患列表 /hazardRecord/pageEvent */
export const postHazardRecordPageEventAPI = (Parameters: any) => {
  return http.post($api.type.hazard + '/hazardRecord/pageEvent', { ...Parameters })
}

/* post 隐患列表 //hazardMeger/pageEvent */
export const posthazardMegerpageEventAPI = (Parameters: any) => {
  return http.post(
    $api.type.hazard +
      '/hazardMeger/pageEvent?businessId=' +
      Parameters.businessId +
      '&flag=' +
      Parameters.flag,
    { ...Parameters },
  )
}

/** post 请求 获取单位列表 hazardGrade/list   */
export const postAllUnitAPI = (Parameters: any) => {
  return http.post($api.type.hazard + '/ehsUpms/getAllUnit', Parameters)
}

// 获取检查表详情
export const inspectDetail = (Parameters: any) => {
  return http.post(
    $api.type.hazard +
      '/hazardCheckArea/detail?id=' +
      Parameters.id +
      '&checkAreaEssentialFactorId=' +
      Parameters.FactorId,
  )
}
/** post 请求 获取隐患分类列表 hazardEssentialFactorClass/treeList   */
export const posthazardEssentialFactorClassAPI = (Parameters: any) => {
  return http.post($api.type.hazard + '/hazardEssentialFactorClass/treeList', {
    ...Parameters,
  })
}

/** post 请求 /hazardPlanTaskEvent/add 任务隐患记录-新增  */
export const posthazardPlanTaskEventaddAPI = (Parameters: any) => {
  return http.post($api.type.hazard + '/hazardPlanTaskEvent/add', Parameters)
}

// /hazardRandomCheck/changeRandomCheck
export const postchangeRandomCheckAPI = (Parameters: any) => {
  return http.post(
    $api.type.hazard +
      '/hazardRandomCheck/changeRandomCheck?checkStatus=' +
      Parameters.checkStatus +
      '&checkStatusName=' +
      Parameters.checkStatusName +
      '&id=' +
      Parameters.id,
  )
}

// /ehsUpms/getTopLevelOrgCode
// 获取集团id
export const postTopLevelOrgCodeAPI = (Parameters: any) => {
  return http.post(
    $api.type.hazard + '/ehsUpms/getTopLevelOrgCode?orgCode=' + Parameters.orgCode,
    Parameters,
  )
}

// 获取隐患来源列表
export const posthazardSourceListAPI = (Parameters: any) => {
  return http.post($api.type.hazard + '/hazardSourceConfig/list', Parameters)
}
