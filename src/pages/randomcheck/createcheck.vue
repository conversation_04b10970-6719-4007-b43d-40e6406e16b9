<route lang="json5">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '新增随机检查',
  },
}
</route>

<template>
  <SafetyNavbar title="新增随机检查"></SafetyNavbar>
  <view class="container">
    <wd-form ref="form" :model="model">
      <view style="padding: 4px 0; border-bottom: 0.3px solid #ebebeb">
        <wd-input label="随机检查名称:" label-width="100px" prop="checkName" clearable v-model="model.checkName"
          placeholder="请输入随机检查名称信息" :maxlength="100" show-word-limit required />
      </view>
      <!-- 隐患单位 -->
      <commonUnit :selectvalue="model.unitId" :selecttext="model.unitName" @getGrade="getUnit" :types="'1'">
      </commonUnit>
      <!-- <view style="padding: 4px 0">
        <wd-select-picker filterable label="检查对象" v-model="model.objId" align-right type="radio" :show-confirm="false"
          :columns="unitcolumns" value-key="id" required label-key="unitName"></wd-select-picker>
      </view> -->
      <commonSelectpicker :status="1" :type="1" :orgUnitId="model.objId" :title="'检查负责人'" @send="reception1">
      </commonSelectpicker>
      <commonSelectpicker :status="1" :type="1" :orgUnitId="model.objId" :title="'检查人员'" @send="reception2">
      </commonSelectpicker>
      <view class="fixed-bottom">
        <wd-button type="info" size="large" @click="goBack" block>取消</wd-button>
        <wd-button type="primary" size="large" :loading="loading" @click="handleSubmit" block>提交</wd-button>
      </view>
    </wd-form>
  </view>
</template>

<script lang="ts" setup>
import SafetyNavbar from '../components/safety-navbar.vue'
import commonSelectpicker from '@/components/commonSelect/common-selectpicker.vue'
import { pageForm, ResponsesData } from './type'
import { postAllUnitAPI, posthazardRandomCheckAddAPI } from './featch'
import { useUserStore } from '@/store'
import { debounce } from '@/utils'
import commonUnit from '@/components/commonSelect/common-Unit.vue'
const userStore = useUserStore()
const loading = ref(false)
const userinfo: any = userStore.userInfo ? userStore.userInfo : {}
const model = ref<pageForm>({
  checkName: '',
  checkStatus: '0',
  checkStatusName: '',
  createBy: userinfo.id,
  createTime: '',
  endTime: '',
  id: '',
  objId: '',
  objName: '',
  search: '',
  startTime: '',
  unitId: '',
  unitName: '',
  updateBy: '',
  updateTime: '',
  zhId: '',
  checkUserJson: '',
  commandUserJson: '',
  pageNo: 0,
  pageSize: 0,
  searchCount: 0,
  createUserName: userinfo.userName,
})
const unitcolumns = ref<any>([])

if (userinfo.unitOrgType === '1') {
  getAllUnit()
}
function getAllUnit() {
  model.value.objId = userinfo.unitId
  model.value.unitId = userinfo.unitId
  model.value.unitName = userinfo.unitName
  // postAllUnitAPI({ orgCode: userinfo.unitId, pageSize: -1 })
  //   .then((res: any) => {
  //     unitcolumns.value.push(...res.data.rows)
  //   })
  //   .finally(() => { })
}
// 接收检查负责人数据
function reception1(event) {
  model.value.commandUserJson = JSON.stringify(convertToList(JSON.parse(JSON.stringify(event))))
}
// 接收检查人数据
function reception2(event) {
  model.value.checkUserJson = JSON.stringify(convertToList(JSON.parse(JSON.stringify(event))))
}
function getUnit(event) {
  model.value.objId = event.id
  model.value.unitId = event.id
  model.value.unitName = event.text
}
function convertToList(items) {
  return items.map((item) => ({
    checkUserName: item.userName,
    checkUserId: item.id,
  }))
}

const goBack = () => {
  uni.navigateBack()
}

const handleSubmit = debounce(() => {
  console.log(model.value)
  loading.value = true
  if (!model.value.checkName) {
    loading.value = false
    uni.showToast({
      icon: 'none',
      title: '请填写随机检查名称',
    })
    return
  }
  if (!model.value.objId) {
    loading.value = false
    uni.showToast({
      icon: 'none',
      title: '请选择检查对象',
    })
    return
  }
  if (!model.value.commandUserJson) {
    loading.value = false
    uni.showToast({
      icon: 'none',
      title: '请选择检查负责人',
    })
    return
  }
  if (!model.value.checkUserJson) {
    loading.value = false
    uni.showToast({
      icon: 'none',
      title: '请选择检查人员',
    })
    return
  }
  model.value.search = userinfo.createByName + '|' + model.value.checkName + '|'
  model.value.objName = model.value.unitName
  // // console.log(model.value)
  posthazardRandomCheckAddAPI(model.value)
    .then((res: any) => {
      if (res.code === 'error') {
        loading.value = false
        uni.showToast({
          icon: 'none',
          title: res.message || '请求错误',
        })
        return
      }
      // loading.value = false
      uni.showToast({
        icon: 'none',
        title: '新增成功',
      })
      uni.navigateBack({
        delta: 1,
      })
    })
    .finally(() => {
      // console.log(111)
    })
}, 500)
</script>

<style lang="scss">
// .selectprincipal.selectprincipal {
//   padding-left: 15px;
//   margin: 0;
// }

::v-deep {
  .wd-select-picker__value {
    color: #767676;
    text-align: end;
  }

  .wd-select-picker__cell {
    // border-bottom: 0.3px solid #ebebeb;
  }

  .wd-button.is-block {
    width: 100% !important;
    height: 100% !important;
    border-radius: 0 !important;
  }
}

.fixed-bottom {
  position: fixed;
  bottom: 0;
  display: flex;
  flex-direction: row;
  align-items: center;
  width: 100%;
  height: 3.375rem;
  line-height: 3.375rem;
  color: #fff;
  text-align: center;
  background-color: white;
}
</style>
