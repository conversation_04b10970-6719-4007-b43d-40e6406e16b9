<route lang="json5">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '图例',
  },
}
</route>

<template>
  <SafetyNavbar title="图例"></SafetyNavbar>
  <view class="container-box">
    <view class="infobox_title">
      <view class="fasticon"></view>
      <view>正确图例</view>
    </view>
    <view class="img-list">
      <view class="fileList" v-for="(item, index) in correctFiles" :key="index">
        <wd-img :src="VITE_PREVIEW_BASEURL + item.fileUrl" :enable-preview="true" />
      </view>
    </view>
    <view class="infobox_title">
      <view class="fasticon"></view>
      <view>正确图例</view>
    </view>
    <view class="img-list">
      <view class="fileList" v-for="(item, index) in errorFiles" :key="index">
        <wd-img :src="VITE_PREVIEW_BASEURL + item.fileUrl" :enable-preview="true" />
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { useAppStore, usefileConfigStore } from '@/store'
import SafetyNavbar from '../components/safety-navbar.vue'
// const VITE_PREVIEW_BASEURL = import.meta.env.VITE_PREVIEW_BASEURL
const usefileStore = usefileConfigStore()
const VITE_PREVIEW_BASEURL = ref()
const currentEnvInfo = useAppStore().appEnv
if (!currentEnvInfo.wychApp) {
  VITE_PREVIEW_BASEURL.value = usefileStore.fileUrl.fileUrlPrefixMiniProgram + '/'
} else {
  VITE_PREVIEW_BASEURL.value = usefileStore.fileUrl.fileUrlPrefix + '/'
}

// const VITE_PREVIEW_BASEURL = ref(usefileStore.fileUrl + '/')
// const VITE_PREVIEW_BASEURL = ref('')
// getbaseurl()
// // eslint-disable-next-line camelcase
// function getbaseurl() {
//   const dynamicsUrl = location.origin
//   if (location.protocol === 'https:') {
//     return (VITE_PREVIEW_BASEURL.value = dynamicsUrl + import.meta.env.VITE_PREVIEW_BASEURL_dev)
//   } else {
//     return (VITE_PREVIEW_BASEURL.value = import.meta.env.VITE_PREVIEW_BASEURL)
//   }
// }
const checkAreaFileList = ref([])
const correctFiles = computed(() =>
  checkAreaFileList.value.filter((item) => item.fileCategory === '1'),
)
const errorFiles = computed(() =>
  checkAreaFileList.value.filter((item) => item.fileCategory === '2'),
)
onLoad((params) => {
  if (!params.checkAreaFileList) checkAreaFileList.value = []
  else checkAreaFileList.value = JSON.parse(params.checkAreaFileList)
  // console.log(checkAreaFileList.value)
})
</script>

<style lang="scss">
.infobox_title {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 0.25rem 0 0.9375rem 1.125rem;

  .fasticon {
    width: 0.25rem;
    height: 0.875rem;
    margin-right: 0.1875rem;
    background-color: #597bf7;
    border-radius: 1.125rem;
  }
}

.fileList {
  width: 45%;
  height: 40vw;
  margin: 0.5rem;
  text-align: center;
  border-radius: 0.375rem;
}

.img-list {
  display: flex;
  flex-wrap: wrap;
  padding: 0.25rem 0 0.9375rem 1.125rem;
}

.wd-img {
  width: 100%;
  height: 90%;
}
</style>
