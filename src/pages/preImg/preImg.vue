<route lang="json5">
{
  layout: '',
  style: {
    navigationBarTitleText: '',
  },
}
</route>

<template>
  <view class="w-full">
    <!-- <img :src="paramas" alt=""> -->
    <wd-img @click="closeimg" style="display: block; width: 95%; height: 90vh; margin: auto" :src="imginfo" />
  </view>
</template>
<script setup lang="ts">
const imginfo = ref('')
onLoad((paramas) => {
  // console.log(paramas.url, 'onLoad')
  imginfo.value = paramas.url
  // })
})
function closeimg() {
  uni.navigateBack()
}
</script>
<style lang="scss" scoped>
::v-deep {
  .uni-page-head {
    display: none !important;
  }
}
</style>
