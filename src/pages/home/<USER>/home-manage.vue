<template>
  <view
    v-for="(item, index) in resourceVoList"
    :key="index"
    class="bg-white rounded-2 overflow-hidden mb-4"
  >
    <view class="mx-4 mt-4 mb-5 font-size-4 font-600 color-#323233">
      <span class="inline-block w-0.75 h-3.5 rounded-0.375 mr-2 tipblock"></span>
      <text>{{ item.resName }}</text>
    </view>
    <wd-row>
      <wd-col
        :span="6"
        class="text-center color-#484A4D"
        v-for="e in item.childrens"
        :key="e.id"
        @click="navigateTo(e.resUrl)"
      >
        <view>
          <image class="w-12.25 h-12.25 mb-1.5" :src="imgMap[e.resIcon]" mode="scaleToFill" />
          <view class="font-size-3 mb-4">{{ e.resName }}</view>
        </view>
      </wd-col>
    </wd-row>
  </view>
</template>
<script lang="ts" setup>
import { useUserStore } from '@/store'
import imgMap from '../MenuIcons'

const userStore = useUserStore()

const resourceVoList = ref(null)
setTimeout(function () {
  const userinfo: any = userStore.userInfo ? userStore.userInfo : {}
  resourceVoList.value = userinfo.resourceVoList
}, 500)
function navigateTo(event) {
  let url = `/${event}`
  if (url === '/pages/randomcheck/createhiddendanger') {
    url = `${url}?types=1`
  }
  if (!event) return
  uni.navigateTo({
    url,
  })
}
</script>
<style lang="scss">
.tipblock {
  background: #527cff;
}
</style>
