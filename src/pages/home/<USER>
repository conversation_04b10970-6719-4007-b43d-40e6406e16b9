<!-- 使用 type="home" 属性设置首页，其他页面不需要设置，默认为page；推荐使用json5，更强大，且允许注释 -->
<route lang="json5" type="home">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '隐患排查治理',
  },
}
</route>
<template>
  <SafetyNavbar class="navbar" title="隐患排查治理" transparent></SafetyNavbar>
  <view class="overflow-scroll px-4 full-screen-bg">
    <image class="w-85.75 h-40.25 mb-4" :src="banner" mode="scaleToFill" />
    <view class="overflow-hidden">
      <homeManage></homeManage>
    </view>
  </view>
</template>
<script lang="ts" setup>
import banner from '@/static/pages/images/banner.png'
import homeManage from './components/home-manage.vue'
import SafetyNavbar from '../components/safety-navbar.vue'
</script>
<style lang="scss" scoped>
::v-deep {
  uni-page-wrapper {
    overflow: auto !important;
  }
}

* {
  color: #333;
}

body {
  background: linear-gradient(180deg, #dee8fe 0%, rgba(226, 235, 254, 0.26) 100%);
  // background: linear-gradient(180deg, #dee8fe 0%, rgba(226, 235, 254, 0.26) 100%), #ffffff"
}

.main-title-color {
  color: #d14328;
}
</style>
