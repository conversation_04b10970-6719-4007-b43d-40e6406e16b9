<!--
 * @Author: 悦悦 <EMAIL>
 * @Date: 2024-11-05 18:32:10
 * @LastEditors: fangweiwei <EMAIL>
 * @LastEditTime: 2025-01-14 20:37:07
 * @FilePath: /隐患排查app/src/pages/task/qrcodePage.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<route lang="json5">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '扫码',
  },
}
</route>

<template>
  <!-- <SafetyNavbar title="扫码"></SafetyNavbar> -->
  <view class="w-full flex-1">
    <view class="qrcode-wrapper">
      <view class="close-box" @click="onClose">
        <wd-icon :color="'#fff'" name="close-circle-filled" size="22px"></wd-icon>
      </view>
      <view id="qrcode-scanner" class="qrcode-reader"></view>
    </view>
  </view>
</template>
<script setup lang="ts">
import SafetyNavbar from '../components/safety-navbar.vue'
import { Html5Qrcode } from 'html5-qrcode'
import type { QrcodeSuccessCallback, QrcodeErrorCallback } from 'html5-qrcode'
import { debounce } from '@/utils'
const taskId = ref('')
let html5Qrcode = null
const qrcodeConfig = {
  fps: 10,
  qrbox: {
    width: 280,
    height: 280,
  },
}

const onResult: any = (decodedText, res) => {
  // console.log(decodedText, '=======================decodedText')
  if (decodedText) {
    decodedText = encodeURIComponent(decodedText)
    uni.redirectTo({
      url: `/pages/task/scanCodes?taskId=${taskId.value}&qrCodeValue=${decodedText}`,
    })
  } else {
    uni.navigateBack()
  }
}

const init = debounce(() => {
  // console.log('init-qrcode')
  console.time()
  html5Qrcode = new Html5Qrcode('qrcode-scanner', false)
  // console.log('🚀 ~ getState :', html5Qrcode.getState())
  if (html5Qrcode.getState() > 0) {
    html5Qrcode.start({ facingMode: 'environment' }, qrcodeConfig, onScanSuccess, onScanError)
  } else {
    html5Qrcode.clear()
    uni.navigateBack()
  }
}, 1000)

const onScanSuccess: QrcodeSuccessCallback = (decodedText, res) => {
  html5Qrcode
    .stop()
    .then(() => {
      onResult(decodedText, res)
      html5Qrcode.clear()
    })
    .catch(() => { })
}
const onScanError: QrcodeErrorCallback = (message, error) => { }

const onClose = () => {
  if (html5Qrcode.isScanning) {
    html5Qrcode.stop().finally(() => {
      html5Qrcode.clear()
      uni.navigateBack()
    })
  }
  uni.navigateBack()
}

onLoad((params) => {
  // console.log(params)
  taskId.value = params.taskId
})
onShow(() => {
  // console.log('🚀 ~ onShow ~ init:')
  init()
})
</script>
<style scoped>
.qrcode-wrapper {
  position: relative;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  background-color: #000;
}

.close-box {
  position: absolute;
  top: 80rpx;
  right: 30rpx;
  z-index: 9;
  padding: 8px;
  border-radius: 50%;
}

.qrcode-reader {
  position: absolute;
  top: 50%;
  width: 100%;
  transform: translateY(-50%);
}
</style>
