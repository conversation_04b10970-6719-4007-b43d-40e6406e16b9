import { $api } from '@/api/index'
import { http } from '@/utils/http'
import { pageForm, taskAdd } from './type'

export const getHazardTaskShortInfo = (params: any) => {
  return http.post(
    $api.type.hazard + '/hazardTaskDetail/getHazardTaskShortInfo?taskId=' + params.id,
  )
}

// 任务详情
/** 任务检查列表 */
export const taskInspection = (Parameters: any) => {
  return http.post($api.type.hazard + '/hazardTask/page', { ...Parameters })
}

/** 获取所有的下属单位 */
export const getAllUnit = (Parameters: pageForm) => {
  return http.post($api.type.hazard + '/ehsUpms/getAllUnit', { ...Parameters })
}

// 任务检查列表-按钮操作
export const taskInspectionBtn = (id: any, userId: any) => {
  return http.post($api.type.hazard + '/hazardTask/startTask?taskId=' + id + '&userId=' + userId)
}

// 获取组织机构下级单位（任务检查对象）
export const unitData = (Parameters: any) => {
  return http.post($api.type.hazard + '/ehsUpms/getAllUnit', { ...Parameters })
}

// 获取任务详情下的任务信息
export const taskDetail = (id: any) => {
  return http.post($api.type.hazard + '/hazardTaskDetail/getHazardPlanDetai?taskId=' + id)
}

/** 隐患检查情况 */
// 获取隐患数量
export const taskHiddenNum = (Parameters: any) => {
  return http.post($api.type.hazard + '/hazardRecord/statistics', { ...Parameters })
}

// 获取隐患检查级别
export const taskHiddenLevel = (Parameters: any) => {
  return http.post($api.type.hazard + '/hazardRecord/levelStatistics', { ...Parameters })
}

// 获取安全生产检查表-无需逐一执行/需要逐一执行
export const inspectList = (Parameters: any) => {
  return http.post(
    $api.type.hazard +
    '/hazardTask/getTaskCheckArea?taskId=' +
    Parameters.taskId +
    '&paramKey=' +
    Parameters.paramKey,
  )
}

// 获取安全生产检查表检查数量（需要逐一执行）
export const inspectNeedNum = (Parameters: any) => {
  return http.post(
    $api.type.hazard +
    '/hazardTask/getTaskCheckAreaCount?taskId=' +
    Parameters.taskId +
    '&paramKey=' +
    Parameters.paramKey,
  )
}

/* 获取检查表详情 hazardCheckArea/detail?
checkAreaEssentialFactorId=1838399120060268545&-----检查表id
id=1838398308785405953 -----taskid */
export const inspectDetail = (id: any, factorId: any) => {
  return http.post(
    $api.type.hazard +
    '/hazardCheckArea/detail?id=' +
    id +
    '&checkAreaEssentialFactorId=' +
    factorId,
  )
}
// 检查任务打卡
export const taskAddClockIn = (Parameters: taskAdd) => {
  return http.post($api.type.hazard + '/hazardTask/taskAddClockIn', { ...Parameters })
}

// 检查任务打卡记录查询--登录人最新一条
export const getTaskClockInOne = (Parameters: any) => {
  return http.post(
    $api.type.hazard +
    `/hazardTask/getTaskClockInOne?taskId=${Parameters.taskId}&userId=${Parameters.userId}`,
  )
}

// 获取设备检查项
export const getDevieItem = (Parameters: any) => {
  return http.post(
    $api.type.hazard +
    `/hazardTaskDetail/getDevieItem?taskId=${Parameters.taskId}&taskDeviceId=${Parameters.taskDeviceId}`,
  )
}

// 检查任务查询设备列表（点位检查任务-点位扫码）
export const selectPointDetails = (Parameters: any) => {
  return http.post(
    $api.type.hazard +
    `/hazardTask/getcheckPoint?taskId=${Parameters.taskId}&pointId=${Parameters.pointId}`,
  )
}

// 检查任务 巡查点位-详情（根据扫码的状态去置换id）
export const postDetailByQrCodeValueAPI = (Parameters: any) => {
  return http.post(
    $api.type.hazard +
    `/hazardCheckPoint/detailByQrCodeValue?qrCodeValue=${Parameters.qrCodeValue}`,
  )
}

// 批量 -检查项详情状态变更
export const batchUpdateTaskDeviceContent = (Parameters: any) => {
  return http.post($api.type.hazard + `/hazardTaskDetail/batchUpdateTaskDeviceContent`, Parameters)
}

// 点位批量 -检查项详情状态变更
export const updateTaskPointContentList = (Parameters: any) => {
  return http.post($api.type.hazard + '/hazardTask/updateTaskPointContentList', Parameters)
}

// 每日巡查点位批量 -检查项详情状态变更 新
export const updateDayTaskPointContentList = (Parameters: any) => {
  return http.post($api.type.hazard + '/hazardTask/updateTaskCheckPoint', Parameters)
}

// 每日巡查点位 - 任务删除点位检查项隐患
export const deleteTaskPoint = (Parameters: any) => {
  return http.post(
    $api.type.hazard +
    `/hazardTask/deteteCheckItemHazard?checkContentId=${Parameters.checkContentId}&checkState=${Parameters.checkState}&&pointId=${Parameters.pointId}&taskId=${Parameters.taskId}`,
  )
}

// {
//           taskId: taskId.value,
//           checkInState: '1',
//           id: pointId.value,
//           userld: userInfo.id,
//           userName: userInfo.userName,
//         }
// 更改点位巡检状态
export const updateTaskPointAPI = (Parameters: any) => {
  return http.post(
    $api.type.hazard +
    '/hazardTask/updateTaskPoint?checkInState=' +
    Parameters.checkInState +
    '&taskId=' +
    Parameters.taskId +
    '&checkPointId=' +
    Parameters.checkPointId +
    '&userId=' +
    Parameters.userId +
    '&userName=' +
    Parameters.userName,
    Parameters,
  )
}
// 更改设备巡检状态
export const updateTaskDeviceContentAPI = (Parameters: any) => {
  return http.post(
    $api.type.hazard +
    '/hazardTaskDetail/updateTaskDevice?checkInState=' +
    Parameters.checkInState +
    '&taskId=' +
    Parameters.taskId +
    '&taskDeviceId=' +
    Parameters.taskDeviceId +
    '&userId=' +
    Parameters.userId +
    '&userName=' +
    Parameters.userName,
    Parameters,
  )
}
// /hazardTaskDetail/updateTaskDeviceContent
// /hazardTask/updateTaskPoint
// // 获取检查表详情
// export const inspectDetail = (id: any) => {
//   return http.post('/hazardCheckArea/detail?id=' + id)
// }

// 拍照打卡记录
export const checkRecordList = (Parameters: any) => {
  return http.post(
    $api.type.hazard +
    '/hazardTask/getTaskClockInList?taskId=' +
    Parameters.taskId +
    '&userId=' +
    Parameters.userId +
    '&userName=' +
    Parameters.userName,
  )
}

// 检查任务更新检查项适用状态（区域检查任务）
export const updateCheckStatus = (id: any, hazardState: any) => {
  return http.post(
    $api.type.hazard + '/hazardTask/updateTaskCheckArea?id=' + id + '&hazardState=' + hazardState,
  )
}

// 更新单个检查项状态
export const updateSingleCheckItemStatus = (Parameters: any) => {
  return http.post(
    $api.type.hazard +
    '/hazardTask/updateCheckItemStatus?id=' +
    Parameters.id +
    '&checkState=' +
    Parameters.checkState,
    Parameters,
  )
}

/* 统计设备情况====hazardTaskDetail/getDevieStatistics?taskId=bf5f888cb909440cbf6e7d22e85c23d7 */
export const PostDevieStatisticsAPI = (id: any) => {
  return http.post($api.type.hazard + '/hazardTaskDetail/getDevieStatistics?taskId=' + id)
}

/* */
export const getPointStatisticsAPI = (id: any) => {
  return http.post($api.type.hazard + '/hazardTaskDetail/getPointStatistics?taskId=' + id)
}

/* 1获取楼栋 hazardTaskDetail/getBuild?taskId=bf5f888cb909440cbf6e7d22e85c23d7 */
export const PostgetBuildAPI = (id: any) => {
  return http.post($api.type.hazard + '/hazardTaskDetail/getBuild?taskId=' + id)
}

export const selectPointListGroupAPI = (id: any) => {
  return http.post($api.type.hazard + '/hazardTask/selectPointListGroup?taskId=' + id)
}

export const selectBuildingListAPI = (id: any, checkInState: any, checkState: any) => {
  return http.post(
    $api.type.hazard +
    `/hazardTask/selectBuildingList?taskId=${id}&checkInState=${checkInState}&checkState=${checkState}`,
  )
}

export const selectFloorListAPI = (
  taskId: any,
  buildingId: any,
  checkInState: any,
  checkState: any,
) => {
  return http.post(
    $api.type.hazard +
    `/hazardTask/selectFloorList?taskId=${taskId}&buildingId=${buildingId}&checkInState=${checkInState}&checkState=${checkState}`,
  )
}

export const selectPointListAPI = (
  taskId: any,
  buildingId: any,
  floorId: any,
  checkInState: any,
  checkState: any,
) => {
  return http.post(
    $api.type.hazard +
    `/hazardTask/selectPointList?taskId=${taskId}&buildingId=${buildingId}&floorId=${floorId}&checkInState=${checkInState}&checkState=${checkState}`,
  )
}

/* 2获取楼层 hazardTaskDetail/getFloor?buildId=1&taskId=1&unitId=1 */
export const PostgetFloorAPI = (buildId: any, taskId: any, unitId: any) => {
  return http.post(
    $api.type.hazard +
    `/hazardTaskDetail/getFloor?buildId=${buildId}&taskId=${taskId}&unitId=${unitId}`,
  )
}

/* 3巡检项-某楼层设备 hazardTaskDetail/getDevice?buildId=1&floorId=1&taskId=1&unitId=1 */
export const PostgetDeviceAPI = (buildId: any, taskId: any, unitId: any, floorId: any) => {
  return http.post(
    $api.type.hazard +
    `/hazardTaskDetail/getDevice?buildId=${buildId}&taskId=${taskId}&unitId=${unitId}&floorId=${floorId}`,
  )
}
/* 4 获取设备检查项 hazardTaskDetail/getDevieItem?taskDeviceId=&taskId= */
export const PostgetDevieItemAPI = (taskDeviceId: any, taskId: any) => {
  return http.post(
    $api.type.hazard +
    `/hazardTaskDetail/getDevieItem?taskDeviceId=${taskDeviceId}&taskId=${taskId}`,
  )
}

/*  完成检查  /hazardTask/completionTask */
export const PostcompletionTaskAPI = (taskId: any, userId: any) => {
  return http.post(
    $api.type.hazard + `/hazardTask/completionTask?taskId=${taskId}&userId=${userId}`,
  )
}

/*  更改任务状态 /hazardTask/startTask */
export const PoststartTaskAPI = (taskId: any, userId: any) => {
  return http.post($api.type.hazard + `/hazardTask/startTask?taskId=${taskId}&userId=${userId}`)
}
// /* 5 /hazardTaskDetail/getListDevieCheckItem */
export const PostgetListDevieCheckItemAPI = (taskId: any, checkRange: any) => {
  return http.post(
    $api.type.hazard +
    `/hazardTaskDetail/getListDevieCheckItem?taskId=${taskId}&checkRange=${checkRange}`,
  )
}
// 点位默认检查项配置 hazardCheckDefaultConfig/listPage
export const PosthazardCheckDefaultConfigpointAPI = (Parameters: any) => {
  return http.post($api.type.hazard + `/hazardCheckDefaultConfig/listPage`, {
    ...Parameters,
  })
}

/* 隐患清单  /hazardRecord/pageEvent */
export const postpageEventAPI = (Parameters: any) => {
  return http.post($api.type.hazard + '/hazardRecord/pageEvent', {
    ...Parameters,
  })
}
/* 任务隐患清单  /hazardRecord/pageEvent */
export const hazardPlanTaskEventAPI = (Parameters: any) => {
  return http.post($api.type.hazard + '/hazardPlanTaskEvent/pageEvent', {
    ...Parameters,
  })
}
/* post 隐患列表 //hazardMeger/pageEvent */
export const posthazardMegerpageEventAPI = (Parameters: any) => {
  return http.post(
    $api.type.hazard +
    '/hazardMeger/pageEvent?businessId=' +
    Parameters.businessId +
    '&flag=' +
    Parameters.flag,
    { ...Parameters },
  )
}

// 获取检查类型
// export const getCheckTypeDataList = (Parameters: any) => {
//   return http.post(
//     '/hazardPlanType/getPlanTypeList?planTypeStatus=' +
//       Parameters.planTypeStatus +
//       '&unitId=' +
//       Parameters.unitId,
//   )
// }
export const getCheckTypeDataList = (Parameters: any) => {
  return http.post(
    $api.type.hazard +
    '/hazardPlanType/getPlanTypeList?planTypeStatus=' +
    Parameters.planTypeStatus +
    '&unitId=' +
    Parameters.unitId,
    {
      ...Parameters,
    },
  )
}
/* 校验用户是否在打卡范围内 lat1 检查对象的纬度, lat2 用户当前的纬度, lon1 检查对象的经度, lon2 用户当前的经度, range 打卡距离 */
export const hazardPunchInRange = (Parameters: any) => {
  return http.post(
    $api.type.hazard +
    '/hazardTaskDetail/punchInRange?lat1=' +
    Parameters.lat1 +
    '&lat2=' +
    Parameters.lat2 +
    '&lon1=' +
    Parameters.lon1 +
    '&lon2=' +
    Parameters.lon2 +
    '&range=' +
    Parameters.range,
  )
}

export const postGetAreaListByCheckTableIdAPI = (Parameters: any) => {
  return http.post($api.type.hazard + '/hazardCheckArea/getAreaListByCheckTableId', {
    ...Parameters,
  })
}

// weather/getLocationName?lat=39.984154&lng=116.307490
export const getLocationName = (Parameters: any) => {
  return http.post(
    $api.type.hazard + '/weather/getLocationName?lat=' + Parameters.lat + '&lng=' + Parameters.lng,
  )
}

// 获取相关方/承租方单位列表
export const getUserTaskUnitList = (Parameters: any) => {
  return http.post($api.type.hazard + '/hazardTask/getUserTaskUnitList?userId=' + Parameters.userId)
}
// export const Getlocation = () => {
//   return http.get(
//     'https://apis.map.qq.com/ws/geocoder/v1/?location=39.984154,116.307490&key=OB4BZ-D4W3U-B7VVO-4PJWW-6TKDJ-WPB77',
//   )
// }

// ehs-clnt-hazard-service/ehsUpms/getOrgTree
export const getOrgTree = (Parameters: any) => {
  return http.post(
    $api.type.hazard +
    '/ehsUpms/getOrgTree?orgCode=' +
    Parameters.orgCode +
    '&unitStatus=' +
    Parameters.unitStatus,
  )
}
