import { watch } from 'vue'
import { useAppReceiveStore } from '@/store'

export class wychScan {
  static receiveStore = useAppReceiveStore()

  static openSacn = async (taskid) => {
    try {
      const { data } = await window.waltz.call({
        // 或者通过 window.waltz.call
        module: 'WZScanner', // 模块名，对应下表的Module
        handlerName: 'scan', // 方法名，对应下表的HandlerName
        data: {
          canOpenAlbum: false,
        }, // 方法入参，json格式
      })
      // console.log('>>>>', data)

      if (+data.code === 10000) {
        // 成功回调的业务代码
        const _body = data.body // 接口返回的业务实参对象
        this.scanCallBack(_body.QRCode, taskid)
      }
    } catch (error) {
      uni.showToast({
        title: '二维码失效',
        icon: 'none',
      })
    }
  }

  static scanCallBack = (data, taskid) => {
    // console.log(data)
    //  && (data.includes('taskDeviceId') || data.includes('keyPartId'))
    if (data) {
      data = encodeURIComponent(data)
      uni.navigateTo({
        url: `/pages/task/scanCodes?taskId=${taskid}&qrCodeValue=${data}`,
      })
      // const list = data.split('=')
      // if (list[2] === '1') {
      //   uni.redirectTo({
      //     url: `/pages/keySite/detail/index?redirect=true&id=${list[1]}`,
      //   })
      // } else {
      //   uni.redirectTo({
      //     url: '/pages/deviceMgr/detail/index?redirect=true&id=' + list[1],
      //   })
      // }
    } else {
      uni.showToast({
        title: '无效二维码',
        icon: 'none',
      })
    }
  }
}
