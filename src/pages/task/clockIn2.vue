<route lang="json5">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '现场打卡',
  },
}
</route>
<template>
  <SafetyNavbar title="现场打卡"></SafetyNavbar>
  <view v-if="showMap">地图正在加载中...</view>
  <tlbs-map :style="{ height: 'calc(100vh - 420px)' }" ref="map" api-key="OB4BZ-D4W3U-B7VVO-4PJWW-6TKDJ-WPB77"
    :center="center" :zoom="12">
    <tlbs-multi-circle :geometries="geometries" :styles="{
      color: 'rgba(41,91,255,0.16)',
      showBorder: true,
      borderColor: 'rgba(41,91,255,1)',
      borderWidth: 2,
    }" :options="{ zIndex: 1 }" />
    <tlbs-dom-overlay :position="nowposition">
      <div style="padding: 10px">
        <img style="width: 20px; height: 20px" :src="markBlueSrc" />
      </div>
    </tlbs-dom-overlay>
    <tlbs-dom-overlay :position="unitposition">
      <div style="padding: 10px">
        <img style="width: 20px; height: 20px" :src="markRedSrc" />
      </div>
    </tlbs-dom-overlay>
  </tlbs-map>

  <view class="button-placeholder">
    <view class="location">
      <view class="location-title">单位地址</view>
      <view class="location-info">{{ unitaddress || '--' }}</view>
    </view>
    <view class="location">
      <view class="location-title">当前位置</view>
      <view class="location-info">{{ address || '--' }}附近</view>
    </view>
    <view class="location">
      <view class="location-title">当前范围</view>
      <view class="location-info _outer" v-if="punchType">不在打卡范围内</view>
      <view class="location-info _inner" v-else>在打卡范围内</view>
    </view>
    <button class="photo-clock" @click="getImage" :style="{ background: punchType ? '#909399' : 'rgb(45, 80, 242)' }">
      拍照打卡
    </button>
  </view>
</template>
<script lang="ts" setup>
import markBlueSrc from '@/static/pages/images/_mark_blue.png'
import markRedSrc from '@/static/pages/images/_mark_red.png'
import {
  getTaskClockInOne,
  taskAddClockIn,
  hazardPunchInRange,
  Getlocation,
  getLocationName,
} from './featch'
import SafetyNavbar from '../components/safety-navbar.vue'
import { useAppStore, usefileConfigStore, useUserStore } from '@/store'
import { getCurrentPosition } from '@/utils'
// import { wychLocation } from './loactionService'
import axios from 'axios'
import { compressFile } from '@/utils/dictData'
import { getWatermarkBizData } from '@/components/upload/featch'
const VITE_UPLOAD_IMAGE_BASEURL = import.meta.env.VITE_UPLOAD_WATERMARK_BASEURL

const VITE_SERVER_BASEURL = import.meta.env.VITE_SERVER_BASEURL
const VITE_APP_PROXY_PREFIX = import.meta.env.VITE_APP_PROXY_PREFIX

// const currentEnvInfo = useAppStore().appEnv
/* 获取登录人信息 */
const userStore = useUserStore()
const userinfo: any = userStore.userInfo ? userStore.userInfo : {}

const saveParams = reactive({
  taskId: '',
  userId: userinfo.id,
  userName: userinfo.userName,
})
const rangeParams = reactive({
  lat1: null,
  lat2: 0,
  lon1: null,
  lon2: 0,
  range: 0,
})
const showMap = ref<boolean>(true)
const punchType = ref(true) // 是否在范围内 true不在范围内 false在范围内
// const showloading = ref<boolean>(false)
const address = ref<string>('')
const unitaddress = ref<string>('')

const position = ref({ lat: null, lng: null })

const map = ref(null)
const center = ref()
const geometries = ref()

// 当前定位标识
const nowposition = ref()
// 打卡地点
const unitposition = ref()

onLoad(async (params) => {
  // uni.setStorageSync('face-photo', '')
  saveParams.taskId = params.taskId

  if (params.lat1 || params.lon1) {
    rangeParams.lat1 = Number(params.lat1)
    rangeParams.lon1 = Number(params.lon1)
  }
  rangeParams.range = Number(params.range)
  // 单位地址经纬度转换
  const gisPoint = new GISShare.SMap.Geometry.Point(rangeParams.lon1, rangeParams.lat1)
  // 由BD09MC转换为百度经纬度坐标BD09LL
  // GISShare.SMap.SpatialReference.GeographicCoordinateSystemStyle.eGCS_WGS_1984_BD09LL,
  GISShare.SMap.Fitting.FittingHelper.Fit(
    gisPoint,
    GISShare.SMap.SpatialReference.ProjectedCoordinateSystemStyle
      .eWGS_1984_Web_Mercator_Auxiliary_Sphere_BD09MC,
    GISShare.SMap.SpatialReference.GeographicCoordinateSystemStyle.eGCS_WGS_1984_GCJ02,
  )
  const dX = gisPoint.getX()
  const dY = gisPoint.getY()
  rangeParams.lon1 = dX
  rangeParams.lat1 = dY
  const parmas = { lat: rangeParams.lat1, lng: rangeParams.lon1 }
  // 获取单位地址
  getLocationName(parmas).then((res: any) => {
    unitaddress.value = res.data
  })
  // 获取当前位置，从长航获取
  try {
    const { data } = await window.waltz.call({
      // 或者通过 window.waltz.call
      module: 'WZLocation', // 模块名，对应下表的Module
      handlerName: 'getLoactionInfo', // 方法名，对应下表的HandlerName
    })
    if (+data.code === 10000) {
      // 成功回调的业务代码
      const resultObject = data.body // 接口返回的业务实参对象
      console.log(resultObject, '获取的经纬度')
      if (resultObject) {
        rangeParams.lat2 = resultObject.latitude
        rangeParams.lon2 = resultObject.longitude

        console.log(position.value, '赋值给position')
        position.value.lat = resultObject.latitude
        position.value.lng = resultObject.longitude
        // console.log('获取当前位置信息===')
        getLocationName(position.value).then((res: any) => {
          address.value = res.data
        })
      }
    }
  } catch (error) {
    uni.showToast({
      title: 'GPS定位信号弱',
      icon: 'none',
    })
  }
  // rangeParams.lat2 = 31.762906142291225
  // rangeParams.lon2 = 117.27049296887573

  // position.value.lat = 31.762906142291225
  // position.value.lng = 117.22049296887573
  // console.log('获取当前位置信息===')
  getLocationName(position.value).then((res: any) => {
    address.value = res.data
  })
  setTimeout(function () {
    getMap(position.value, rangeParams)
    hazardPunchRange()
    getWatermark(position.value)
  }, 100)
})

// onShow(() => {
//   const face = uni.getStorageSync('face-photo')
//   // console.log(face)
//   if (face) {
//     uni.showLoading({
//       title: '上传中...',
//       icon: 'none',
//     })
//     const params = {
//       clockInAddress: address.value,
//       filePath: face[0].fileUrl,
//       taskId: saveParams.taskId,
//       userId: saveParams.userId,
//       userName: saveParams.userName,
//     }
//     // console.log(params, '=======================params')
//     setTimeout(() => {
//       taskAddClockInAPI(params)
//     }, 100)
//   }
// })

// 地图显示

function getMap(position, rangeParams) {
  center.value = { lat: position.lat, lng: position.lng }
  // 打卡地点画圈
  geometries.value = [
    {
      styleId: 'circle',
      radius: rangeParams.range,
      center: { lat: rangeParams.lat1, lng: rangeParams.lon1 },
    },
  ]
  // 当前定位标识
  nowposition.value = { lat: position.lat, lng: position.lng }
  // 单位定位标识
  unitposition.value = { lat: rangeParams.lat1, lng: rangeParams.lon1 }
}

// 校验用户是否在打卡范围内
const hazardPunchRange = async () => {
  try {
    const res = await hazardPunchInRange(rangeParams)
    // 返回true则在范围内允许打卡 否则不允许打卡
    if (res.data === true) {
      punchType.value = false
    }
    console.log(
      '校验用户是否在打卡范围内 res ---punchType.value：false代表在范围内- = ',
      res,
      punchType.value,
    )
    showMap.value = false
  } catch (error) {
    // console.log('校验用户是否在打卡范围内接口报错', error)
    showMap.value = false
  }
}
// 水印信息
const fileInfo = ref({
  address: 'string',
  dateTime: 'string',
  logoImageUrl: 'string',
  projectName: 'string',
  sourceImageUrl: 'string',
  unitName: 'string',
  userName: 'string',
  waterTemplateName: 'string',
  weather: 'string',
  workContent: 'string',
})
// 获取水印信息
const getWatermark = async (position) => {
  const info = {
    coordType: 'gcj02ll',
    longitude: position.lng,
    latitude: position.lat,
    orgCode: userinfo.orgCode,
  }
  const res: any = await getWatermarkBizData(
    info.coordType,
    info.longitude,
    info.latitude,
    info.orgCode,
  )
  if (res.code === 'success') {
    fileInfo.value = res.data
  }
}
// 图片信息
const compressFileList = ref()
// 打卡按钮--去拍照
const getImage = () => {
  if (showMap.value || punchType.value) {
    return false
  }
  // uni.navigateTo({
  //   url: `/pages/demoCameraApp/index`,
  // })
  uni.chooseImage({
    count: 1,
    sizeType: ['compressed'],
    sourceType: ['camera'],
    async success(res: any) {
      console.log(res)
      uni.showLoading({
        title: '上传中...',
        icon: 'none',
        mask: true,
      })
      // for (let i = 0; i < res.tempFiles.length; i++) {
      const formData = new FormData()
      const size = res.tempFiles[0].size / 1024 / 1024 // 计算文件大小（单位：M）
      const extension = res.tempFiles[0].type
      const arr = ['image/jpeg', 'image/png', 'image/webp', 'image/jpg'] // 允许的图片格式列表\\
      if (size > 10) {
        uni.showToast({ title: '图片大小不能超过10M', icon: 'none' })
      } else if (extension && !arr.includes(extension)) {
        uni.showToast({ title: '图片格式仅支持JPG、PNG、JPEG', icon: 'none' })
      } else {
        compressFileList.value = await compressFile(res.tempFiles[0])
        formData.append('address', fileInfo.value.address)
        formData.append('dateTime', fileInfo.value.dateTime)
        formData.append('logoImageUrl', fileInfo.value.logoImageUrl)
        formData.append('watermarkProjectName', fileInfo.value.projectName)
        formData.append('sourceImageUrl', fileInfo.value.sourceImageUrl)
        formData.append('unitName', fileInfo.value.unitName)
        formData.append('userName', fileInfo.value.userName)
        formData.append('waterTemplateName', fileInfo.value.waterTemplateName)
        formData.append('weather', fileInfo.value.weather)
        formData.append('workContent', '隐患排查')
        console.log(compressFileList.value, '压缩后的文件')
        formData.append('files', compressFileList.value)
        // 调用上传图片方法---图片上传完成之后调用上传打卡信息接口
        uploadFile(formData)
        // }
      }
    },
    fail: function (chooseImageError) {
      uni.showToast({
        title: chooseImageError,
      })
    },
  })
}

// 上传文件
async function uploadFile(formData) {
  const apiClient = axios.create({
    baseURL: VITE_SERVER_BASEURL, // 基础URL
    timeout: 60000,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  })
  apiClient
    .post(VITE_APP_PROXY_PREFIX + 'ehs-clnt-hazard-service' + VITE_UPLOAD_IMAGE_BASEURL, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
        Accept: '*/*',
      },
    })
    .then((response) => {
      console.log('文件上传成功:', response.data)
      if (response.data.code === 'success') {
        const upfiles = response.data
        const params = {
          clockInAddress: address.value,
          filePath: upfiles.data[0].fileUrl,
          taskId: saveParams.taskId,
          userId: saveParams.userId,
          userName: saveParams.userName,
        }
        // 调用上传打卡信息接口
        taskAddClockInAPI(params)
      } else {
        uni.hideLoading()
        uni.showToast({
          icon: 'none',
          title: '上传失败',
        })
      }
    })
    .catch((error) => {
      uni.hideLoading()
      uni.showToast({
        icon: 'none',
        title: error.message,
      })
    })
    .finally(() => {
      // uni.hideLoading()
    })
}
// 打卡
const taskAddClockInAPI = (params) => {
  // showloading.value = true
  taskAddClockIn(params)
    .then((res) => {
      // console.log('res', res)
      // 打卡成功之后在重新请求
      // showloading.value = false
      uni.hideLoading()
      uni.showToast({
        icon: 'none',
        title: '上传成功',
      })
      uni.setStorageSync('face-photo', '')
      // 缓存中存个变量 返回上一个页面时 通过这个变量判断是否打卡
      uni.setStorageSync('isClock', { isClock: true })
      setTimeout(() => {
        uni.navigateBack({
          delta: 1, // 默认值为1，表示返回上一页面
        })
      }, 2000)
      // getTaskClockInOneAPI(saveParams)
    })
    .catch(() => {
      // showloading.value = false
      uni.hideLoading()
      uni.showToast({
        icon: 'none',
        title: '上传失败',
      })
    })
    .finally(() => {
      uni.hideLoading()
      // showloading.value = false
    })
}
</script>
<style lang="scss" scoped>
body {
  background-color: #f5f5f5 !important;
}

.button-placeholder {
  position: fixed;
  bottom: 0px;
  z-index: 999;
  box-sizing: border-box;
  width: 100vw;
  padding: 10px 0px 20px;
  background: #fff;
  border-radius: 10px 10px 0px 0px;
}

.location {
  box-sizing: border-box;
  width: 95vw;
  padding: 10px 20px;
  margin: 0px auto;
  font-size: 14px;
  color: rgb(20, 20, 20);
  background-color: #fff;
  border-radius: 10px;

  .location-title {
    margin-bottom: 10px;
    font-weight: 700;
    color: #000000;
  }

  .location-info {
    color: rgb(150, 150, 150);
  }

  ._outer {
    color: #e80000;
  }

  ._inner {
    color: #1ae92c;
  }

  .location-item {
    margin-bottom: 10px;
    color: #333333;

    .location-title {
      margin-right: 10px;
      font-weight: 100;
      color: rgb(120, 120, 120);
    }
  }

  .location-photo {
    display: flex;
    align-items: flex-start;

    .location-title {
      margin-right: 10px;
      font-weight: 100;
      color: rgb(120, 120, 120);
    }
  }
}

.photo-clock {
  width: 96vw;
  margin-top: 20px;
  color: #fff;
  background-color: rgb(45, 80, 242);
}
</style>
