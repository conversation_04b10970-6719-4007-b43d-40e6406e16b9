<route lang="json5">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '扫码检查',
  },
}
</route>
<template>
  <SafetyNavbar :title="info.qrCodeName"></SafetyNavbar>
  <view class="container" v-if="!loading">
    <view
      v-if="isinternal"
      style="display: flex; flex-direction: column; align-items: center; text-align: center"
    >
      <img
        style="width: 62%"
        src="../../static/pages/images/file-read-33677.png"
        alt=""
        srcset=""
      />
      <div style="width: 80%; margin: auto">未能识别当前二维码</div>
    </view>
    <scroll-view v-else class="scroll-content" scroll-y="true">
      <view
        v-if="list.length === 0"
        style="display: flex; flex-direction: column; align-items: center; text-align: center"
      >
        <img
          style="width: 62%"
          src="../../static/pages/images/file-read-33677.png"
          alt=""
          srcset=""
        />
        <div style="width: 80%; margin: auto">
          您正在查看的点位与当前任务不匹配哦，请检查并前往正确的任务点位
        </div>
      </view>
      <view v-else class="list-item" v-for="(item, index) in list" :key="index">
        <view class="list-item-context">
          {{ item.checkContent }}
        </view>
        <view class="list-item-button">
          <label class="radio">
            <wd-icon
              v-if="item.checkState === '1'"
              name="check-circle-filled"
              size="26px"
              style="color: #94c86c"
              @click="change(item)"
            ></wd-icon>
            <wd-icon v-else name="check-circle" size="26px" @click="change(item)"></wd-icon>
          </label>
          <label class="radio" v-if="!item.checkState || item.checkState === '1'">
            <wd-icon name="close-circle" size="26px" @click="changeBad(item)"></wd-icon>
          </label>
          <wd-icon
            v-if="item.checkState === '2'"
            style="color: #ff9000"
            name="close-circle-filled"
            size="26px"
            @click="goForward"
          ></wd-icon>
        </view>
      </view>
    </scroll-view>
    <view v-if="list.length > 0" class="list-button">
      <view class="list-button-cancel" @click="cancel">取消</view>
      <view class="list-button-sure" @click="submitHandle">确定</view>
    </view>
  </view>
</template>
<script lang="ts" setup>
import { useAppStore, useUserStore } from '@/store'
import {
  batchUpdateTaskDeviceContent,
  getDevieItem,
  selectPointDetails,
  updateTaskPointContentList,
  taskDetail,
  updateTaskPointAPI,
  postDetailByQrCodeValueAPI,
  updateTaskDeviceContentAPI,
} from '@/pages/task/featch'
import { check } from '@/pages/task/type'
import SafetyNavbar from '@/pages/components/safety-navbar.vue'
const { appEnv, appInfo } = useAppStore()
const userStore = useUserStore()
const userInfo: any = userStore.userInfo ? userStore.userInfo : {}
const type = ref<string>('')
const taskId = ref<string>('')
const pointId = ref<string>('')
const isinternal = ref<boolean>(false)
const info = ref<any>({})

const loading = ref(true)

onLoad((params: any) => {
  uni.showLoading({
    title: '加载中...',
    icon: 'none',
    mask: true,
  })
  // console.log('获取参数', params)
  // params.qrCodeValue = 'pointId=07001000001'
  if (
    params.qrCodeValue.includes('pointId') ||
    params.qrCodeValue.includes('keyPartId') ||
    params.qrCodeValue.includes('taskDeviceId')
  ) {
    params.qrCodeValue = encodeURIComponent(params.qrCodeValue)
    // params.taskId = '264a10b5430c4bdbb234c314f39be09c'
    taskId.value = params.taskId
    // 根据扫码出来的内容去置换pointId从而更改检查状态
    getDetailByQrCodeValue(params)
  } else {
    console.log('非园区内二维码')
    uni.hideLoading()
    isinternal.value = true
    info.value.qrCodeName = '点位详情'
    loading.value = false
  }
})

// 根据扫码出来的内容去置换pointId从而更改检查状态
function getDetailByQrCodeValue(params: any) {
  postDetailByQrCodeValueAPI(params)
    .then((res: any) => {
      if (res.data.code === 'error') {
        list.value = []
      } else {
        info.value = res.data
        pointId.value = res.data.pointId
        // 获取点位信息
        getPointList()
      }
    })
    .catch(() => {
      // console.log(err, '这里是扫码失败')
      list.value = []
      uni.hideLoading()
      loading.value = false
    })
}

const list = ref<Array<check>>([])
const change = (e) => {
  const index = list.value.findIndex((item) => item.checkContentId === e.checkContentId)
  list.value[index].checkState = '1'
}
const changeBad = (e) => {
  const index = list.value.findIndex((item) => item.checkContentId === e.checkContentId)
  list.value[index].checkState = '2'
  goForward(e)
}

const goForward = (e) => {
  uni.navigateTo({
    url: `/pages/randomcheck/createhiddendanger?checkContentId=${e.checkContentId}&taskId=${taskId.value}&types=0`,
  })
}
function cancel() {
  uni.navigateBack()
}
// const cancel = () => {
//   uni.navigateBack({ delta: 1 })
//   // const data = list.value
//   // for (let i = 0; i < data.length; i++) {
//   //   if (!data[i].checkState) {
//   //     uni.showModal({
//   //       title: '提示',
//   //       content: '还有未完成的检查项,退出将不会保存现有操作,是否要退出?',
//   //       success: function (res) {
//   //         if (res.confirm) {
//   //           uni.navigateBack({ delta: 2 })
//   //         } else if (res.cancel) {
//   //           // 在这里执行取消后的操作
//   //         }
//   //       },
//   //     })
//   //   } else {
//   //     submitHandle()
//   //   }
//   // }
// }

const submitHandle = () => {
  const data = list.value
  // if (type.value === 'taskDeviceId') {
  //   const value = data.map((item: any) => {
  //     return {
  //       id: item.id,
  //       checkState: item.checkState,
  //     }
  //   })
  //   // console.log(value, '======设备检查=====')
  //   for (let i = 0; i < value.length; i++) {
  //     if (!value[i].checkState) {
  //       uni.showToast({
  //         icon: 'none',
  //         title: '还有未完成的检查项',
  //       })
  //       return
  //     }
  //   }
  //   batchUpdateTaskDeviceContent(value).then((res) => {
  //     if ((res.code as any) === 'success') {
  //       // 更改设备巡检状态
  //       updateTaskDeviceContentAPI({
  //         taskId: taskId.value,
  //         checkInState: '1',
  //         taskDeviceId: taskDeviceId.value,
  //         userId: userInfo.id,
  //         userName: userInfo.userName,
  //       }).then((res: any) => {
  //         if (res.code === 'success') {
  //           uni.showToast({
  //             icon: 'none',
  //             title: '检查成功',
  //           })
  //           setTimeout(() => {
  //             cancel()
  //           }, 2000)
  //         }
  //       })
  //     }
  //   })
  // } else {
  // console.log(data, '====点位检查====')
  for (let i = 0; i < data.length; i++) {
    if (!data[i].checkState) {
      uni.showToast({
        icon: 'none',
        title: '还有未完成的检查项',
      })
      return
    }
  }
  updateTaskPointContentList(data).then((res) => {
    if ((res.code as any) === 'success') {
      // 更改点位巡检状态
      updateTaskPointAPI({
        taskId: taskId.value,
        checkInState: '1',
        checkPointId: pointId.value,
        userId: userInfo.id,
        userName: userInfo.userName,
      }).then((res: any) => {
        if (res.code === 'success') {
          uni.showToast({
            icon: 'none',
            title: '检查成功',
          })
          cancel()
          // setTimeout(() => {
          //   uni.navigateBack({ delta: 2 })
          // }, 1000)
        }
      })
    }
  })
  // }
}
// const getList = (params) => {
//   getDevieItem(params).then((res: any) => {
//     list.value = res.data.map((item) => {
//       return {
//         ...item,
//       }
//     })
//   })
// }
const getPointList = () => {
  selectPointDetails({
    taskId: taskId.value,
    pointId: pointId.value,
  })
    .then((res: any) => {
      // console.log(res, '====点位详情====')
      uni.hideLoading()
      list.value = res.data.map((item: any) => {
        return { ...item }
      })
    })
    .catch(() => {
      uni.hideLoading()
    })
    .finally(() => (loading.value = false))
}
</script>
<style lang="scss" scoped>
::v-deep {
  .wd-navbar__left--hover,
  .wd-navbar__right--hover {
    background: none !important;
  }

  .wd-icon-arrow-left {
    color: white;
  }

  .wd-navbar__title {
    font-size: 1.25rem;
    font-weight: 500;
    color: white;
  }
}

.box {
  // width: 100%;
  // height: 100%;
  // height: calc(100vh - 80px);
  // padding-top: 80px;
  // overflow: auto;
}

.list-head {
  display: flex;
  align-items: flex-end;
  width: 100%;
  height: 88px;
  background-image: url('@/static/safety/image/work_bg.png');
  background-size: 100%, 100%;

  .list-head-title {
    width: 100%;
    font-size: 20px;
    line-height: 44px;
    color: #fff;
    text-align: center;
  }
}

.list-item {
  display: flex;
  justify-content: space-between;
  width: 96vw;
  margin: 15px auto;

  .list-item-context {
    width: calc(100% - 100px);
    font-size: 14px;
    color: rgb(75, 75, 75);
  }

  .list-item-button {
    display: flex;
    align-items: center;
    justify-content: space-around;
    width: 80px;
  }
}

// .list-placeholder {
//   height: 80px;
// }

// .scroll-content {
//   height: calc(100vh - 80px);
// }
.container {
  height: 100%;
}

.scroll-content {
  padding-bottom: 5rem;
}

.list-button {
  position: fixed;
  bottom: 0px;
  display: flex;
  align-items: center;
  justify-content: space-around;
  width: 100%;
  height: 80px;
  background-color: #fff;
  border-top: 1px solid #f2f2f2;

  .list-button-cancel {
    display: flex;
    align-items: center;
    justify-content: space-around;
    width: 120px;
    height: 40px;
    color: #666;
    border: 1px solid #666;
    border-radius: 20px;
  }

  .list-button-sure {
    display: flex;
    align-items: center;
    justify-content: space-around;
    width: 120px;
    height: 40px;
    color: #fff;
    background-color: #3a86f6;
    border-radius: 18px;
  }
}
</style>
