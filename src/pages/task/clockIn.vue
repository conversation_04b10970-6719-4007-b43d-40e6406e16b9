<route lang="json5">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '现场打卡',
  },
}
</route>
<template>
  <SafetyNavbar title="现场打卡"></SafetyNavbar>
  <view v-if="showMap">地图正在加载中...</view>
  <!-- <view id="container" style="width: 100vw"
    :style="{ height: showInfo ? 'calc(100vh - 400px)' : 'calc(100vh - 250px)' }"></view> -->
  <view id="container" style="width: 100vw" :style="{ height: 'calc(100vh - 250px)' }"></view>
  <view class="button-placeholder">
    <view class="location" v-if="unitaddress">
      <view class="location-title">单位地址</view>
      <view class="location-info">{{ unitaddress }}</view>
    </view>
    <view class="location" v-if="address">
      <view class="location-title">当前位置</view>
      <view class="location-info">{{ address }}附近</view>
    </view>
    <view class="location" v-if="address">
      <view class="location-title">当前范围</view>
      <view class="location-info _outer" v-if="punchType">不在打卡范围内</view>
      <view class="location-info _inner" v-else>在打卡范围内</view>
    </view>
    <button class="photo-clock" :style="{ background: showMap || punchType ? '#909399' : 'rgb(45, 80, 242)' }"
      @click="getImage">
      拍照打卡
    </button>
  </view>
</template>
<script lang="ts" setup>
import markBlueSrc from '@/static/pages/images/_mark_blue.png'
import markRedSrc from '@/static/pages/images/_mark_red.png'
import { getTaskClockInOne, taskAddClockIn, hazardPunchInRange } from './featch'
import SafetyNavbar from '../components/safety-navbar.vue'
import { useAppStore, usefileConfigStore, useUserStore } from '@/store'
import { getCurrentPosition } from '@/utils'
import { getWatermarkBizData } from '@/components/upload/featch'
import axios from 'axios'
import { compressFile } from '@/utils/dictData'
const VITE_UPLOAD_IMAGE_BASEURL = import.meta.env.VITE_UPLOAD_WATERMARK_BASEURL

const VITE_SERVER_BASEURL = import.meta.env.VITE_SERVER_BASEURL
const VITE_APP_PROXY_PREFIX = import.meta.env.VITE_APP_PROXY_PREFIX

const currentEnvInfo = useAppStore().appEnv

/* 获取登录人信息 */
const userStore = useUserStore()
const userinfo: any = userStore.userInfo ? userStore.userInfo : {}
const showInfo = ref<boolean>(false)
const showMap = ref<boolean>(true)
const punchType = ref(false) // 是否在范围内 true不在范围内 false在范围内

const saveParams = reactive({
  taskId: '',
  userId: userinfo.id,
  userName: userinfo.userName,
})
const rangeParams = reactive({
  lat1: null,
  lat2: 0,
  lon1: null,
  lon2: 0,
  range: 0,
})
const address = ref<string>('')
const unitaddress = ref<string>('')
onLoad((params) => {
  saveParams.taskId = params.taskId
  if (params.lat1 || params.lon1) {
    rangeParams.lat1 = Number(params.lat1)
    rangeParams.lon1 = Number(params.lon1)
  }
  rangeParams.range = Number(params.range)
  // 显示地图
  getMap()
  // 获取水印
  getWatermark()
})
// 水印信息
const fileInfo = ref({
  address: 'string',
  dateTime: 'string',
  logoImageUrl: 'string',
  projectName: 'string',
  sourceImageUrl: 'string',
  unitName: 'string',
  userName: 'string',
  waterTemplateName: 'string',
  weather: 'string',
  workContent: 'string',
})
// 获取水印信息
const getWatermark = async () => {
  let position = {}
  position = await getCurrentPosition()
  const info = {
    coordType: 'bd09ll',
    longitude: position.longitude,
    latitude: position.latitude,
    orgCode: userinfo.orgCode,
  }
  const res: any = await getWatermarkBizData(
    info.coordType,
    info.longitude,
    info.latitude,
    info.orgCode,
  )
  if (res.code === 'success') {
    fileInfo.value = res.data
  }
}
// 图片信息
const compressFileList = ref()
const getImage = () => {
  if (showMap.value || punchType.value) {
    return false
  }
  uni.chooseImage({
    count: 1,
    sizeType: ['compressed'],
    sourceType: ['camera'],
    async success(res: any) {
      console.log(res)
      uni.showLoading({
        title: '上传中...',
        icon: 'none',
        mask: true,
      })
      // for (let i = 0; i < res.tempFiles.length; i++) {
      const formData = new FormData()
      const size = res.tempFiles[0].size / 1024 / 1024 // 计算文件大小（单位：M）
      const extension = res.tempFiles[0].type
      const arr = ['image/jpeg', 'image/png', 'image/webp', 'image/jpg'] // 允许的图片格式列表\\
      if (size > 15) {
        uni.showToast({ title: '图片大小不能超过15M', icon: 'none' })
      } else if (extension && !arr.includes(extension)) {
        uni.showToast({ title: '图片格式仅支持JPG、PNG、JPEG', icon: 'none' })
      } else {
        compressFileList.value = await compressFile(res.tempFiles[0])
        formData.append('address', fileInfo.value.address)
        formData.append('dateTime', fileInfo.value.dateTime)
        formData.append('logoImageUrl', fileInfo.value.logoImageUrl)
        formData.append('watermarkProjectName', fileInfo.value.projectName)
        formData.append('sourceImageUrl', fileInfo.value.sourceImageUrl)
        formData.append('unitName', fileInfo.value.unitName)
        formData.append('userName', fileInfo.value.userName)
        formData.append('waterTemplateName', fileInfo.value.waterTemplateName)
        formData.append('weather', fileInfo.value.weather)
        formData.append('workContent', '隐患排查')
        console.log(compressFileList.value, '压缩后的文件')
        formData.append('files', compressFileList.value)
        // 调用上传图片方法---图片上传完成之后调用上传打卡信息接口
        uploadFile(formData)
        // }
      }
    },
    fail: function (chooseImageError) {
      uni.showToast({
        title: chooseImageError,
      })
    },
  })
}

const getMap = () => {
  setTimeout(() => {
    /* eslint-disable no-console, no-alert */
    // 创建百度地图
    const map = new BMapGL.Map('container')
    // 获取当前位置经纬度
    const geolocation = new BMapGL.Geolocation()
    // console.log('geolocation ----', geolocation)
    geolocation.getCurrentPosition(function (r) {
      if (this.getStatus() === 6) {
        uni.showModal({
          title: '提示',
          content:
            '定位失败，原因：没有权限，请在手机设置中找到对应的app，点击权限管理中开启位置权限，在重新打开',
          success: function (res) {
            if (res.confirm) {
              // console.log('用户点击确定')
            } else if (res.cancel) {
              // console.log('用户点击取消')
            }
          },
        })
      }
      if (this.getStatus() === BMAP_STATUS_SUCCESS) {
        // 当前所在位置点绘制
        const location = new BMapGL.Point(r.point.lng, r.point.lat)
        if (rangeParams.lon1 === null && rangeParams.lat1 === null) {
          uni.showToast({
            title: '单位地址为空，请检查是否有单位地址',
            icon: 'none',
            duration: 2000,
          })
          return
        }
        const gisPoint = new GISShare.SMap.Geometry.Point(rangeParams.lon1, rangeParams.lat1)
        // 由BD09MC转换为百度经纬度坐标BD09LL
        GISShare.SMap.Fitting.FittingHelper.Fit(
          gisPoint,
          GISShare.SMap.SpatialReference.ProjectedCoordinateSystemStyle
            .eWGS_1984_Web_Mercator_Auxiliary_Sphere_BD09MC,
          GISShare.SMap.SpatialReference.GeographicCoordinateSystemStyle.eGCS_WGS_1984_BD09LL,
        )
        const dX = gisPoint.getX()
        const dY = gisPoint.getY()
        rangeParams.lon1 = dX
        rangeParams.lat1 = dY
        const blueIcon = new BMapGL.Icon(markBlueSrc, new BMapGL.Size(30, 30))
        const marker = new BMapGL.Marker(location, { icon: blueIcon })
        map.addOverlay(marker)
        // 创建一个mark标记
        // 创建图标样式
        const redIcon = new BMapGL.Icon(markRedSrc, new BMapGL.Size(30, 30))
        const companyPosi = { lat: rangeParams.lat1, lng: rangeParams.lon1 }
        const mk = new BMapGL.Marker(companyPosi, { icon: redIcon })
        map.addOverlay(mk)
        map.panTo(companyPosi)
        const points = new BMapGL.Point(companyPosi.lng, companyPosi.lat)
        // 设置单位在地图上的位置 - 打卡地址范围
        map.centerAndZoom(points, 15) // 初始化地图,设置中心点坐标和地图级别
        // 创建圆形区域
        const circle = new BMapGL.Circle(points, rangeParams.range, {
          strokeColor: '#2d50f2',
          strokeWeight: 2,
          strokeOpacity: 0.8,
          fillColor: '#2d50f2',
          fillOpacity: 0.1,
        })
        map.addOverlay(circle)
        map.enableScrollWheelZoom(true)
        // 创建地理编码实例
        const myGeo = new BMapGL.Geocoder()
        // 根据坐标得到地址描述
        rangeParams.lat2 = r.point.lat
        rangeParams.lon2 = r.point.lng
        // 获取单位地址
        const nuitlocation = new BMapGL.Point(rangeParams.lon1, rangeParams.lat1)
        myGeo.getLocation(nuitlocation, function (result) {
          if (result) {
            unitaddress.value = result.address
          }
        })

        myGeo.getLocation(location, function (result) {
          if (result) {
            address.value = result.address
          }
          hazardPunchRange()
        })
      }
    })
    /* eslint-enable no-console, no-alert */
  }, 100)
}
// 判断是否允许打卡
const hazardPunchRange = async () => {
  try {
    const res = await hazardPunchInRange(rangeParams)
    // 返回true则在范围内允许打卡 否则不允许打卡
    if (res.data === false) {
      punchType.value = true
    }
    showMap.value = false
  } catch (error) {
    showMap.value = false
  }
}
// 请求提交打卡信息
const taskAddClockInAPI = (params) => {
  taskAddClockIn(params)
    .then((res) => {
      uni.hideLoading()
      uni.showToast({
        icon: 'none',
        title: '上传成功',
      })
      // 缓存中存个变量 返回上一个页面时 通过这个变量判断是否打卡
      uni.setStorageSync('isClock', { isClock: true })
      setTimeout(() => {
        uni.navigateBack({
          delta: 1, // 默认值为1，表示返回上一页面
        })
      }, 2000)
    })
    .catch(() => {
      uni.hideLoading()
      uni.showToast({
        icon: 'none',
        title: '上传失败',
      })
    })
    .finally(() => {
      uni.hideLoading()
    })
}
// 上传文件
async function uploadFile(formData) {
  const apiClient = axios.create({
    baseURL: VITE_SERVER_BASEURL, // 基础URL
    timeout: 60000,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  })
  apiClient
    .post(VITE_APP_PROXY_PREFIX + 'ehs-clnt-hazard-service' + VITE_UPLOAD_IMAGE_BASEURL, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
        Accept: '*/*',
      },
    })
    .then((response) => {
      console.log('文件上传成功:', response.data)
      if (response.data.code === 'success') {
        const upfiles = response.data
        const params = {
          clockInAddress: address.value,
          filePath: upfiles.data[0].fileUrl,
          taskId: saveParams.taskId,
          userId: saveParams.userId,
          userName: saveParams.userName,
        }
        // 调用上传打卡信息接口
        taskAddClockInAPI(params)
      } else {
        uni.hideLoading()
        uni.showToast({
          icon: 'none',
          title: '上传失败',
        })
      }
    })
    .catch((error) => {
      uni.hideLoading()
      uni.showToast({
        icon: 'none',
        title: error.message,
      })
    })
    .finally(() => {
      // uni.hideLoading()
    })
}
</script>
<style lang="scss" scoped>
body {
  background-color: #f5f5f5 !important;
}

.button-placeholder {
  position: fixed;
  bottom: 0px;
  z-index: 9999;
  box-sizing: border-box;
  width: 100vw;
  padding: 10px 0px 20px;
  background: #fff;
  border-radius: 10px 10px 0px 0px;
}

.location {
  box-sizing: border-box;
  width: 95vw;
  padding: 10px 20px;
  margin: 0px auto;
  font-size: 14px;
  color: rgb(20, 20, 20);
  background-color: #fff;
  border-radius: 10px;

  .location-title {
    margin-bottom: 10px;
    font-weight: 700;
    color: #000000;
  }

  .location-info {
    color: rgb(150, 150, 150);
  }

  ._outer {
    color: #e80000;
  }

  ._inner {
    color: #1ae92c;
  }

  .location-item {
    margin-bottom: 10px;
    color: #333333;

    .location-title {
      margin-right: 10px;
      font-weight: 100;
      color: rgb(120, 120, 120);
    }
  }

  .location-photo {
    display: flex;
    align-items: flex-start;

    .location-title {
      margin-right: 10px;
      font-weight: 100;
      color: rgb(120, 120, 120);
    }
  }
}

.photo-clock {
  width: 96vw;
  margin-top: 20px;
  color: #fff;
  background-color: rgb(45, 80, 242);
}
</style>
