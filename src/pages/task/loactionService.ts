import { watch } from 'vue'
import { useAppReceiveStore } from '@/store'

export class wychLocation {
  static receiveStore = useAppReceiveStore()
  static getLocationinfo = async () => {
    try {
      const { data } = await window.waltz.call({
        // 或者通过 window.waltz.call
        module: 'WZLocation', // 模块名，对应下表的Module
        handlerName: 'getLoactionInfo', // 方法名，对应下表的HandlerName
      })
      // console.log('>>>>', data)

      if (+data.code === 10000) {
        // 成功回调的业务代码
        // const _body = data.body // 接口返回的业务实参对象
        // this.scanCallBack(_body.QRCode, taskid)
        // 成功回调的业务代码
        const resultObject = data.body // 接口返回的业务实参对象
        // console.log(resultObject, '获取的经纬度')
        this.scanCallBack(resultObject)
      }
    } catch (error) {
      uni.showToast({
        title: 'GPS定位信号弱',
        icon: 'none',
      })
    }
  }

  static scanCallBack = (data) => {
    // console.log(data)
    if (data) {
      return data
    } else {
      uni.showToast({
        title: 'GPS定位信号弱',
        icon: 'none',
      })
    }
  }
}
