<route lang="json5">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '现场打卡',
  },
}
</route>
<template>
  <SafetyNavbar title="现场打卡"></SafetyNavbar>
  <view v-if="showMap">地图正在加载中...</view>
  <!-- <view id="container" style="width: 100vw"
    :style="{ height: showInfo ? 'calc(100vh - 400px)' : 'calc(100vh - 250px)' }"></view> -->
  <view id="container" style="width: 100vw" :style="{ height: 'calc(100vh - 250px)' }"></view>
  <view class="button-placeholder">
    <view class="location" v-if="unitaddress">
      <view class="location-title">单位地址</view>
      <view class="location-info">{{ unitaddress }}</view>
    </view>
    <view class="location" v-if="address">
      <view class="location-title">当前位置</view>
      <view class="location-info">{{ address }}附近</view>
    </view>
    <view class="location" v-if="address">
      <view class="location-title">当前范围</view>
      <view class="location-info _outer" v-if="punchType">不在打卡范围内</view>
      <view class="location-info _inner" v-else>在打卡范围内</view>
    </view>
    <!-- <view class="location" v-if="showInfo">
      <view class="location-item">
        <span class="location-title">打卡时间</span>
        {{ infoData.clockInTime }}
      </view>
      <view class="location-item">
        <span class="location-title">打卡位置</span>
        {{ infoData.clockInAddress }}
      </view>
      <view class="location-item">
        <span class="location-title">打卡人员</span>
        {{ infoData.userName }}
      </view>
      <view class="location-photo">
        <span class="location-title">打卡照片</span>
        <wd-img :src="VITE_PREVIEW_BASEURL + infoData.filePath" alt="" style="width: 60px; height: 60px" />
      </view>
    </view> -->
    <button class="photo-clock" :style="{ background: showMap || punchType ? '#909399' : 'rgb(45, 80, 242)' }"
      @click="getImage">
      <!-- {{ showInfo ? '重新打卡' : '拍照打卡' }} -->
      拍照打卡
    </button>
  </view>
  <!-- <wd-overlay :show="showloading" style="line-height: 50; text-align: center">
    <wd-loading />
  </wd-overlay> -->
</template>
<script lang="ts" setup>
import markBlueSrc from '@/static/pages/images/_mark_blue.png'
import markRedSrc from '@/static/pages/images/_mark_red.png'
import { getTaskClockInOne, taskAddClockIn, hazardPunchInRange } from './featch'
import SafetyNavbar from '../components/safety-navbar.vue'
import { useAppStore, usefileConfigStore, useUserStore } from '@/store'
const VITE_UPLOAD_BASEURL = import.meta.env.VITE_UPLOAD_BASEURL
// const VITE_PREVIEW_BASEURL = import.meta.env.VITE_PREVIEW_BASEURL

const usefileStore = usefileConfigStore()
const VITE_PREVIEW_BASEURL = ref()
const currentEnvInfo = useAppStore().appEnv
if (!currentEnvInfo.wychApp) {
  VITE_PREVIEW_BASEURL.value = usefileStore.fileUrl.fileUrlPrefixMiniProgram + '/'
} else {
  VITE_PREVIEW_BASEURL.value = usefileStore.fileUrl.fileUrlPrefix + '/'
}

// const VITE_PREVIEW_BASEURL = ref(usefileStore.fileUrl + '/')

// const VITE_PREVIEW_BASEURL = ref('')
// getbaseurl()
// // eslint-disable-next-line camelcase
// function getbaseurl() {
//   const dynamicsUrl = location.origin
//   if (location.protocol === 'https:') {
//     return (VITE_PREVIEW_BASEURL.value = dynamicsUrl + import.meta.env.VITE_PREVIEW_BASEURL_dev)
//   } else {
//     return (VITE_PREVIEW_BASEURL.value = import.meta.env.VITE_PREVIEW_BASEURL)
//   }
// }
/* 获取登录人信息 */
const userStore = useUserStore()
const userinfo: any = userStore.userInfo ? userStore.userInfo : {}
const showInfo = ref<boolean>(false)
const showMap = ref<boolean>(true)
const punchType = ref(false) // 是否在范围内 true不在范围内 false在范围内
// const showloading = ref<boolean>(false)
const infoData = reactive({
  userName: '',
  clockInAddress: '',
  clockInTime: '',
  filePath: '',
})
const saveParams = reactive({
  taskId: '',
  userId: userinfo.id,
  userName: userinfo.userName,
})
const rangeParams = reactive({
  lat1: null,
  lat2: 0,
  lon1: null,
  lon2: 0,
  range: 0,
})
const address = ref<string>('')
const unitaddress = ref<string>('')
onLoad((params) => {
  uni.setStorageSync('face-photo', '')
  saveParams.taskId = params.taskId
  const obj = {
    taskId: params.taskId,
    userId: saveParams.userId,
  }
  // // console.log(params, '=============================params')
  if (params.lat1 || params.lon1) {
    rangeParams.lat1 = Number(params.lat1)
    rangeParams.lon1 = Number(params.lon1)
  }
  rangeParams.range = Number(params.range)
  getMap()
  // getTaskClockInOneAPI(obj)
})

const getImage = () => {
  if (showMap.value || punchType.value) {
    return false
  }
  // const formData = new FormData()
  uni.navigateTo({
    url: `/pages/demoCameraApp/index`,
  })
  // uni.chooseImage({
  //   count: 1,
  //   type: 'image',
  //   sourceType: ['camera'], // 保留相机和相册
  //   success(res) {
  //     // console.log('选择图片得信息', res)
  //     uni.showLoading({
  //       title: '上传中...',
  //       icon: 'none',
  //     })
  //     function uploadFile(formData, tempFilePath) {
  //       uni.uploadFile({
  //         url: VITE_UPLOAD_BASEURL,
  //         filePath: tempFilePath,
  //         name: 'files',
  //         formData,
  //         success: (uploadFileRes) => {
  //           if (uploadFileRes.data) {
  //             const upfiles = JSON.parse(uploadFileRes.data)
  //             const params = {
  //               clockInAddress: address.value,
  //               filePath: upfiles.data[0].fileUrl,
  //               taskId: saveParams.taskId,
  //               userId: saveParams.userId,
  //               userName: saveParams.userName,
  //             }
  //             taskAddClockInAPI(params)
  //             // }
  //           } else {
  //             uni.showToast({
  //               icon: 'none',
  //               title: '网络错误',
  //             })
  //           }
  //         },
  //       })
  //     }
  //     formData.append('files', res.tempFiles[0])
  //     uploadFile(formData, res.tempFilePaths[0])
  //   },
  // })
}
onShow(() => {
  const face = uni.getStorageSync('face-photo')
  // console.log(face, '=====show--face')
  if (face) {
    uni.showLoading({
      title: '上传中...',
      icon: 'none',
    })
    // const res = JSON.parse(face)
    // // console.log(res, '===============res')
    const params = {
      clockInAddress: address.value,
      filePath: face[0].fileUrl,
      taskId: saveParams.taskId,
      userId: saveParams.userId,
      userName: saveParams.userName,
    }
    // console.log(params, '=======================params')
    setTimeout(() => {
      taskAddClockInAPI(params)
    }, 100)
  }
})

// const doors = () => {
//   let layer
//   let objPosition = {}
//   let extent
//   // 地图控制
//   const indoor = new IndoorMap({
//     target: 'gsMap', // 绑定的标签位置
//     center: [13055425.12247062, 3710371.6014901265],  // 设置地图初始中心位置（百度墨卡托BD09MC）
//     zoom: 1, // 设置地图初始层级
//     tile: true, // 底图是否可见
//     tileURL: CONST_map_vector_baidu, // 底图URL地址配置
//     onLoad: onLoad  // 加载完成事件
//   })
//   // 确保顺序执行
//   function onLoad(sender) {
//     const indoorMap = sender
//     // 创建矢量图层
//     layer = indoorMap.createCustomizeVectorLayer({
//       name: '矢量图层',
//     })
//     // 添加点样式
//     let styles = IndoorMap.createImageStyle({
//       src: "../../../img/mark7.png",
//       anchor: [0.5, 1],
//       scale: 0.7,
//     }) //点样式位置，锚点, 缩放
//     //
//     //单个添加点数据
//     //
//     indoorMap.addPointToCustomizeVectorLayer(
//       layer,
//       { name: '四号楼', x: 13055326.623421457, y: 3710441.1480062976 },
//       'x',  //x轴对应字段
//       'y',  //y轴对应字段
//       undefined,  //z轴坐标对应字段，可省去
//       styles
//     );
//     //
//     //添加点数组数据
//     //
//     indoorMap.addPointsToCustomizeVectorLayer(
//       layer,
//       [
//           { name: '三号楼', x: 13055360.542381916, y: 3710384.297968333, zt: 1 },
//           { name: '二号楼', x: 13055383.20153063, y: 3710323.947769371, zt: 2 },
//           { name: '一号楼', x: 13055408.04358669, y: 3710260.8872356135, zt: 3 },
//       ],
//       'x',  //x轴对应字段
//       'y',  //y轴对应字段
//       undefined,  //z轴坐标对应字段，可省去
//       //过滤器 ，通过data.zt zt值加载不同样式
//       function (data) {
//         switch (data.zt) {
//           case 1:
//               styles = IndoorMap.createImageStyle({
//                   src: "../../../img/mark6.png",
//                   anchor: [0.5, 1],
//                   scale: 0.7,
//               });
//               break;
//           case 2:
//               styles = IndoorMap.createImageStyle({
//                   src: "../../../img/mark5.png",
//                   anchor: [0.5, 1],
//                   scale: 0.7,
//               });
//               break;
//           case 3:
//               styles = IndoorMap.createImageStyle({
//                   src: "../../../img/mark4.png",
//                   anchor: [0.5, 1],
//                   scale: 0.7,
//               });
//               break;
//         }
//         return styles;
//       }
//     );
//     extent = calcExtent(indoorMap);
//     zoomToExtent(indoorMap)
//     // console.log("加载完成")
//   }
//   //获取所有的Point点
//   function calcExtent(indoor) {
//       let pMultiPoint = new GISShare.SMap.Geometry.MultiPoint();  //创建一个多点
//       indoor.getCustomizeVectorLayerFeatures(layer).forEach(ele => {
//           let xy = ele.getGeometry().getCoordinates();    //获取要素坐标
//           // console.log(xy);
//           pMultiPoint.Add(new GISShare.SMap.Geometry.Point(xy[0], xy[1]));  //创建point加到多点中
//           objPosition[ele.gsData.name] = xy;
//       });
//       let pEnvelope = pMultiPoint.getEnvelope();  //获取点位的最小外接矩形
//       pEnvelope.Expand(1.5, 1.5);  //将外接矩形扩大至1.5倍数
//       //Left Bottom right Top
//       return [pEnvelope.getLeft(), pEnvelope.getBottom(), pEnvelope.getRight(), pEnvelope.getTop()];
//   };
//   //范围控制（定位到当前所有数据）
//   function zoomToExtent(indoor) {
//       indoor.zoomToExtent(undefined, undefined, extent);  //根据外接矩形定位至当前数据
//   };
//   //要素点击事件
//   indoor.onMouseClick = function (e) {
//       let geomObject = indoor.getGeoObjectByClientXY(layer, e.pixel[0], e.pixel[1]);
//       // console.log(geomObject);
//       if (geomObject) {
//           alert('选中：' + geomObject.gsData.name + '\n' + '坐标：' + '(' + geomObject.gsData.x + ',' + geomObject.gsData.y + ')');
//       }
//   };
//   //
//   // 属性控制
//   //
//   const gui = new GUI({ title: "控制" });
//   const objParams = {
//       '要素': "三号楼",
//       '图层是否可见': true,
//       Zoom: function() {
//           zoomToExtent(indoor)
//       }
//   };
//   gui.add(objParams, '要素', objPosition).onChange(function (value) {
//       indoor.setCenter([value[0], value[1]]);
//   });
//   gui.add(objParams, '图层是否可见').onChange(function (value) {
//       layer.setVisible(value);
//   });
//   gui.add(objParams, 'Zoom').name("全局定位");
// }
const getMap = () => {
  setTimeout(() => {
    /* eslint-disable no-console, no-alert */
    // 创建百度地图
    const map = new BMapGL.Map('container')
    // 获取当前位置经纬度
    const geolocation = new BMapGL.Geolocation()
    // console.log('geolocation ----', geolocation)
    geolocation.getCurrentPosition(function (r) {
      if (this.getStatus() === 6) {
        uni.showModal({
          title: '提示',
          content:
            '定位失败，原因：没有权限，请在手机设置中找到对应的app，点击权限管理中开启位置权限，在重新打开',
          success: function (res) {
            if (res.confirm) {
              // console.log('用户点击确定')
            } else if (res.cancel) {
              // console.log('用户点击取消')
            }
          },
        })
      }
      if (this.getStatus() === BMAP_STATUS_SUCCESS) {
        // 当前所在位置点绘制
        const location = new BMapGL.Point(r.point.lng, r.point.lat)
        // console.log('location = ', location)
        // console.log('rangeParams = ', rangeParams)
        if (rangeParams.lon1 === null && rangeParams.lat1 === null) {
          uni.showToast({
            title: '单位地址为空，请检查是否有单位地址',
            icon: 'none',
            duration: 2000,
          })
          return
        }
        const gisPoint = new GISShare.SMap.Geometry.Point(rangeParams.lon1, rangeParams.lat1)
        // 由BD09MC转换为百度经纬度坐标BD09LL
        GISShare.SMap.Fitting.FittingHelper.Fit(
          gisPoint,
          GISShare.SMap.SpatialReference.ProjectedCoordinateSystemStyle
            .eWGS_1984_Web_Mercator_Auxiliary_Sphere_BD09MC,
          GISShare.SMap.SpatialReference.GeographicCoordinateSystemStyle.eGCS_WGS_1984_BD09LL,
        )
        const dX = gisPoint.getX()
        const dY = gisPoint.getY()
        // console.log(dX, '======================dX')
        // console.log(dY, '======================dY')
        rangeParams.lon1 = dX
        rangeParams.lat1 = dY
        // console.log(rangeParams, '======================rangeParams')
        const blueIcon = new BMapGL.Icon(markBlueSrc, new BMapGL.Size(30, 30))
        const marker = new BMapGL.Marker(location, { icon: blueIcon })
        map.addOverlay(marker)
        // 创建一个mark标记
        // 创建图标样式
        const redIcon = new BMapGL.Icon(markRedSrc, new BMapGL.Size(30, 30))
        const companyPosi = { lat: rangeParams.lat1, lng: rangeParams.lon1 }
        const mk = new BMapGL.Marker(companyPosi, { icon: redIcon })
        map.addOverlay(mk)
        map.panTo(companyPosi)
        // console.log('r.point', r.point)
        const points = new BMapGL.Point(companyPosi.lng, companyPosi.lat)
        // 设置单位在地图上的位置 - 打卡地址范围
        map.centerAndZoom(points, 15) // 初始化地图,设置中心点坐标和地图级别
        // MultiCircle
        // 创建圆形区域
        const circle = new BMapGL.Circle(points, rangeParams.range, {
          strokeColor: '#2d50f2',
          strokeWeight: 2,
          strokeOpacity: 0.8,
          fillColor: '#2d50f2',
          fillOpacity: 0.1,
        })
        map.addOverlay(circle)
        map.enableScrollWheelZoom(true)
        // alert('您的位置：' + r.point.lng + ',' + r.point.lat);
        // 创建地理编码实例
        const myGeo = new BMapGL.Geocoder()
        // 根据坐标得到地址描述
        rangeParams.lat2 = r.point.lat
        rangeParams.lon2 = r.point.lng
        // 获取单位地址
        const nuitlocation = new BMapGL.Point(rangeParams.lon1, rangeParams.lat1)
        myGeo.getLocation(nuitlocation, function (result) {
          if (result) {
            // console.log('单位.address ---- = ', result.address)
            unitaddress.value = result.address
          }
        })

        myGeo.getLocation(location, function (result) {
          if (result) {
            // console.log('result.address ---- = ', result.address)
            address.value = result.address
          }
          hazardPunchRange()
        })
      }
    })
    /* eslint-enable no-console, no-alert */
  }, 100)
}
const hazardPunchRange = async () => {
  try {
    const res = await hazardPunchInRange(rangeParams)
    // 返回true则在范围内允许打卡 否则不允许打卡
    if (res.data === false) {
      punchType.value = true
    }
    showMap.value = false
    // console.log('校验用户是否在打卡范围内 res ---- = ', res, punchType.value)
  } catch (error) {
    // console.log('校验用户是否在打卡范围内接口报错', error)
    showMap.value = false
  }
}
// 请求
const taskAddClockInAPI = (params) => {
  // showloading.value = true
  taskAddClockIn(params)
    .then((res) => {
      // console.log('res', res)
      // 打卡成功之后在重新请求
      // showloading.value = false
      uni.hideLoading()
      uni.showToast({
        icon: 'none',
        title: '上传成功',
      })
      uni.setStorageSync('face-photo', '')
      // 缓存中存个变量 返回上一个页面时 通过这个变量判断是否打卡
      uni.setStorageSync('isClock', { isClock: true })
      setTimeout(() => {
        uni.navigateBack({
          delta: 1, // 默认值为1，表示返回上一页面
        })
      }, 2000)
      // getTaskClockInOneAPI(saveParams)
    })
    .catch(() => {
      // showloading.value = false
      uni.hideLoading()
      uni.showToast({
        icon: 'none',
        title: '上传失败',
      })
    })
    .finally(() => {
      uni.hideLoading()
      // showloading.value = false
    })
}

onUnload(() => {
  uni.setStorageSync('face-photo', '')
})

// 请求最近一条打卡记录
const getTaskClockInOneAPI = (params) => {
  getTaskClockInOne(params).then((res: any) => {
    if (res.data.id) {
      showInfo.value = true
      infoData.userName = res.data.userName
      infoData.clockInAddress = res.data.clockInAddress
      infoData.clockInTime = res.data.clockInTime
      infoData.filePath = res.data.filePath
    } else {
      showInfo.value = false
    }
  })
}
</script>
<style lang="scss" scoped>
body {
  background-color: #f5f5f5 !important;
}

.button-placeholder {
  position: fixed;
  bottom: 0px;
  z-index: 9999;
  box-sizing: border-box;
  width: 100vw;
  padding: 10px 0px 20px;
  background: #fff;
  border-radius: 10px 10px 0px 0px;
}

.location {
  box-sizing: border-box;
  width: 95vw;
  padding: 10px 20px;
  margin: 0px auto;
  font-size: 14px;
  color: rgb(20, 20, 20);
  background-color: #fff;
  border-radius: 10px;

  .location-title {
    margin-bottom: 10px;
    font-weight: 700;
    color: #000000;
  }

  .location-info {
    color: rgb(150, 150, 150);
  }

  ._outer {
    color: #e80000;
  }

  ._inner {
    color: #1ae92c;
  }

  .location-item {
    margin-bottom: 10px;
    color: #333333;

    .location-title {
      margin-right: 10px;
      font-weight: 100;
      color: rgb(120, 120, 120);
    }
  }

  .location-photo {
    display: flex;
    align-items: flex-start;

    .location-title {
      margin-right: 10px;
      font-weight: 100;
      color: rgb(120, 120, 120);
    }
  }
}

.photo-clock {
  width: 96vw;
  margin-top: 20px;
  color: #fff;
  background-color: rgb(45, 80, 242);
}
</style>
