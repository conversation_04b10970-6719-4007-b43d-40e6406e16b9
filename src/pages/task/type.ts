/*
 * @Author: 悦悦 <EMAIL>
 * @Date: 2024-09-21 15:55:16
 * @LastEditors: 悦悦 <EMAIL>
 * @LastEditTime: 2024-09-23 20:08:55
 * @FilePath: /隐患排查app/src/pages/task/type.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */

// export type pageData<T> = {
//   pageNo: number
//   pageSize: number
//   pages: number
//   rows: T[]
//   total: number
// }

// 检查任务列表
export type pageForm = {
  keyWords: string
  pageNo: number
  pageSize: number
  planEndDate: string
  planStartDate: string
  planTypeId: string
  planTypeName: string
  planUserId: string
  searchCount: number
  superviseUnitId: string
  taskState: '1' | '2' | '3' // 1:待开始 2：进行中 3：已完成
  unitId: string
  userRoleCodes: string
  taskType: number | string
}

export type recordList = {
  taskId: string
  userId: string
  userName: string
}

export type taskAdd = {
  clockInAddress: string
  filePath: string
  taskId: string
  userId: string
  userName: string
}

export type check = {
  checkContent: string
  checkContentId: string
  checkState: string
  checkType: string
  checkDetail?: string
  id?: string
  parentId?: string
  serialNumber?: string
  complianceRequirements?: string
  legalBasis?: string
  legalLiability?: string
  legalText?: string
  checkTableId?: string
  childCheckAreaList?: check[]
}
/**
 * ResponseModel«TaskCheckPointVo»
 */
export interface Response {
  code?: string
  data?: TaskCheckPointVo
  dataType?: string
  message?: { [key: string]: any }
  status?: string
  token?: string
  [property: string]: any
}

/**
 * TaskCheckPointVo
 */
export interface TaskCheckPointVo {
  /**
   * 检查内容
   */
  checkAreaVos?: CheckAreaVo[]
  /**
   * 巡检状态（0待巡检 1已巡检）
   */
  checkInState?: string
  /**
   * 点位id
   */
  checkPointId?: string
  /**
   * 文件
   */
  fileUploadVos?: fileUploadVos[]
  /**
   * 点位主键id
   */
  id?: string
  /**
   * 任务id
   */
  taskId?: string
  /**
   * 巡检打卡人id
   */
  userId?: string
  /**
   * 巡检打卡人名称
   */
  userName?: string
  [property: string]: any
}

/**
 * CheckAreaVo
 */
export interface CheckAreaVo {
  /**
   * 检查内容
   */
  checkContent?: string
  /**
   * 检查详情
   */
  checkDetail?: string
  /**
   * 检查状态（1正常；2异常）
   */
  checkState?: string
  /**
   * 检查表id
   */
  checkTableId?: string
  /**
   * 检查项-内容子级
   */
  childCheckAreaList?: CheckAreaVo[]
  /**
   * 合规要求
   */
  complianceRequirements?: string
  /**
   * 检查项id
   */
  id?: string
  /**
   * 法规依据
   */
  legalBasis?: string
  /**
   * 法律责任
   */
  legalLiability?: string
  /**
   * 法律原文
   */
  legalText?: string
  /**
   * 父级id,顶级为0
   */
  parentId?: string
  /**
   * 序号
   */
  serialNumber?: string
  [property: string]: any
}

/**
 * 隐患附件表
 */
export interface fileUploadVos {
  /**
   * '附件名称'
   */
  attachName?: string
  /**
   * '附件地址'
   */
  attachPath?: string
  /**
   * ''创建时间''
   */
  createTime?: Date
  /**
   * 主键id
   */
  id?: string
  /**
   * '关联字段ID'
   */
  relationId?: string
  [property: string]: any
}
