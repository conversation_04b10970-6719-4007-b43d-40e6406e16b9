<!--
 * @Author: 悦悦 <EMAIL>
 * @Date: 2024-11-05 18:32:10
 * @LastEditors: fangweiwei <EMAIL>
 * @LastEditTime: 2025-01-14 20:29:15
 * @FilePath: /隐患排查app/src/pages/task/qrcodePage.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<route lang="json5">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '扫码',
  },
}
</route>

<template>
  <SafetyNavbar title="扫码"></SafetyNavbar>
  <view class="w-full flex-1">
    <Qrcodescanner class="flex-1" ref="qrcodeRef" @result="onResult" @colse="onBack"></Qrcodescanner>
  </view>
  <!-- <view>
    <Qrcodescanner @result="onResult" @colse="onBack"></Qrcodescanner>
  </view> -->
</template>
<script setup lang="ts">
import SafetyNavbar from '../components/safety-navbar.vue'
import Qrcodescanner from '@/components/qrcodeScanner/index.vue'
const taskId = ref('')
onLoad((params) => {
  // console.log(params)
  taskId.value = params.taskId
})
const onResult: any = (decodedText, res) => {
  // // console.log(res)
  // console.log(decodedText, '=======================decodedText')
  if (decodedText) {
    // const list = decodedText.split('=')
    // // console.log(decodedText)
    decodedText = encodeURIComponent(decodedText)

    uni.redirectTo({
      url: `/pages/task/scanCodes?taskId=${taskId.value}&qrCodeValue=${decodedText}`,
    })
    // uni.reLaunch({
    //   url: `/pages/task/scanCodes?taskId=${taskId.value}&qrCodeValue=${decodedText}`,
    // })
    // uni.navigateTo({
    //   // url: `/pages/task/scanCodes?taskId=${taskId.value}&${list[0]}=${list[1]}`,
    //   url: `/pages/task/scanCodes?taskId=${taskId.value}&qrCodeValue=${decodedText}`,
    // })
  } else {
    uni.navigateBack()
  }
}
function onBack() {
  uni.navigateBack()
}
const qrcodeRef = ref(null)
// onBeforeUnmount(() => {
//   qrcodeRef.value.clearQrcode()
// })
</script>
