<route lang="json5">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '安全生产检查表',
  },
}
</route>

<template>
  <SafetyNavbar title="安全生产检查表"></SafetyNavbar>
  <view class="container">
    <scroll-view class="scroll-content" scroll-y="true">
      <!-- 隐患检测情况 -->
      <view class="search">
        <div class="no-date" v-if="dataList.length < 1">暂无数据</div>
        <view v-for="(item, index) in dataList" :key="index" v-else>
          <wd-collapse v-model="collapsevalue">
            <view class="titbefor"></view>
            <wd-collapse-item class="custom-title" :title="item.checkContent" :name="'itme' + index">
              <view v-for="($item, $index) in item.childCheckAreaList" :key="$index" style="padding: 0.3rem 1rem">
                {{ $index + 1 }}.{{ $item.checkDetail }}
              </view>
            </wd-collapse-item>
          </wd-collapse>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script lang="ts" setup>
import SafetyNavbar from '../../components/safety-navbar.vue'
import {
  PostgetListDevieCheckItemAPI,
  PosthazardCheckDefaultConfigpointAPI,
  postGetAreaListByCheckTableIdAPI,
} from '../featch'
import { useUserStore } from '@/store'
const userStore = useUserStore()
const userInfo: any = userStore.userInfo ? userStore.userInfo : {}
const collapsevalue = ref(['itme0'])
const dataList = ref<any>([])

const taskId = ref<any>('')
const checkRange = ref<any>('')
const checkTableId = ref<any>('')

onLoad((option) => {
  // 判断是否从本模块打开页面
  taskId.value = option.taskId
  checkRange.value = option.checkRange
  checkTableId.value = option.checkTableId || ''

  // console.log(checkTableId.value)
  if (checkTableId.value !== 'null') {
    // getInspectionPoints(checkTableId.value)
    if (option.checkRange === '1') getInspectList()
    else getInspectionPoints(checkTableId.value)
  } else {
    getDefaultConfigpointList()
  }
})

function getInspectList() {
  PostgetListDevieCheckItemAPI(taskId.value, checkRange.value).then((res: any) => {
    if (res.code === 'success') {
      // console.log(res)
      dataList.value = res.data
    }
  })
}

function getInspectionPoints(checkTableId) {
  postGetAreaListByCheckTableIdAPI({ checkTableId } as any).then((res: any) => {
    if (res.code === 'success') {
      // console.log(res)
      dataList.value = res.data
    }
  })
}
function getDefaultConfigpointList() {
  PosthazardCheckDefaultConfigpointAPI({
    checkTableType: 'point',
    pageNo: 1,
    pageSize: 9999,
    unitId: userInfo.topUnitId,
  }).then((res: any) => {
    dataList.value = res.data.rows
  })
}
</script>

<style lang="scss" scoped>
.no-date {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100px;
  color: rgb(125, 125, 125);
}

// body {
//   background-color: #f9f9f9 !important;
// }
// .container {
//   height: 100%;
// }

// .scroll-content {
//   height: calc(100vh - 16.375rem); /* 减去底部固定区域的高 */
// }

.item {
  height: 50px;
  line-height: 50px;
  text-align: center;
  border-bottom: 1px solid #ccc;
}

.fixed-bottom {
  position: fixed;
  bottom: 0;
  display: flex;
  flex-direction: row;
  align-items: center;
  width: 100%;
  height: 3.375rem;
  line-height: 3.375rem;
  color: #fff;
  text-align: center;
  background-color: white;
}

.cus-steps {
  padding-top: 0.625rem;
  background-color: white;
}

.titbefor {
  position: absolute;
  width: 0.2rem;
  height: 1rem;
  margin: 1rem 0 1rem 0.5rem;
  background-color: blue;
  border-radius: 2px;
}

.custom-title {
  padding: 0 !important;
}

.infobox {
  margin-top: 0.625rem;
  margin-bottom: 0.625rem;
}

.wrap {
  padding: 0.5rem;
  margin: 0 1rem;
  background: #ebeef5;
  border-radius: 0.5rem;
}

.wrap+.wrap {
  margin-top: 1rem;
}

.box {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  justify-content: space-between;
  padding: 1.125rem;

  .box-itme {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 10.25rem;
    height: 4.375rem;
    margin: 0.3125rem;
    background-color: rgba(188, 201, 218, 0.2);
    border-radius: 0.75rem;

    .imgbox {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 2.25rem;
      height: 2.25rem;

      .img {
        width: 100%;
        height: 100%;
      }
    }
  }

  .box-itme2 {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-around;
    width: 21.1875rem;
    height: 3.4375rem;
    margin: 0.3125rem;
    background-color: rgba(188, 201, 218, 0.2);
    border-radius: 12px;
  }
}

::v-deep {
  .wd-collapse-item__arrow {
    color: #0052d9;
  }

  .wd-collapse-item__body {
    padding: 0 !important;
  }

  .wd-search__input {
    height: 2.7rem;
  }

  .wd-popup {
    border-radius: 1.5rem 1.5rem 0 0;
  }

  .wd-button.is-medium.is-round {
    min-width: 0;
  }
}

.v-popup {
  text-align: center;

  .v-popup-title {
    padding: 1rem;
    font-size: 18px;
    border-bottom: 0.01rem solid #ebeef5;
  }

  .v-popup-content {
    display: flex;
    justify-content: space-between;
    padding: 0.6rem;
    border-bottom: 0.01rem solid #ebeef5;
  }

  .v-popup-btn {
    display: flex;
    flex-direction: column;
    padding: 0.7rem;

    .wd-button {
      padding: 1rem;
      margin: 0.5rem 0;
    }
  }
}

.wrap-btn {
  display: flex;
  align-items: center;
  margin-top: 10px;

  .wrap-btn-detail {
    padding: 5px 20px;
    margin-right: 10px;
    color: #fff;
    background-color: #0256ff;
    border-radius: 15px;
  }

  .wrap-btn-update {
    padding: 5px 20px;
    color: #fff;
    background-color: #0256ff;
    border-radius: 15px;
  }
}
</style>
