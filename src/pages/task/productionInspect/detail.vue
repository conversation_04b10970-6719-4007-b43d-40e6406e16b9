<!--
 * @Author: 悦悦 <EMAIL>
 * @Date: 2024-09-23 15:48:11
 * @LastEditors: 悦悦 <EMAIL>
 * @LastEditTime: 2024-09-29 21:37:10
 * @FilePath: /隐患排查app/src/pages/task/productionInspect/detail.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<route lang="json5">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '检查项详情',
  },
}
</route>

<template>
  <SafetyNavbar title="检查项详情"></SafetyNavbar>
  <view class="container-d">
    <info :info1Itme="detailData"></info>
    <hiddertroubleList v-if="troubleinfo.length > 0" :type="type" :troubleinfo="troubleinfo" :taskId="params.taskId">
    </hiddertroubleList>
  </view>

  <!-- 底部按钮-----判断页面需不需要上报按钮 -->
  <view class="fixed-bottom" v-if="type === '1'">
    <wd-button type="primary" size="large" @click="goReport()" block>上报隐患</wd-button>
  </view>
</template>

<script lang="ts" setup>
import SafetyNavbar from '../../components/safety-navbar.vue'
import info from '../components/info1.vue'
import hiddertroubleList from '../components/hidder_troubleList.vue'
import { inspectDetail } from '../featch'
const type = ref<string>('')

const detailData = ref<any>({})
onLoad((option) => {
  type.value = option.type
  params.taskId = option.taskId
  params.checkId = option.checkId
  getDetail(option.checkId)
})
const params = reactive({
  taskId: '',
  checkId: '',
})
const troubleinfo = ref<any>([])

function getDetail(id) {
  // 获取详情
  inspectDetail(id, '')
    .then((res: any) => {
      // // console.log(res, '法鲁法规')
      detailData.value = res.data
      troubleinfo.value = res.data.essentialFactorList
      // console.log(troubleinfo)
    })
    .finally(() => {
      // console.log(111)
    })
}
// 上报隐患  // taskid--任务id checkContentId检查项id
function goReport() {
  // checkContentId
  uni.navigateTo({
    url:
      '/pages/randomcheck/createhiddendanger?checkContentId=' +
      params.checkId +
      '&taskId=' +
      params.taskId +
      '&checkId=' +
      detailData.value.id +
      '&types=0',
  })
}
</script>

<style lang="scss" scoped>
::v-deep {
  .wd-button.is-block {
    width: 90% !important;
    height: 90% !important;
    margin-top: 1%;
    border-radius: 0 !important;
  }
}

body {
  background-color: #f9f9f9 !important;
}

.container-d {
  padding-bottom: 3rem;
}

.fixed-bottom {
  position: fixed;
  bottom: 0;
  width: 100%;
  height: 3.375rem;
  line-height: 3.375rem;
  color: #fff;
  text-align: end;
  background-color: white;
  border-top: 0.3px solid #ccc;
}
</style>
