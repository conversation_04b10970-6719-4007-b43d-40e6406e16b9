<route lang="json5">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '安全生产检查表',
  },
}
</route>

<template>
  <z-paging ref="paging" @query="getInspectList">
    <template #top>
      <SafetyNavbar title="安全生产检查表"></SafetyNavbar>
      <wd-collapse v-model="collapsevalue" v-if="showInfo">
        <view class="titbefor"></view>
        <wd-collapse-item class="custom-title" title="隐患检查情况" name="item1">
          <commonStatistics
            ref="commonStatisticsRef"
            :type="0"
            :taskId="params.taskId"
            :is-plan-task="[1, 2].includes(+taskState)"
            :create-by="userInfo.id"
            :unitId="''"
            :topUnitId="
              userInfo.orgRes === '1' ? userInfo.topUnitId : userInfo.associationTopUnitId
            "
          ></commonStatistics>
        </wd-collapse-item>
      </wd-collapse>
      <wd-search
        placeholder-left
        placeholder="请输入检查项模糊搜索"
        v-model="searchVal"
        :hide-cancel="true"
        @change="handleChange"
      />
    </template>
    <div class="no-date" v-if="dataList.length < 1">暂无数据</div>
    <view v-for="(item, index) in dataList" :key="index" v-else>
      <wd-collapse v-model="collapsevalue">
        <view class="titbefor"></view>
        <wd-collapse-item class="custom-title" :title="item.checkContent" :name="item">
          <view
            style="padding: 1rem 0"
            v-if="item.childCheckAreaList && item.childCheckAreaList.length > 0"
          >
            <view class="wrap" v-for="(j, q) in item.childCheckAreaList" :key="q">
              <view>{{ index + 1 }}.{{ q + 1 }} {{ j.checkDetail }}</view>
              <view class="wrap-btn">
                <view class="wrap-btn-detail" @click="goDetail(j)">详情</view>
                <view
                  class="wrap-btn-update"
                  v-if="type != '1' && taskState != '3'"
                  @click="goHidden(j.id)"
                >
                  上报隐患
                </view>
              </view>
            </view>
          </view>
          <view style="padding: 0.5rem 1rem" v-else>
            <view class="wrap-btn">
              <view class="wrap-btn-detail" @click="goDetail(item)">详情</view>
              <view
                class="wrap-btn-update"
                v-if="type != '1' && taskState != '3'"
                @click="goHidden(item.id)"
              >
                上报隐患
              </view>
            </view>
          </view>
        </wd-collapse-item>
      </wd-collapse>
    </view>
  </z-paging>
  <!-- <SafetyNavbar title="安全生产检查表"></SafetyNavbar>
  <view class="container">
    <scroll-view class="scroll-content" scroll-y="true">
      <wd-collapse v-model="collapsevalue" v-if="showInfo">
        <view class="titbefor"></view>
        <wd-collapse-item class="custom-title" title="隐患检查情况" name="item1">
          <commonStatistics ref="commonStatisticsRef" :type="0" :taskId="params.taskId"
            :is-plan-task="[1, 2].includes(+taskState)" :create-by="userInfo.id" :unitId="''" :topUnitId="userInfo.orgRes === '1' ? userInfo.topUnitId : userInfo.associationTopUnitId
              "></commonStatistics>
        </wd-collapse-item>
      </wd-collapse>
      <view class="search">
        <wd-search placeholder-left placeholder="请输入检查项模糊搜索" v-model="searchVal" :hide-cancel="true"
          @change="handleChange" />
        <div class="no-date" v-if="dataList.length < 1">暂无数据</div>
        <view v-for="(item, index) in dataList" :key="index" v-else>
          <wd-collapse v-model="collapsevalue">
            <view class="titbefor"></view>
            <wd-collapse-item class="custom-title" :title="item.checkContent" :name="item">
              <view style="padding: 1rem 0" v-if="item.childCheckAreaList && item.childCheckAreaList.length > 0">
                <view class="wrap" v-for="(j, q) in item.childCheckAreaList" :key="q">
                  <view>{{ index + 1 }}.{{ q + 1 }} {{ j.checkDetail }}</view>
                  <view class="wrap-btn">
                    <view class="wrap-btn-detail" @click="goDetail(j)">详情</view>
                    <view class="wrap-btn-update" v-if="type != '1' && taskState != '3'" @click="goHidden(j.id)">
                      上报隐患
                    </view>
                  </view>
                </view>
              </view>
              <view style="padding: 0.5rem 1rem" v-else>
                <view class="wrap-btn">
                  <view class="wrap-btn-detail" @click="goDetail(item)">详情</view>
                  <view class="wrap-btn-update" v-if="type != '1' && taskState != '3'" @click="goHidden(item.id)">
                    上报隐患
                  </view>
                </view>
              </view>
            </wd-collapse-item>
          </wd-collapse>
        </view>
      </view>
    </scroll-view>
  </view> -->

  <wd-popup v-model="show" position="bottom">
    <view class="v-popup">
      <view class="v-popup-title">常见隐患</view>
      <view class="v-popup-content" v-for="(item, index) in troubleInfo" :key="item">
        <text style="width: 10%">{{ index + 1 }}.</text>
        <view style="width: 75%; text-align: left">
          {{ item.essentialFactorDescribe }}
        </view>
        <wd-button
          style="margin-left: auto; color: #fff; background: #0256ff; border-radius: 7px"
          @click="goReport(item.id, item.zid)"
          size="medium"
        >
          上报
        </wd-button>
      </view>
      <view class="v-popup-btn">
        <wd-button
          style="margin-left: 1rem; color: #fff; background: #0256ff; border-radius: 7px"
          @click="goReport('')"
          size="medium"
        >
          新增隐患
        </wd-button>
        <wd-button
          style="margin-left: 1rem; color: #fff; background: #0256ff; border-radius: 7px"
          @click="show = false"
          size="medium"
        >
          取消
        </wd-button>
      </view>
    </view>
  </wd-popup>
</template>

<script lang="ts" setup>
import commonStatistics from '@/components/commonSelect/common-statistics.vue'
import { useUserStore } from '@/store'
import { debounce } from '@/utils'
import SafetyNavbar from '../../components/safety-navbar.vue'
import { getHazardTaskShortInfo, inspectDetail, postGetAreaListByCheckTableIdAPI } from '../featch'
const userStore = useUserStore()
const userInfo: any = userStore.userInfo ? userStore.userInfo : {}
const collapsevalue = ref(['item1'])
const dataList = ref<any>([])
const searchVal = ref('')
const taskState = ref('')
const type = ref('')
const troubleInfo = ref<any>({})
const show = ref<boolean>(false)
const showInfo = ref<boolean>(false)
const saveOption = ref({})
const checkTableId = ref('')
const params = reactive<any>({
  createBy: '',
  unitId: userInfo.unitId,
  zhId: userInfo.zhId, // 租户
  checkId: '',
  taskId: '',
})
const commonStatisticsRef = ref()
onLoad((option) => {
  // 判断是否从本模块打开页面
  params.taskId = option.taskId
  type.value = option.type
  taskState.value = option.taskState
  showInfo.value = !option.type
  saveOption.value = option
  // 检查表id
  checkTableId.value = option.checkTableId
  getInspectList()
})
onShow(() => {
  showInfo.value = !saveOption.value.type
  getInspectList()
})

const handleChange = debounce((val: any) => {
  searchVal.value = val.value
  getInspectList()
}, 500)

function getInspectList() {
  setTimeout(() => {
    commonStatisticsRef.value?.update()
  })
  // inspectList({ taskId: params.taskId, paramKey: encodeURIComponent(searchVal.value) } as any).then(
  //   (res: any) => {
  //     if (res.code === 'success') {
  //       dataList.value = res.data
  //     }
  //   },
  // )
  postGetAreaListByCheckTableIdAPI({
    checkTableId: checkTableId.value,
    checkContent: searchVal.value,
  } as any).then((res: any) => {
    if (res.code === 'success') {
      // console.log(res)
      dataList.value = res.data
    }
  })
}

// 无需逐一点击上报隐患
async function goHidden(id) {
  // console.log('隐患上报')

  try {
    const res: any = await getHazardTaskShortInfo({ id })
    if (res.code === 'success') {
      if (+res.data?.delFlag === 1) {
        uni.showToast({
          icon: 'none',
          title: '该任务已删除，不支持继续排查。',
        })
      } else {
        params.checkId = id
        show.value = true
        getDetail(params.checkId)
      }
    } else {
      uni.showToast({
        icon: 'none',
        title: res.message,
      })
    }
  } catch (error) {}
}

// 获取常见隐患项
function getDetail(id) {
  // 获取详情
  inspectDetail(id, '')
    .then((res: any) => {
      // console.log(res, '这是获取的是常见隐患项')
      const d = res.data
      troubleInfo.value = d.essentialFactorList.map((item) => {
        return {
          ...item,
          zid: d.id,
        }
      })
    })
    .finally(() => {
      // console.log(111)
    })
}

// 下钻到隐患上报
function goReport(id, zid = '') {
  // console.log(id)
  let url
  // 非快捷上报
  if (!id) {
    url =
      '/pages/randomcheck/createhiddendanger?checkContentId=' +
      params.checkId +
      '&taskId=' +
      params.taskId +
      '&checkId=' +
      params.checkId +
      '&types=0'
  } else {
    // 快捷上报
    url =
      '/pages/randomcheck/createhiddendanger?checkContentId=' +
      params.checkId +
      '&FactorId=' +
      id +
      '&checkId=' +
      zid +
      '&taskId=' +
      params.taskId +
      '&types=0'
  }
  showInfo.value = false
  uni.navigateTo({
    url,
  })
}

// 跳转安全检查表详情
function goDetail(j) {
  // console.log(j)
  // 从任务详情检查表进入
  if (type.value === '1') {
    uni.navigateTo({
      url: `/pages/task/productionInspect/detail?checkId=${j.id}&taskId=${params.taskId}&type=0`,
    })
  } else {
    // 从任务详情点击继续检查进入
    if (taskState.value === '3') {
      // 任务已完成
      uni.navigateTo({
        url: `/pages/task/productionInspect/detail?checkId=${j.id}&taskId=${params.taskId}&type=0`,
      })
    } else if (taskState.value === '2') {
      // 任务进行中
      uni.navigateTo({
        url: `/pages/task/productionInspect/detail?checkId=${j.id}&taskId=${params.taskId}&type=1`,
      })
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep {
  .navbar {
    position: unset !important;
  }
}

.no-date {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100px;
  color: rgb(125, 125, 125);
}

.container {
  height: 100%;
}

.item {
  height: 50px;
  line-height: 50px;
  text-align: center;
  border-bottom: 1px solid #ccc;
}

.fixed-bottom {
  position: fixed;
  bottom: 0;
  display: flex;
  flex-direction: row;
  align-items: center;
  width: 100%;
  height: 3.375rem;
  line-height: 3.375rem;
  color: #fff;
  text-align: center;
  background-color: white;
}

.cus-steps {
  padding-top: 0.625rem;
  background-color: white;
}

.titbefor {
  // position: absolute;
  // width: 0.2rem;
  // height: 1rem;
  // margin: 1rem 0 1rem 0.5rem;
  // background-color: blue;
  // border-radius: 2px;
}

.custom-title {
  padding: 0 !important;
}

.infobox {
  margin-top: 0.625rem;
  margin-bottom: 0.625rem;
}

.wrap {
  padding: 0.5rem;
  margin: 0 1rem;
  background: #ebeef5;
  border-radius: 0.5rem;
}

.wrap + .wrap {
  margin-top: 1rem;
}

.box {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  justify-content: space-between;
  padding: 1.125rem;

  .box-itme {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 10.25rem;
    height: 4.375rem;
    margin: 0.3125rem;
    background-color: rgba(188, 201, 218, 0.2);
    border-radius: 0.75rem;

    .imgbox {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 2.25rem;
      height: 2.25rem;

      .img {
        width: 100%;
        height: 100%;
      }
    }
  }

  .box-itme2 {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-around;
    width: 21.1875rem;
    height: 3.4375rem;
    margin: 0.3125rem;
    background-color: rgba(188, 201, 218, 0.2);
    border-radius: 12px;
  }
}

::v-deep {
  .wd-collapse-item__arrow {
    color: #0052d9;
  }

  .wd-collapse-item__body {
    padding: 0 !important;
  }

  .wd-search__input {
    height: 2.7rem;
  }

  .wd-popup {
    border-radius: 1.5rem 1.5rem 0 0;
  }

  .wd-button.is-medium.is-round {
    min-width: 0;
  }
}

.v-popup {
  text-align: center;

  .v-popup-title {
    padding: 1rem;
    font-size: 18px;
    border-bottom: 0.01rem solid #ebeef5;
  }

  .v-popup-content {
    display: flex;
    justify-content: flex-start;
    width: 95%;
    padding: 0.6rem;
    border-bottom: 0.01rem solid #ebeef5;
  }

  .v-popup-btn {
    display: flex;
    flex-direction: column;
    padding: 0.7rem;

    .wd-button {
      padding: 1rem;
      margin: 0.5rem 0;
    }
  }
}

.wrap-btn {
  display: flex;
  align-items: center;
  margin-top: 10px;

  .wrap-btn-detail {
    padding: 5px 20px;
    margin-right: 10px;
    color: #fff;
    background-color: #0256ff;
    border-radius: 15px;
  }

  .wrap-btn-update {
    padding: 5px 20px;
    color: #fff;
    background-color: #0256ff;
    border-radius: 15px;
  }
}
</style>
