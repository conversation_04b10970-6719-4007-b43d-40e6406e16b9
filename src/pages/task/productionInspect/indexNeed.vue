<route lang="json5">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '安全生产检查表',
  },
}
</route>

<template>
  <SafetyNavbar title="安全生产检查表"></SafetyNavbar>
  <view class="container">
    <scroll-view class="scroll-content" scroll-y="true">
      <!-- 隐患检测情况 -->
      <wd-collapse v-model="collapsevalue" @change="getStatisticsData">
        <view class="titbefor"></view>
        <wd-collapse-item class="custom-title" title="隐患检查情况" name="item1">
          <commonStatistics ref="commonStatisticsRef" :create-by="userInfo.id" :task-id="params.taskId"
            :is-plan-task="[1, 2].includes(+params.taskState)" :type="0" v-if="showComm"></commonStatistics>
        </wd-collapse-item>
      </wd-collapse>
      <view style="background: #fff">
        <wd-search placeholder-left placeholder="请输入检查项模糊搜索" v-model="searchVal" :hide-cancel="true"
          @change="handleChange" />
        <view class="flex content-view">
          <view class="wrap bg1" style="margin-left: 0.625rem">
            <p style="font-size: 14px">待检查</p>
            <p style="font-size: 20px">{{ numTotal.noChecked || 0 }}</p>
          </view>
          <view class="wrap bg2" style="margin-left: 0.625rem">
            <p style="font-size: 14px">符合</p>
            <p style="font-size: 20px">{{ numTotal.hazard || 0 }}</p>
          </view>
          <view class="wrap bg3" style="margin-left: 0.625rem">
            <p style="font-size: 14px">不符合</p>
            <p style="font-size: 20px">{{ numTotal.noHazard || 0 }}</p>
          </view>
          <view class="wrap bg4" style="margin-left: 0.625rem">
            <p style="font-size: 14px">不适用</p>
            <p style="font-size: 20px">{{ numTotal.noSuit || 0 }}</p>
          </view>
        </view>
      </view>
      <view style="margin-top: 0.7rem" v-if="dataList.length > 0">
        <view>
          <wd-tabs v-model="tabValue" animated>
            <wd-tab v-for="($item, index) in dataList" :key="index" :title="`${$item.checkContent}`">
              <wd-collapse v-model="collapsevalue">
                <view class="titbefor"></view>
                <wd-collapse-item class="custom-title" :title="$item.checkContent" :name="$item.serialNumber">
                  <view v-if="$item.children && $item.children.length > 0">
                    <view v-for="(j, q) in $item.children" :key="q"
                      style="margin-bottom: 1rem; border-bottom: 1px solid #ebedf0">
                      <view class="wrap-children">
                        <view style="font-size: 14px; color: #3d3d3d">
                          {{ j.checkDetail }}
                          <span style="color: #0256ff" @click="goDetailHidden(j.checkId)">
                            详情>
                          </span>
                        </view>
                      </view>
                      <view class="wrap-btn">
                        <wd-button :disabled="params.taskState === '3'"
                          style="min-width: 0; margin-top: 1rem; border-radius: 7px" size="medium"
                          :class="[j.hazardState === '1' ? 'isActive' : '']" @click="getStatus(1, j.id, '')">
                          符合
                        </wd-button>
                        <wd-button :disabled="params.taskState === '3'"
                          :class="[j.hazardState === '2' ? 'isActive' : '']" @click="getStatus(2, j.id, j.checkId)"
                          style="min-width: 0; margin-left: 1rem; border-radius: 7px" size="medium">
                          不符合
                        </wd-button>
                        <wd-button :disabled="params.taskState === '3'"
                          :class="[j.hazardState === '3' ? 'isActive' : '']"
                          style="min-width: 0; margin-left: 1rem; border-radius: 7px" size="medium"
                          @click="getStatus(3, j.id, '')">
                          不适用
                        </wd-button>
                      </view>
                      <!-- <view v-if="j.hazardState === '2'">
                        <view v-for="(k, p) in j.hazardPlanTaskEvents" :key="p">
                          <wd-img v-for="(l, r) in k.hazardPlanTaskEventFiles" :key="r" style="width: 100%; height: 90%"
                            :src="VITE_PREVIEW_BASEURL + l.fileUrl" :enable-preview="true" />
                        </view>
                        <uploadButton v-if="params.taskState !== '3'" @getFileObjList="getFilelist"></uploadButton>
                      </view> -->
                    </view>
                  </view>
                  <view v-else>
                    <view class="wrap-children">
                      <view style="font-size: 14px; color: #3d3d3d">
                        {{ $item.checkContent }}
                        <span style="color: #0256ff" @click="goDetailHidden($item.checkId)">
                          详情>
                        </span>
                      </view>
                    </view>
                    <view class="wrap-btn">
                      <wd-button :disabled="params.taskState === '3'"
                        style="min-width: 0; margin-top: 1rem; border-radius: 7px" size="medium"
                        :class="[$item.hazardState === '1' ? 'isActive' : '']" @click="getStatus(1, $item.id, '')">
                        符合
                      </wd-button>
                      <wd-button :disabled="params.taskState === '3'"
                        :class="[$item.hazardState === '2' ? 'isActive' : '']"
                        @click="getStatus(2, $item.id, $item.checkId)"
                        style="min-width: 0; margin-left: 1rem; border-radius: 7px" size="medium">
                        不符合
                      </wd-button>
                      <wd-button :disabled="params.taskState === '3'"
                        :class="[$item.hazardState === '3' ? 'isActive' : '']"
                        style="min-width: 0; margin-left: 1rem; border-radius: 7px" size="medium"
                        @click="getStatus(3, $item.id, '')">
                        不适用
                      </wd-button>
                    </view>
                    <view v-if="$item.hazardState === '2'">
                      <view v-for="(k, p) in $item.hazardPlanTaskEvents" :key="p">
                        <wd-img v-for="(l, r) in k.hazardPlanTaskEventFiles" :key="r" style="width: 100%; height: 90%"
                          :src="VITE_PREVIEW_BASEURL + l.fileUrl" :enable-preview="true" />
                      </view>
                      <uploadButton v-if="params.taskState !== '3'" @getFileObjList="getFilelist"></uploadButton>
                    </view>
                  </view>
                </wd-collapse-item>
              </wd-collapse>
            </wd-tab>
          </wd-tabs>
        </view>
      </view>
      <div class="no-date" v-else>暂无数据</div>
    </scroll-view>
    <wd-popup v-model="show" position="bottom" @close="closePopup">
      <view class="v-popup">
        <view class="v-popup-title">常见隐患</view>
        <view style="
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding-right: 5px;
            border-bottom: 0.01rem solid #ebeef5;
          " v-for="(item, index) in troubleInfo" :key="item">
          <view class="v-popup-content">
            <text style="width: 10px">{{ index + 1 }}.</text>
            <view style="text-align: left">{{ item.essentialFactorDescribe }}</view>
          </view>
          <wd-button style="
              min-width: 80px;
              /* min-width: 0; */
              margin-left: auto;
              color: #fff;
              background: #0256ff;
              border-radius: 7px;
            " @click="goReport(item.id, item.zid)" size="medium">
            上报
          </wd-button>
        </view>
        <view class="v-popup-btn">
          <wd-button style="margin-left: 1rem; color: #fff; background: #0256ff; border-radius: 7px"
            @click="goReport('')" size="medium">
            新增隐患
          </wd-button>
          <wd-button style="margin-left: 1rem; color: #fff; background: #0256ff; border-radius: 7px"
            @click="show = false" size="medium">
            取消
          </wd-button>
        </view>
      </view>
    </wd-popup>
  </view>
</template>

<script lang="ts" setup>
import SafetyNavbar from '../../components/safety-navbar.vue'
import uploadButton from '@/components/upload/upload-button.vue'
import commonStatistics from '@/components/commonSelect/common-statistics.vue'
import { inspectList, inspectNeedNum, updateCheckStatus, inspectDetail } from '../featch'
import { useAppStore, usefileConfigStore, useUserStore } from '@/store'
import { debounce } from '@/utils'

// const VITE_PREVIEW_BASEURL = import.meta.env.VITE_PREVIEW_BASEURL

const usefileStore = usefileConfigStore()
const VITE_PREVIEW_BASEURL = ref()
const currentEnvInfo = useAppStore().appEnv
if (!currentEnvInfo.wychApp) {
  VITE_PREVIEW_BASEURL.value = usefileStore.fileUrl.fileUrlPrefixMiniProgram + '/'
} else {
  VITE_PREVIEW_BASEURL.value = usefileStore.fileUrl.fileUrlPrefix + '/'
}
// const VITE_PREVIEW_BASEURL = ref(usefileStore.fileUrl + '/')

// const VITE_PREVIEW_BASEURL = ref('')
// getbaseurl()
// // eslint-disable-next-line camelcase
// function getbaseurl() {
//   const dynamicsUrl = location.origin
//   if (location.protocol === 'https:') {
//     return (VITE_PREVIEW_BASEURL.value = dynamicsUrl + import.meta.env.VITE_PREVIEW_BASEURL_dev)
//   } else {
//     return (VITE_PREVIEW_BASEURL.value = import.meta.env.VITE_PREVIEW_BASEURL)
//   }
// }
const collapsevalue = ref<number[]>([1])
const numTotal = ref<any>([])
const searchVal = ref('')
const dataList = ref<any>([])
const show = ref<boolean>(false)
const showComm = ref<boolean>(true)
const tabValue = ref('1')
const troubleInfo = ref<any>({})
const userStore = useUserStore()
const userInfo: any = userStore.userInfo ? userStore.userInfo : {}
onLoad((options) => {
  params.taskId = options.taskId
  params.taskState = options.taskState
  unitIds.value = options.unitIds
  getDataList()
  getInspectNeedList()
})
onShow(() => {
  showComm.value = true
  getStatisticsData()
})
const unitIds = ref<string>('')
const params = reactive({
  unitId: userInfo.unitId,
  zhId: userInfo.zhId, // 租户
  taskState: '', // 任务状态
  taskId: '',
  checkId: '',
  id: '',
})
const commonStatisticsRef = ref()

const closePopup = () => {
  // console.log('closePopup')
  getStatisticsData()
}

const getStatisticsData = () => {
  setTimeout(() => {
    commonStatisticsRef.value?.update()
  }, 60)
}

const handleChange = debounce((val: any) => {
  searchVal.value = val.value
  getDataList()
}, 500)

// function handleChange(val: any) {
//   // console.log(val)
//   // paramsForm.value.pageNo = 1
//   // // console.log(paramsForm.value)
//   searchVal.value = val.value
//   getDataList()
//   // getInspectNeedList()
// }

// 查看详情
function goDetailHidden(checkId) {
  // 任务已完成
  if (params.taskState === '3') {
    uni.navigateTo({
      url: `/pages/task/productionInspect/detail?checkId=${checkId}&taskId=${params.taskId}&type=0`,
    })
  } else if (params.taskState === '2') {
    // 任务进行中
    uni.navigateTo({
      url: `/pages/task/productionInspect/detail?checkId=${checkId}&taskId=${params.taskId}&type=1`,
    })
  }
}

// 获取常见隐患项
function getDetail(id) {
  // 获取详情
  inspectDetail(id, '')
    .then((res: any) => {
      const d = res.data
      // // console.log('aaaaaaaaaa----->>>>>>>>>', res)
      troubleInfo.value = d.essentialFactorList.map((item) => {
        return {
          ...item,
          zid: d.id,
        }
      })
      // console.log(troubleInfo)
    })
    .finally(() => {
      // console.log(111)
    })
}

function goReport(id, zid = '') {
  // console.log(id)
  // taskid--任务id  FactorId--隐患信息id--有这个代表快捷上报不是快捷上报就不传  checkContentId检查项id
  let url
  // 非快捷上报
  if (!id) {
    url =
      '/pages/randomcheck/createhiddendanger?checkContentId=' +
      params.checkId +
      '&taskId=' +
      params.taskId +
      '&types=0'
  } else {
    // 快捷上报
    url =
      '/pages/randomcheck/createhiddendanger?checkContentId=' +
      params.checkId +
      '&FactorId=' +
      id +
      '&checkId=' +
      zid +
      '&taskId=' +
      params.taskId +
      '&types=0'
  }
  showComm.value = false
  uni.navigateTo({
    url,
  })
}
// 操作按钮
function getStatus(type, id, checkId) {
  params.id = id
  if (type === 2) {
    params.checkId = checkId
    show.value = true
    getDetail(params.checkId)
  }
  updateCheckStatus(params.id, type).then((res: any) => {
    if (res.code === 'success') {
      // console.log(res, '更新状态')
      getDataList() // 刷新详情数据
      getInspectNeedList() // 数量更新
    }
  })
}

// 列表详情
function getDataList() {
  inspectList({ taskId: params.taskId, paramKey: searchVal.value } as any)
    .then((res: any) => {
      // console.log(res, '这里是列表详情0000')
      if (res.code === 'success') {
        dataList.value = res.data
      }
      // else if (res.status === '401') {
      //   uni.showToast({
      //     icon: 'none',
      //     title: res.message,
      //   })
      // }
      // // console.log(111)
    })
    .finally(() => {
      // console.log(111)
    })
}

// 获取图片列表
function getFilelist(event) {
  // console.log(event)
  // reviewChecktModel.value.filePathList = event
}

function getInspectNeedList() {
  inspectNeedNum({ taskId: params.taskId, paramKey: searchVal.value } as any).then((res: any) => {
    if (res.code === 'success') {
      numTotal.value = res.data
    }
  })
}
</script>

<style lang="scss" scoped>
.no-date {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100px;
  color: rgb(125, 125, 125);
}

body {
  background-color: #f9f9f9 !important;
}

.container {
  height: 100%;
  overflow: auto;
}

.item {
  height: 50px;
  line-height: 50px;
  text-align: center;
  border-bottom: 1px solid #ccc;
}

.wrap-children {
  padding: 0.5rem;
  margin: 0.5rem 1rem;
  background: #ebeef5;
  border-radius: 0.5rem;
}

.content-view {
  display: flex;
  justify-content: space-around;
  padding: 0.7rem 0;

  .wrap {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 0.6rem;
    margin-left: 0.625rem;
    border-radius: 0.3rem;
  }

  .bg1 {
    color: #0256ff;
    background: rgba(2, 86, 255, 0.1);
  }

  .bg2 {
    color: #f59a23;
    background: rgba(245, 154, 35, 0.1);
  }

  .bg3 {
    color: #e23b50;
    background: rgba(226, 59, 80, 0.1);
  }

  .bg4 {
    color: #bfbf00;
    background: rgba(191, 191, 0, 0.1);
  }
}

.v-popup {
  text-align: center;

  .v-popup-title {
    padding: 1rem;
    font-size: 18px;
    border-bottom: 0.01rem solid #ebeef5;
  }

  .v-popup-content {
    display: flex;
    align-items: center;
    // justify-content: space-between;
    justify-content: flex-start;
    width: 70%;
    padding: 0.6rem;
    // border-bottom: 0.01rem solid #ebeef5;
  }

  .v-popup-btn {
    display: flex;
    flex-direction: column;
    padding: 0.7rem;

    .wd-button {
      padding: 1rem;
      margin: 0.5rem 0;
    }
  }
}

.titbefor {
  position: absolute;
  width: 0.2rem;
  height: 1rem;
  margin: 1rem 0 1rem 0.5rem;
  background-color: blue;
  border-radius: 2px;
}

.wrap-btn {
  margin: 0 0 1rem 1rem;
}

.custom-title {
  padding: 0 !important;
}

.infobox {
  margin-top: 0.625rem;
  margin-bottom: 0.625rem;
}

.box {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  justify-content: space-between;
  padding: 1.125rem;

  .box-itme {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 10.25rem;
    height: 4.375rem;
    margin: 0.3125rem;
    background-color: rgba(188, 201, 218, 0.2);
    border-radius: 0.75rem;

    .imgbox {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 2.25rem;
      height: 2.25rem;

      .img {
        width: 100%;
        height: 100%;
      }
    }
  }

  .box-itme2 {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-around;
    width: 21.1875rem;
    height: 3.4375rem;
    margin: 0.3125rem;
    background-color: rgba(188, 201, 218, 0.2);
    border-radius: 12px;
  }
}

::v-deep {
  .wd-collapse-item__arrow {
    color: #0052d9;
  }

  .wd-collapse-item__body {
    padding: 0 !important;
  }

  .wd-search__input {
    height: 2.7rem;
  }

  .wd-tabs__nav {
    padding: 0 0.7rem;
  }

  .wd-popup {
    border-radius: 1.5rem 1.5rem 0 0;
  }

  .upload_btn {
    padding: 0.5rem 0.5rem 1rem 0.5rem;
  }

  .wd-button.is-medium.is-round {
    min-width: 0;
  }
}

.wd-button.is-primary {
  color: #0052d9;
  background: #fff;
  border: 1px solid #0052d9;
}

.wd-button.is-primary.isActive {
  color: #fff;
  background: #0052d9;
}
</style>
