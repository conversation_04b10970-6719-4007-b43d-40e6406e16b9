<!--
 * @Author: 悦悦 <EMAIL>
 * @Date: 2024-09-26 13:56:43
 * @LastEditors: 悦悦 <EMAIL>
 * @LastEditTime: 2024-09-27 09:10:37
 * @FilePath: /隐患排查app/src/pages/task/components/custom-steps.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <view class="container">
    <view v-for="(item, index) in stepsData" :key="index" style="position: relative">
      <view class="stage">
        <text class="stage-title">{{ item.title }}</text>
        <view
          class="circle"
          v-if="index === 0"
          :class="{
            finish: item.taskState == 1,
            proceed: item.taskState == 2 || item.taskState == 3,
          }"
        >
          {{ index + 1 }}
        </view>
        <view
          class="circle"
          v-else-if="index === 1"
          :class="{ finish: item.taskState == 2, proceed: item.taskState == 3 }"
        >
          {{ index + 1 }}
        </view>
        <view class="circle" v-else-if="index === 2" :class="{ finish: item.taskState == 3 }">
          {{ index + 1 }}
        </view>
        <!-- 【app检查任务】任务详情，计划下发、任务执行节点时间应精确到时分秒 -->
        <text class="stage-date" style="margin-top: 0.375rem">
          {{ item.dateinfo ? item.dateinfo.split(' ')[0] : '-' }}
        </text>
        <text class="stage-date">{{ item.dateinfo && item.dateinfo.split(' ')[1] }}</text>
      </view>
      <view
        v-if="index === 0"
        class="line"
        :class="{ finish_line: item.taskState == 2 || item.taskState == 3 }"
      ></view>
      <view
        v-else-if="index === 1"
        class="line"
        :class="{ finish_line: item.taskState == 3 }"
      ></view>
    </view>
  </view>
</template>

<script lang="ts" setup>
defineProps({
  stepsData: {
    type: Array<{
      id: number
      title: string
      dateinfo: string
      state: number
    }>,
    default: [],
  },
})
</script>

<style lang="scss" scoped>
.container {
  display: flex;
  flex-direction: row;
  // align-items: center;
  justify-content: space-evenly;
}

.stage {
  display: flex;
  flex-direction: column;
  align-items: center;
  // margin-bottom: 20px;
}

.stage-title {
  margin-bottom: 5px;
  font-size: 0.875rem;
  // font-weight: bold;
}
.circle {
  width: 1.75rem;
  height: 1.75rem;
  font-size: 1.125rem;
  line-height: 1.75rem;
  color: white;
  text-align: center;
  background-color: #dfdfdf;
  border: 3px solid #dfdfdf;
  border-radius: 50%;
}
.stage-date {
  font-size: 14px;
  color: #3775f6;
}
.line {
  position: absolute;
  top: 2.7rem;
  // margin-top: -2.7rem;
  // margin-left: 4.5rem;
  left: 4.5rem;
  width: 2.5rem;
  border: 0.5px dashed #3775f6;
}
.linenone {
  display: none;
}
.finish_line {
  border: 0.5px solid #3775f6;
}

// 已完成
.proceed {
  color: #3775f6;
  background-color: white;
  border: 3px solid #3775f6;
}
// 进行中
.finish {
  color: white;
  background-color: #3775f6;
  border: 3px solid #3775f6;
}
</style>
