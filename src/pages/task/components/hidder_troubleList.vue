<!--
 * @Author: 悦悦 <EMAIL>
 * @Date: 2024-09-24 19:32:38
 * @LastEditors: 悦悦 <EMAIL>
 * @LastEditTime: 2024-09-29 19:33:09
 * @FilePath: /隐患排查app/src/pages/task/components/hidder_troubleList.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <view class="container" v-if="troubleinfo.length > 0">
    <view class="infobox">
      <view class="infobox_title">
        <view class="fasticon"></view>
        <view>隐患信息</view>
      </view>
      <view class="infoitme" v-for="(itme, index) in troubleinfo" :key="index">
        <view class="infoitme_title" style="margin-bottom: 14px">
          <view class="infoitme_key">隐患分类</view>
          <view class="infoitme_descript">{{ itme.essentialFactorClass }}</view>
          <!-- #E23B50---重大；#F59A23----一般一级；#BFBF00---一般二级；#76B90E----一般三级----- -->
          <view class="infoitme_title_tag" style="background-color: #ff9908">
            {{ itme.essentialFactorGrade }}
          </view>
        </view>
        <!-- <view class="infoitme_descript">{{ itme.essentialFactorClass }}</view> -->
        <view class="infoitme_title">
          <view class="infoitme_key">隐患描述</view>
          <view class="infoitme_descript">
            {{ itme.essentialFactorDescribe }}
          </view>
          <view style="display: flex; gap: 8px" class="infoitme_title_tag2">
            <view
              style="color: #0256ff"
              @click="goReport(itme.checkAreaId, itme.id)"
              v-if="type === '1'"
            >
              上报
            </view>
            <!-- <view style="color: #0256ff" @click="goLegend(itme)">图例</view> -->
          </view>
        </view>
        <!-- <view class="infoitme_descript">
          {{ itme.essentialFactorDescribe }}
        </view> -->
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
const props = defineProps<{
  troubleinfo: any[]
  taskId: string
  type: string
}>()
// checkAreaEssentialFactorId=1838399120060268545&id=1838398308785405953
function goReport(id, factorId) {
  uni.navigateTo({
    url: `/pages/randomcheck/createhiddendanger?checkContentId=${id}&checkId=${id}&FactorId=${factorId}&taskId=${props.taskId}&types=0`,
  })
}
const goLegend = (data) => {
  uni.navigateTo({
    url: `/pages/randomcheck/legend?checkAreaFileList=${JSON.stringify(data.checkAreaFileList)}`,
  })
}
</script>

<style lang="scss" scoped>
body {
  background-color: #f9f9f9 !important;
}

.infobox {
  padding-bottom: 1rem;
  margin-bottom: 1rem;
  background-color: white;

  .infobox_title {
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 0.25rem 0 0.9375rem 1.125rem;

    .fasticon {
      width: 0.25rem;
      height: 0.875rem;
      margin-right: 0.1875rem;
      background-color: #597bf7;
      border-radius: 1.125rem;
    }
  }

  .infoitme {
    padding: 0.5rem;
    margin: 0.5rem 0.9375rem 1.5rem;
    font-size: 0.875rem;
    background-color: #ebeef5;
    border-radius: 0.5rem;

    .infoitme_title {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .infoitme_key {
        // flex: 1;
        min-width: 75px;
        color: #7f7f7f;
      }

      .infoitme_descript {
        // flex: 2;
        min-width: 95px;
      }

      .infoitme_title_tag {
        min-width: 90px;
        padding: 0.1rem;
        margin-left: auto;
        overflow: hidden;
        font-size: 12px;
        line-height: 24px;
        color: white;
        text-align: center;
        text-overflow: ellipsis;
        white-space: nowrap;
        border-radius: 0.25rem;
      }

      .infoitme_title_tag2 {
        min-width: 45px;
        margin-left: auto;
        line-height: 24px;
        text-align: center;
      }
    }

    .infoitme_descript {
      // margin-top: 10px;
    }
  }
}
</style>
