<!--
 * @Author: 悦悦 <EMAIL>
 * @Date: 2024-09-24 11:23:13
 * @LastEditors: 悦悦 <EMAIL>
 * @LastEditTime: 2024-09-24 14:36:45
 * @FilePath: /隐患排查app/src/pages/dangerlist/components/custom-list.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <view class="muenitme" v-for="(item, index) in props.initialData" :key="index"
    @click="openDetail(item.id, item.disposeId, item.flag)">
    <view class="field1" style="padding-top: 1rem">
      <view style="font-size: 1rem; font-weight: bold; color: #000000; width: 70%">
        {{ item.hazardDesc }}
      </view>
      <view>
        <wd-tag style="min-width: 4rem; font-size: 0.675rem; text-align: center;padding: 0.3rem 0rem;" type="warning"
          mark>
          {{ item.hazardLevelName }}
        </wd-tag>
      </view>
    </view>
    <view class="field1">
      <view>{{ item.hazardSourceName }}</view>
      <view>{{ item.hazardTypeName }}</view>
    </view>
    <view class="field">
      <view>{{ item.hazardPosition }}</view>
    </view>
    <view class="field">
      <view>检查人：{{ item.createByName || '--' }}</view>
    </view>
    <view class="field">
      <view>检查时间：{{ item.createTime }}</view>
    </view>
  </view>
</template>

<script lang="ts" setup>
const props = defineProps<{
  initialData: any[]
  taskState?: string
}>()
function openDetail(id, disposeId, flag) {
  // uni.navigateTo({
  //   url: `/pages/dangerlist/detail?id=${id}&flag=${flag}`,
  // })
  uni.navigateTo({
    url: `/pages/dangerlist/detail?id=${id}&disposeId=${disposeId}&flag=${flag}`,
  })
}
</script>

<style lang="scss" scoped>
.muenitme {
  width: 21.5625rem;
  // height: 11.25rem;
  margin: 0.9375rem;
  background-color: #ebeef5;
  border-radius: 0.6rem;

  .field1 {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.5rem;
    font-size: 0.875rem;
  }

  .field {
    padding: 0.2rem 0.5rem;
    font-size: 0.875rem;
  }
}
</style>
