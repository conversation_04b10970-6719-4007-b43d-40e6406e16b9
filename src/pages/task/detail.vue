<route lang="json5">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '任务详情',
  },
}
</route>

<template>
  <SafetyNavbar title="任务详情"></SafetyNavbar>
  <view class="container">
    <view class="cus-steps">
      <customSteps :stepsData="stepsData"></customSteps>
    </view>
    <scroll-view class="scroll-content" scroll-y="true">
      <!-- 任务信息 -->
      <view class="infobox">
        <wd-collapse v-model="collapsevalue">
          <view class="titbefor"></view>
          <wd-collapse-item class="custom-title" title="任务信息" name="item1" open="true">
            <wd-cell-group title="" border>
              <wd-cell title="检查类型" :value="info.planTypeName"></wd-cell>
              <wd-cell title="检查频次" v-if="info.frequencyType === '99'" value="不重复"></wd-cell>
              <wd-cell
                title="检查频次"
                v-if="info.frequencyType !== '99'"
                :value="
                  '每' +
                  info.frequency +
                  (info.frequencyType === '0'
                    ? '时'
                    : info.frequencyType === '1'
                      ? '日'
                      : info.frequencyType === '2'
                        ? '周'
                        : info.frequencyType === '3'
                          ? '月'
                          : info.frequencyType === '4'
                            ? '季度'
                            : info.frequencyType === '5'
                              ? '年'
                              : '')
                "
              ></wd-cell>
              <!-- <wd-cell
                title="检查频次"
                v-if="info.frequencyType !== '99' && info.frequencyType === '0'"
                :value="'每时' + info.frequency + '次'"
              ></wd-cell>
              <wd-cell
                title="检查频次"
                v-if="info.frequencyType !== '99' && info.frequencyType === '1'"
                :value="'每日' + info.frequency + '次'"
              ></wd-cell>
              <wd-cell
                title="检查频次"
                v-if="info.frequencyType !== '99' && info.frequencyType === '2'"
                :value="'每周' + info.frequency + '次'"
              ></wd-cell>
              <wd-cell
                title="检查频次"
                v-if="info.frequencyType !== '99' && info.frequencyType === '3'"
                :value="'每月' + info.frequency + '次'"
              ></wd-cell>
              <wd-cell
                title="检查频次"
                v-if="info.frequencyType !== '99' && info.frequencyType === '4'"
                :value="'每季度' + info.frequency + '次'"
              ></wd-cell>
              <wd-cell
                title="检查频次"
                v-if="info.frequencyType !== '99' && info.frequencyType === '5'"
                :value="'每年' + info.frequency + '次'"
              ></wd-cell> -->

              <wd-cell title="创建人" :value="info.createByName"></wd-cell>
              <wd-cell title="检查对象" :value="info.unitNames"></wd-cell>
              <wd-cell
                title="计划起止时间"
                :value="info.planStartDate + '-' + info.planEndDate"
              ></wd-cell>
              <wd-cell
                title="任务起止时间"
                :value="
                  (info.planStartTime ? info.planStartTime + '-' : '') +
                  (info.planEndTime ? info.planEndTime : ' ')
                "
              ></wd-cell>
            </wd-cell-group>
          </wd-collapse-item>
        </wd-collapse>
      </view>
      <!-- 检查人员信息 -->
      <view class="infobox">
        <wd-collapse v-model="collapsevalue">
          <view class="titbefor"></view>
          <wd-collapse-item class="custom-title" title="检查人员信息" name="item2">
            <wd-cell-group title="" border>
              <wd-cell title="检查负责人" :value="info.fzrs"></wd-cell>
              <wd-cell title="检查参与人" :value="info.cyrs"></wd-cell>
            </wd-cell-group>
          </wd-collapse-item>
        </wd-collapse>
      </view>
      <!-- 隐患检测情况 -->
      <wd-collapse v-model="collapsevalue">
        <view class="titbefor"></view>
        <wd-collapse-item
          class="custom-title"
          v-if="(clockIn && info.isNeedClock === '1') || info.taskState !== '1'"
          title="隐患检查情况"
          name="item4"
        >
          <commonStatistics
            v-if="info.taskState"
            ref="commonStatisticsRef"
            @click="gocheckHidden(info.taskState)"
            :type="0"
            :createBy="userInfo.id"
            :isPlanTask="info.taskState == '2'"
            :taskId="taskId"
            :unitId="''"
            :topUnitId="''"
          ></commonStatistics>
        </wd-collapse-item>
      </wd-collapse>

      <!-- 检查表 -->
      <view class="infobox">
        <wd-collapse v-model="collapsevalue">
          <view class="titbefor"></view>
          <wd-collapse-item class="custom-title" title="检查表" name="item5">
            <wd-cell
              :title="info.checkTable"
              :value="'检查内容:' + info.checkItemNum + '项目'"
              is-link
              @click="goCheckTable(info.taskState)"
            ></wd-cell>
          </wd-collapse-item>
        </wd-collapse>
      </view>
      <!-- 检查打卡记录 -->
      <view class="infobox" v-if="info.taskState !== '1' && info.isNeedClock === '1'">
        <wd-cell title="检查打卡记录" is-link @click="goCheckRecord"></wd-cell>
      </view>
    </scroll-view>
    <!-- 底部按钮 -->
    <view v-if="tabindex === '0'" class="fixed-bottom">
      <!-- 开始任务 现场打卡 继续检查 结束任务 -->
      <wd-button
        style="min-width: 0; border-radius: 4px"
        size="medium"
        v-if="
          info.taskState === '1' &&
          (arrayContainsValue(info.fzrsIds, userInfo.id) ||
            arrayContainsValue(info.cyrsIds, userInfo.id))
        "
        @click="getInspectionBtn"
      >
        开始任务
      </wd-button>
      <!--   isNeedClock===1才需要打卡   -->
      <wd-button
        style="min-width: 0; border-radius: 4px"
        size="medium"
        @click="clockInAPI"
        v-if="
          info.taskState === '2' &&
          info.isNeedClock === '1' &&
          !clockIn &&
          (arrayContainsValue(info.fzrsIds, userInfo.id) ||
            arrayContainsValue(info.cyrsIds, userInfo.id))
        "
      >
        现场打卡
      </wd-button>

      <wd-button
        style="min-width: 0; border-radius: 4px"
        size="medium"
        @click="goCheckInspect"
        v-if="
          ((info.taskState === '2' && info.isNeedClock !== '1') ||
            (info.taskState === '2' && info.isNeedClock === '1' && clockIn)) &&
          (arrayContainsValue(info.fzrsIds, userInfo.id) ||
            arrayContainsValue(info.cyrsIds, userInfo.id))
        "
      >
        继续检查
      </wd-button>

      <wd-button
        @click="goDetail"
        v-if="info.taskState === '3'"
        style="min-width: 0; border-radius: 4px"
        plain
        hairline
        size="medium"
      >
        任务详情
      </wd-button>
      <wd-button
        style="min-width: 0; border-radius: 4px"
        plain
        hairline
        size="medium"
        @click="checkHistoryHidden"
      >
        检查对象历史隐患
      </wd-button>
      <wd-button
        @click="goEnd"
        :loading="endLoading"
        v-if="
          ((info.taskState === '2' && clockIn) ||
            (info.taskState === '2' && info.isNeedClock !== '1')) &&
          ((info.checkEndOperate === '2' && arrayContainsValue(info.cyrsIds, userInfo.id)) ||
            arrayContainsValue(info.fzrsIds, userInfo.id))
        "
        style="min-width: 0; border-radius: 4px"
        size="medium"
        type="error"
      >
        结束任务
      </wd-button>
    </view>
  </view>

  <wd-message-box selector="wd-message-box-slot">
    <!-- <template #message>
      <text>是否确认结束任务？</text>
    </template> -->
    <template #buttons>
      <wd-button type="primary" @click="() => {}">我知道了</wd-button>
    </template>
  </wd-message-box>
</template>

<script lang="ts" setup>
import commonStatistics from '@/components/commonSelect/common-statistics.vue'
import { useAppStore, useUserStore } from '@/store'
import { useMessage } from 'wot-design-uni'
import SafetyNavbar from '../components/safety-navbar.vue'
import customSteps from './components/custom-steps.vue'
import {
  getHazardTaskShortInfo,
  getTaskClockInOne,
  inspectNeedNum,
  PostcompletionTaskAPI,
  taskDetail,
  taskInspectionBtn,
} from './featch'
const message = useMessage()
const userStore = useUserStore()
const userInfo: any = userStore.userInfo ? userStore.userInfo : {}
const collapsevalue = ref([])
const stepsData = ref([])
const taskId = ref('')
const info = ref<any>({})
const clockIn = ref<boolean>(false)
const typeList = ref('0')
const commonStatisticsRef = ref()
// function arrayContainsValue(arr, value: string) {
//   // // console.log(arr, Object.prototype.toString.call(arr), '详情==是否是整改人===========arr')
//   if (arr?.length > 0 && arr !== null) {
//     return arr.includes(value)
//   } else {
//     return false
//   }
// }
const refreshKey = ref(0)
const tabindex = ref()
onLoad(async (options) => {
  // 存储planId
  uni.setStorageSync('planId', options.planId)
  uni.setStorageSync('taskIdByindex', options.taskId)
  taskId.value = options.taskId
  tabindex.value = options.tabindex
  console.log(tabindex.value, '===========tabindex.value')

  await getDataList()

  await getTaskClockInOneAPI({
    taskId: taskId.value,
    userId: userInfo.id,
  })
  console.log('onLoad执行中')
  // await updateStatistics()
  if (commonStatisticsRef.value) {
    console.log('onLoad调用commonStatisticsRef.value.update()')
    commonStatisticsRef.value.update()
  }
  // 12981 【app督察任务】任务详情页面，任务信息显示不全
  if (!collapsevalue.value.length) collapsevalue.value = ['item1', 'item2', 'item4', 'item5']
  // location.reload()
  console.log('onLoad执行完了')
})
onMounted(() => {
  getTaskClockInOneAPI({
    taskId: taskId.value,
    userId: userInfo.id,
  })
  updateStatistics()
})
onShow(() => {
  getTaskClockInOneAPI({
    taskId: taskId.value,
    userId: userInfo.id,
  })
  updateStatistics()
})
// const params = reactive<any>({
//   createByName: '管理员',
//   unitId: userInfo.unitId,
//   zhId: userInfo.zhId, // 租户
// })

// 点击按钮获取检查项详情
function goDetail() {
  let url
  if (info.value.checkExecuteMethod === '1') {
    url = `/pages/dangerlist/index?taskState=${info.value.taskState}&taskid=${taskId.value}`
    // url = `/pages/task/productionInspect/index?taskId=${taskId.value}&taskState=${info.value.taskState}`
  } else {
    url = `/pages/task/productionInspect/indexNeed?taskId=${taskId.value}&taskState=${info.value.taskState}`
  }
  uni.navigateTo({
    url,
  })
}
const endLoading = ref(false)
// 结束任务
const goEnd = () => {
  // 判断当前用户是否是检查负责人，只有负责人能结束任务
  // if (info.value.checkEndOperate === '1' && !arrayContainsValue(info.value.fzrsIds, userInfo.id)) {
  //   uni.showToast({
  //     icon: 'none',
  //     title: '您没有权限哦！负责人才能结束任务',
  //   })
  //   return
  // }
  uni.showModal({
    title: '提示',
    content: '确认要结束任务吗?结束任务后会上报检查出的隐患',
    success: async function (res) {
      if (res.confirm) {
        // console.log('用户点击确定')
        // 在这里执行确定后的操作
        // 若检查任务中有未完成的检查项，则无法结束任务
        endLoading.value = true
        try {
          const res: any = await inspectNeedNum({ taskId: taskId.value, paramKey: '' })
          if (res.code === 'success') {
            const d = res.data
            // 需要逐一检查且未完成检查项数量不等于0
            if (info.value.checkExecuteMethod === '2' && d.noChecked !== 0) {
              uni.showModal({
                showCancel: false, // 不显示取消按钮
                title: '提示',
                content: '任务中还有未检查项，请全部检查完毕再提交',
                success: function (res) {
                  if (res.confirm) {
                    // console.log('用户点击确定')
                  }
                },
              })
            } else {
              const res: any = await PostcompletionTaskAPI(taskId.value, userInfo.id)
              if (res.code === 'success') {
                uni.showModal({
                  title: '提示',
                  content: res.message,
                  showCancel: false, // 是否显示取消按钮
                  confirmText: '我知道了', // 确认按钮的文字，默认为"确定"
                  success: function (res) {
                    if (res.confirm) {
                      uni.navigateBack()
                      // 确定按钮的逻辑
                    }
                  },
                })
                // uni.showToast({
                //   icon: 'none',
                //   title: res.message,
                // }) // 结束状态 更新页面
                // getDataList()
              } else {
                uni.showModal({
                  title: '提示',
                  content: res.message,
                  showCancel: false, // 是否显示取消按钮
                  confirmText: '我知道了', // 确认按钮的文字，默认为"确定"
                  success: function (res) {
                    if (res.confirm) {
                      uni.navigateBack()
                      // 确定按钮的逻辑
                    }
                  },
                })
              }
            }
          }
        } catch (error) {}
        endLoading.value = false
      } else if (res.cancel) {
        // 在这里执行取消后的操作
      }
    },
  })
}
function arrayContainsValue(arr, value: string) {
  if (arr) {
    arr = arr.split(',')
    return arr.includes(value)
  } else {
    return false
  }
}
// 下钻隐患清单
function checkHistoryHidden() {
  const planId = uni.getStorageSync('planId')
  // 查看检查对象所有隐患历史，不需要整改人id
  // uni.navigateTo({
  //   url: `/pages/dangerlist/index?unitId=${info.value.unitIds}`,
  // // const planId = uni.getStorageSync('planId')
  uni.navigateTo({
    url: `/pages/task/historyList?taskId=${taskId.value}&taskState=${info.value.taskState}&unitIds=${info.value.unitIds}`,
  })
}

// 按钮操作
function getInspectionBtn() {
  taskInspectionBtn(taskId.value, '003')
    .then((res) => {
      if ((res.code as any) === 'success') {
        // console.log(res.data, 'res.data')
        getDataList()
      }
    })
    .finally(() => {
      // console.log(111)
    })
}

// 隐患检测情况
function gocheckHidden(taskState) {
  uni.navigateTo({
    url: `/pages/dangerlist/index?taskState=${taskState}&taskid=${taskId.value}`,
  })
}

// 跳转安全检查表---任务详情获取
function goCheckTable(taskState) {
  uni.navigateTo({
    url: `/pages/task/productionInspect/index?type=1&taskId=${taskId.value}&taskState=${taskState}&checkTableId=${info.value.checkTableId}`,
  })
}
// const { appEnv } = useAppStore()
const currentEnvInfo = useAppStore().appEnv
// 重新打卡/现场打卡
function clockInAPI() {
  // checkExecuteMethod 1不需要逐一/2需要逐一
  // isNeedClock 1现场打卡 2否
  // // console.log(info.value, 'info里面的数据是多少啊-----')
  // printRange
  const url = currentEnvInfo.wychApp
    ? `/pages/task/clockIn2?taskId=${taskId.value}&lat1=${info.value.unitPointY}&lon1=${info.value.unitPointX}&range=${info.value.printRange}`
    : `/pages/task/clockIn?taskId=${taskId.value}&lat1=${info.value.unitPointY}&lon1=${info.value.unitPointX}&range=${info.value.printRange}`
  // const url = `/pages/task/clockIn2?taskId=${taskId.value}&lat1=${info.value.unitPointY}&lon1=${info.value.unitPointX}&range=${info.value.printRange}`
  uni.navigateTo({
    url,
  })
}
// 继续检查/现场打卡
async function goCheckInspect() {
  // checkExecuteMethod 1不需要逐一/2需要逐一
  // isNeedClock 1现场打卡 2否
  // console.log(info, 'info里面的数据是多少啊-----')
  try {
    const params = {
      id: taskId.value,
    }
    const res: any = await getHazardTaskShortInfo(params)
    if (res.code === 'success') {
      if (+res.data?.delFlag === 1) {
        uni.showModal({
          title: '提示',
          content: '任务已失效，可能被计划发起人停用或删除。',
          showCancel: false, // 是否显示取消按钮
          confirmText: '我知道了', // 确认按钮的文字，默认为"确定"
          success: function (res) {
            if (res.confirm) {
              uni.navigateBack()
            }
          },
        })
      } else {
        clockIn.value = false
        let url
        if (info.value.checkExecuteMethod === '1') {
          url = `/pages/task/productionInspect/index?&taskId=${taskId.value}&taskState=${info.value.taskState}&checkTableId=${info.value.checkTableId}`
        } else {
          url = `/pages/task/productionInspect/indexNeed?taskId=${taskId.value}&taskState=${info.value.taskState}&checkTableId=${info.value.checkTableId}`
        }

        uni.navigateTo({
          url,
        })
      }
    } else {
      uni.showToast({
        icon: 'none',
        title: res.message,
      })
    }
  } catch (error) {}
}

// 打卡记录页面
function goCheckRecord() {
  // fzrsIds
  // // console.log(info.value, 'fzrsIds')
  const fzrsIds = info.value.fzrsIds.split(',')
  // fzrsIds.includes(userInfo.id) 是负责人 不传当前人id 否者传当前人id
  // // console.log(fzrsIds)
  uni.navigateTo({
    url: `/pages/task/checkRecord?taskId=${taskId.value}&userinfoId=${fzrsIds.includes(userInfo.id) ? '' : userInfo.id}`,
  })
}

// const updateStatistics = () => {
//   // console.log('123123123123', commonStatisticsRef.value)
//   setTimeout(commonStatisticsRef.value?.update)
// }
const updateStatistics = async () => {
  if (commonStatisticsRef.value) {
    commonStatisticsRef.value.update()
  }
}

const unitList = ref([])
// 任务详情
async function getDataList() {
  const res = await taskDetail(taskId.value)
  if ((res.code as any) === 'success') {
    refreshKey.value += 1
    info.value = res.data
    // 获取当下任务的检查对象
    unitList.value = info.value.unitNames.split(',').map((item, index) => {
      return {
        unitName: item,
        id: info.value.unitIds.split(',')[index],
      }
    })
    uni.setStorageSync('unitList', unitList.value)
    // console.log(unitList.value, 'unitList.value')
    /* state 0未选中 1已选中 2已完成
      taskState 1待开始 2进行中 3已完成 */
    stepsData.value = [
      {
        id: 1,
        title: '计划下发',
        dateinfo: info.value.createTime,
        state: info.value.taskState === '1' ? 0 : 2,
        taskState: info.value.taskState,
      },
      {
        id: 2,
        title: '任务执行',
        dateinfo: info.value.beginTime,
        //  state: info.value.taskState === '2' ? 1 : info.value.taskState === '1' ? 0 : 2,
        state: info.value.taskState === '2' ? 1 : 2,
        taskState: info.value.taskState,
      },
      {
        id: 3,
        title: '任务完成',
        dateinfo: info.value.finishTime,
        state: info.value.taskState === '3' ? 2 : 0,
        taskState: info.value.taskState,
      },
    ]
    updateStatistics()
  }
  // await updateStatistics()
}

// 请求最近一条打卡记录
const getTaskClockInOneAPI = (params) => {
  getTaskClockInOne(params).then((res: any) => {
    if (res.data.id) {
      clockIn.value = true
    } else {
      clockIn.value = false
    }
  })
}
</script>

<style lang="scss" scoped>
body {
  background-color: #f9f9f9 !important;
}

.container {
  height: 100%;
}

.scroll-content {
  /* 减去底部固定区域的高 */
  // height: calc(100vh - 16.375rem);
  padding-bottom: 4rem;
}

.item {
  height: 50px;
  line-height: 50px;
  text-align: center;
  border-bottom: 1px solid #ccc;
}

.fixed-bottom {
  position: fixed;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: space-around;
  width: 100%;
  height: 4rem;
  line-height: 4rem;
  color: #fff;
  background-color: white;
}

.cus-steps {
  padding: 0.625rem 0;
  background-color: white;
}

// .titbefor {
//   position: absolute;
//   width: 0.2rem;
//   height: 1rem;
//   margin: 1rem 0 1rem 0.5rem;
//   background-color: blue;
//   border-radius: 2px;
// }
.custom-title {
  padding: 0 !important;
}

.infobox {
  margin-top: 0.625rem;
  margin-bottom: 0.625rem;
}

.box {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  justify-content: space-between;
  padding: 1.125rem;

  .box-itme {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 10.25rem;
    height: 4.375rem;
    margin: 0.3125rem;
    background-color: rgba(188, 201, 218, 0.2);
    border-radius: 0.75rem;

    .imgbox {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 2.25rem;
      height: 2.25rem;

      .img {
        width: 100%;
        height: 100%;
      }
    }
  }

  .box-itme2 {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-around;
    width: 21.1875rem;
    height: 3.4375rem;
    margin: 0.3125rem;
    background-color: rgba(188, 201, 218, 0.2);
    border-radius: 12px;
  }
}

::v-deep {
  .wd-collapse-item__arrow {
    color: #0052d9;
  }

  .wd-collapse-item__body {
    padding: 0 !important;
  }
}
.custom-shadow {
  display: none;
}
</style>
