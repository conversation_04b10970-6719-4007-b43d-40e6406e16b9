<route lang="json5">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '历史隐患',
  },
}
</route>

<template>
  <z-paging ref="paging" v-model="initialData" @query="GetLoadList">
    <template #top>
      <SafetyNavbar title="历史隐患"></SafetyNavbar>
      <CustomTabs :tabs="tabs" :activeIndex="activeIndex" @handleClick="handleChange"></CustomTabs>
      <view class="tabs-content">
        <dorpMenu :orgRes="userInfo.orgRes" @getGrade="getGradevalue" @getUnit="getUnitvalue"
          :isShowdisposeState="false"></dorpMenu>
        <hr style="border: 0.3px solid rgb(235, 235, 235)" />
        <wd-search placeholder-left @change="search" placeholder="请输入隐患位置/类型模糊搜索" :hide-cancel="true"
          v-model="parameDataModel.likeFieldValue" @clear="clear" />
      </view>
    </template>
    <view>
      <div v-if="activeIndex === 0">
        <customList :initialData="initialData"></customList>
      </div>
      <div v-if="activeIndex === 1">
        <customList :initialData="initialData"></customList>
      </div>
    </view>
  </z-paging>
</template>

<script lang="ts" setup>
import SafetyNavbar from '../components/safety-navbar.vue'
import CustomTabs from '../components/custom-Tabs.vue'
import dorpMenu from '@/components/commonSelect/dorp-menu.vue'
import customList from './components/custom-list.vue'
import { hazardPlanTaskEventAPI, posthazardMegerpageEventAPI, postpageEventAPI } from './featch'
import { useUserStore } from '@/store'
const userStore = useUserStore()
const userInfo: any = userStore.userInfo ? userStore.userInfo : {}
const tabs = ['待整改隐患', '历史隐患']
const activeIndex = ref(0)
const total = ref(0)
const noDate = ref<boolean>(false)
const parameDataModel = ref<any>({
  hazardLevel: '', // 隐患等级
  likeFields: 'hazardPosition,hazardSourceName,hazardTypeName',
  likeFieldValue: '',
  pageNo: 1,
  pageSize: 5,
  disposeState: 0,
  unitName: '',
  unitId: '',
  checkUnitId: '',
  likeParam: '',
  // roleCodes: userInfo.roleCodes,
  taskId: '',
  planId: '',
  //   任务id--查询未完成任务隐患记录使用
  randomCheckId: '',
})
const paging = ref()
const initialData = ref<any>([])
// const apiTag = ref<any>('')
// const taskState = ref<any>('')
onLoad((paramas) => {
  // // console.log(paramas)
  // apiTag.value = paramas.apiTag
  // parameDataModel.value.randomCheckId = paramas.taskId
  // // console.log(parameDataModel.value, '=======params')
  // if (userInfo.orgRes === '1') {
  //   parameDataModel.value.unitId = userInfo.unitId
  // } else {
  //   parameDataModel.value.unitId = userInfo.serverUnitId
  // }
  parameDataModel.value.unitId = paramas.unitIds
  if (paramas.objId) {
    parameDataModel.value.checkUnitId = paramas.objId
  }
  // GetLoadList()
  paging.value.reload()
  // if (paramas.apiTag === '0') {
  //   // 请求随机检查列表跳转》》》列表信息
  //   GetLoadList()
  // } else {
  //   // 检查列表跳转》》》列表信息
  //   // taskId/planId/randomCheckId
  //   // parameDataModel.value.planId = paramas.planId
  //   taskState.value = paramas.taskState
  //   // paramas.taskState === '3' ? (apiTag.value = '0') : (apiTag.value = '1')
  //   GetLoadList()
  //   // paramas.taskState === '3' ? GetLoadList() : GetLoadtaskList()
  // }
})
// 切换面板
function handleChange(event) {
  // // console.log(event)
  activeIndex.value = event
  parameDataModel.value.pageNo = 1
  event === 1 ? (parameDataModel.value.disposeState = 1) : (parameDataModel.value.disposeState = 0)
  // GetLoadList()
  paging.value.reload()
  // apiTag.value === '0' ? GetLoadList() : GetLoadtaskList()
}
// 下拉菜单
/* 等级进行筛选 */
function getGradevalue(event) {
  parameDataModel.value.pageNo = 1
  parameDataModel.value.hazardLevel = event.id === '0000' ? '' : event.id
  // GetLoadList()
  paging.value.reload()
  // apiTag.value === '0' ? GetLoadList() : GetLoadtaskList()
}
/* 单位进行筛选 */
function getUnitvalue(event) {
  parameDataModel.value.pageNo = 1
  parameDataModel.value.checkUnitId = event.id === '0000' ? '' : event.id
  // GetLoadList()
  paging.value.reload()
  // apiTag.value === '0' ? GetLoadList() : GetLoadtaskList()
}
// 检查列表跳转》》》列表信息
// function GetLoadtaskList() {
//   // console.log('检查列表跳转》》》列表信息')
//   hazardPlanTaskEventAPI(parameDataModel.value)
//     .then((res: any) => {
//       total.value = res.data.total
//       initialData.value = res.data.rows
//     })
//     .finally(() => {
//       // console.log(111)
//     })
// }
// 随机检查列表跳转》》》列表信息====任务已完成查询接口
function GetLoadList(pageNo) {
  parameDataModel.value.pageNo = pageNo
  // encodeURIComponent(parameDataModel.value.likeFieldValue)
  posthazardMegerpageEventAPI(parameDataModel.value)
    .then((res: any) => {
      total.value = res.data.total
      // paging.value.complete(res.data.rows)
      paging.value.completeByTotal(res.data.rows, total.value)
    })
    .finally(() => {
      // console.log(111)
    })
}
// 加载更多操作
function handleScrollToLower() {
  // total===数据的len 结束获取
  if (total.value === initialData.value.length) {
    return
  }
  setTimeout(() => {
    // ++ 更改页码
    parameDataModel.value.pageNo++
    // encodeURIComponent(parameDataModel.value.likeFieldValue)
    posthazardMegerpageEventAPI(parameDataModel.value)
      .then((res: any) => {
        if (res.data) {
          initialData.value = [...initialData.value, ...res.data.rows]
        } else {
          initialData.value = []
        }
      })
      .finally(() => {
        // console.log(111)
      })
    // if (apiTag.value === '0') {
    //   postpageEventAPI(parameDataModel.value)
    //     .then((res: any) => {
    //       if (res.data) {
    //         initialData.value = [...initialData.value, ...res.data.rows]
    //       } else {
    //         initialData.value = []
    //       }
    //     })
    //     .finally(() => {
    //       // console.log(111)
    //     })
    // } else {
    //   hazardPlanTaskEventAPI(parameDataModel.value)
    //     .then((res: any) => {
    //       if (res.data) {
    //         initialData.value = [...initialData.value, ...res.data.rows]
    //       } else {
    //         initialData.value = []
    //       }
    //     })
    //     .finally(() => {
    //       // console.log(111)
    //     })
    // }
  }, 500)
}
let timeoutId: ReturnType<typeof setTimeout> | null = null
function search(event) {
  if (timeoutId !== null) {
    clearTimeout(timeoutId)
    timeoutId = null
  }
  timeoutId = setTimeout(() => {
    parameDataModel.value.pageNo = 1
    // GetLoadList()
    paging.value.reload()
    // apiTag.value === '0' ? GetLoadList() : GetLoadtaskList()
  }, 500)
}
// 清空输入框
function clear() {
  parameDataModel.value.likeFieldValue = ''
}
</script>

<style lang="scss">
::v-deep {
  .navbar {
    position: unset !important;
  }
}

::v-deep {
  .wd-search {
    background: none !important;
    border: none !important;
  }
}

.borderBox {
  border-top: 0.0625rem solid #ebebeb;
}

.wd-drop-menu {
  //margin-top: 8px;
}

.tabs-content {
  z-index: 99;
  //height: 100%;
  //margin-top: 15px;
  background-color: white;
  border-radius: 0.625rem;
}

.custominfo {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.no-date {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100px;
  color: rgb(125, 125, 125);
}

.placeholder {
  height: 40px;
}
</style>
