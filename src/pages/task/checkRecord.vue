<route lang="json5">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '打卡记录',
  },
}
</route>

<template>
  <SafetyNavbar title="打卡记录"></SafetyNavbar>
  <view class="container">
    <scroll-view class="scroll-content" scroll-y="true">
      <view class="search">
        <wd-search placeholder-left placeholder="请输入姓名模糊搜索" v-model="searchVal" :hide-cancel="true"
          @change="handleChange" />
      </view>
      <div class="no-date" v-if="info.length < 1">暂无数据</div>
      <view class="content">
        <view class="content-view" v-for="item in info" :key="item.id">
          <view class="content-name">{{ item.userName }}</view>
          <view class="content-record">
            <view class="content-key">打卡时间</view>
            <view class="ontent-value">{{ item.clockInTime }}</view>
          </view>
          <view class="content-record">
            <view class="content-key">打卡位置</view>
            <view class="content-value">{{ item.clockInAddress || '--' }}</view>
          </view>
          <view class="content-record">
            <view class="content-key">打卡照片</view>
            <view class="content-img">
              <wd-img style="width: 100%; height: 90%" :src="getFileURL(item.filePath, true)"
                @click="previewImage(getFileURL(item.filePath))" />
            </view>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>
  <comOverlay ref="overlay" v-if="imageshow" :imgurl="imgurl" @closeimg="closeimg"></comOverlay>
</template>

<script lang="ts" setup>
import { useAppStore, usefileConfigStore, useUserStore } from '@/store'
import SafetyNavbar from '../components/safety-navbar.vue'
import { checkRecordList } from './featch'
import { recordList } from './type'
import { debounce, getFileURL } from '@/utils'
import comOverlay from '@/components/commonSelect/com-overlay.vue'
const searchVal = ref('')
const info = ref<any>([])
const userStore = useUserStore()
const userInfo: any = userStore.userInfo ? userStore.userInfo : {}
// console.log('userInfo', userInfo)
onLoad((options) => {
  // 判断是否从本模块打开页面
  params.taskId = options.taskId
  params.userId = options.userinfoId
  getDataList()
})

const params = reactive<recordList>({
  taskId: '',
  userId: '',
  userName: '',
})
const handleChange = debounce((val: any) => {
  // params.userName = encodeURIComponent(val.value)
  params.userName = val.value
  getDataList()
})

function getDataList() {
  // console.log(params, '看看入参-')
  checkRecordList(params as any)
    .then((res: any) => {
      if (res.code === 'success') {
        info.value = res.data
      }
    })
    .finally(() => {
      // console.log(111)
    })
}

const overlay = ref(null)
const imageshow = ref(false)
const imgurl = ref('')
const previewImage = (url) => {
  imageshow.value = true
  imgurl.value = url
}
// 关闭查看图片
function closeimg(val) {
  imageshow.value = val
}
</script>

<style lang="scss" scoped>
.no-date {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100px;
  color: rgb(125, 125, 125);
}

.container {
  height: 100%;
}

.content {
  margin: 1rem;
  // background: #ebeef5;
  border-radius: 0.5rem;

  .content-view {
    padding: 0.5rem;
    margin-bottom: 1rem;
    background: #ebeef5;
    border-radius: 0.4rem;

    .content-name {
      height: 2rem;
      padding-top: 0.3rem;
      font-size: 18px;
      color: #333333;
    }

    .content-record {
      display: flex;
      padding: 0.3rem 0;
      font-size: 14px;

      .content-key {
        margin-right: 0.5rem;
        color: #7f7f7f;
      }

      .content-value {
        color: #333333;
      }

      .content-img {
        width: 6.25rem;
        height: 6.25rem;
      }
    }
  }
}

::v-deep {
  .wd-search__input {
    height: 2.7rem;
  }

  .wd-collapse-item__arrow {
    color: #0052d9;
  }

  .wd-collapse-item__body {
    padding: 0 !important;
  }
}
</style>
