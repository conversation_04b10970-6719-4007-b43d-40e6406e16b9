import { http } from '@/utils/http'
import { $api } from '@/api/index'
/** post 请求 获取等级列表 hazardGrade/list   */
export const posthazardGradeAPI = (Parameters: any) => {
  return http.post($api.type.hazard + '/hazardGrade/list', {
    ...Parameters,
  })
}
//* 隐患复查列表  hazardRandomCheck/hazardReviewPage*/
export const posthazardReviewPageAPI = (Parameters: any) => {
  return http.post($api.type.hazard + '/hazardRandomCheck/hazardReviewPage', {
    ...Parameters,
  })
}
/* 隐患清单详情  hazardRandomCheck/getHazardReviewDetail */
export const postHazardReviewDetailAPI = (Parameters: any) => {
  return http.post($api.type.hazard + '/hazardRandomCheck/getHazardReviewDetail?id=' + Parameters)
}

//* 复查审核  /hazardRandomCheck/hazardReviewCheck */
export const posthazardReviewCheckAPI = (Parameters: any) => {
  return http.post($api.type.hazard + '/hazardRandomCheck/hazardReviewCheck ', {
    ...Parameters,
  })
}
/* 隐患复查-重新指派 /hazardRandomCheck/hazardReviewDesignate */
export const posthazardReviewDesignateAPI = (Parameters: any) => {
  return http.post($api.type.hazard + '/hazardRandomCheck/hazardReviewDesignate ', Parameters)
}

/* 整改记录 dispose/record */
export const postrecordAPI = (Parameters: any) => {
  return http.post($api.type.hazard + '/dispose/record', {
    ...Parameters,
  })
}
//* 批量复查审核  /hazardRandomCheck/hazardReviewCheck */
export const hazardReviewCheckList = (Parameters: any) => {
  return http.post($api.type.hazard + '/hazardRandomCheck/hazardReviewCheckList', {
    ...Parameters,
  })
}
