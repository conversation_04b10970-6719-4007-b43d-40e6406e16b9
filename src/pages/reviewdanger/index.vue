<route lang="json5">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '隐患复查',
  },
}
</route>
<template>
  <z-paging ref="paging" v-model="initialData" @query="GetLoadList">
    <template #top>
      <SafetyNavbar title="隐患复查"></SafetyNavbar>
      <!-- tab面板 -->
      <view class="_hearder_tab">
        <CustomTabs :tabs="tabs" :activeIndex="activeIndex" @handleClick="handleChange"></CustomTabs>
        <view v-if="activeIndex === 0" class="_button_fc">
          <wd-button style="min-width: 0; border-radius: 4px" plain hairline size="medium" @click="jumpBatch">
            批量复查
          </wd-button>
        </view>
      </view>
      <dorpMenu :orgRes="userinfo.orgRes" @getGrade="getGradevalue" @getUnit="getUnitvalue"></dorpMenu>
      <!-- 分割线 -->
      <hr style="border: 0.3px solid rgb(235, 235, 235)" />
      <!-- 搜索框 -->
      <wd-search @change="search" placeholder-left placeholder="请输入隐患位置/类型模糊搜索" :hide-cancel="true"
        v-model="parameDataModel.likeFieldValue" @clear="clear" />
    </template>

    <view class="container">
      <view v-if="initialData.length">
        <view v-if="activeIndex === 0">
          <customList :initialData="initialData"></customList>
        </view>
        <view v-if="activeIndex === 1">
          <customList :initialData="initialData"></customList>
        </view>
      </view>
      <template v-else>
        <view class="list-null">
          <noData title="暂无数据~"></noData>
        </view>
      </template>
    </view>
  </z-paging>
  <!-- <SafetyNavbar title="隐患复查"></SafetyNavbar>
  <view class="_hearder_tab">
    <CustomTabs :tabs="tabs" :activeIndex="activeIndex" @handleClick="handleChange"></CustomTabs>
    <view v-if="activeIndex === 0" class="_button_fc">
      <wd-button style="min-width: 0; border-radius: 4px" plain hairline size="medium" @click="jumpBatch">
        批量复查
      </wd-button>
    </view>
  </view> -->

  <!-- <view class="container">
    <view class="infobox">
      <dorpMenu @getGrade="getGradevalue" @getUnit="getUnitvalue"></dorpMenu>
      <hr style="border: 0.3px solid rgb(235, 235, 235)" />
      <wd-search @change="search" placeholder-left placeholder="请输入隐患位置/类型模糊搜索" :hide-cancel="true"
        v-model="parameDataModel.likeFieldValue" @clear="clear" />
      <div v-if="activeIndex === 0">
        <scroll-view style="height: 66vh" scroll-into-view="bottom" @scrolltolower="handleScrollToLower"
          scroll-y="true">
          <customList :initialData="initialData"></customList>
          <view style="color: #ccc; text-align: center" v-if="initialData.length === 0">
            暂无数据
          </view>
          <view id="bottom" v-if="total > initialData.length"
            style="font-size: 12px; color: #232323; text-align: center">
            加载更多数据
          </view>
        </scroll-view>
      </div>
      <div v-if="activeIndex === 1">
        <scroll-view style="height: 66vh" scroll-into-view="bottom" @scrolltolower="handleScrollToLower"
          scroll-y="true">
          <customList :initialData="initialData"></customList>
          <view style="color: #ccc; text-align: center" v-if="initialData.length === 0">
            暂无数据
          </view>
          <view id="bottom" v-if="total > initialData.length"
            style="font-size: 12px; color: #232323; text-align: center">
            加载更多数据
          </view>
        </scroll-view>
      </div>
    </view>
  </view> -->
</template>

<script lang="ts" setup>
import SafetyNavbar from '../components/safety-navbar.vue'
import customList from '../reviewdanger/components/custom-list.vue'
import CustomTabs from '../components/custom-Tabs.vue'
import dorpMenu from '@/components/commonSelect/dorp-menu.vue'
import { pageForm, parameFrom } from './type'
import { posthazardReviewPageAPI } from './featch'
/* 获取登录人信息 */
import { useUserStore } from '@/store'
const userStore = useUserStore()
const userinfo: any = userStore.userInfo ? userStore.userInfo : {}
const tabs = ['待复查', '已复查']
const activeIndex = ref(0)
const parameDataModel = ref<parameFrom>({
  hazardLevel: '',
  likeParam: '',
  pageNo: 1,
  pageSize: 10,
  reviewState: activeIndex.value,
  unitName: '',
  userId: userinfo.id,
  unitId: userinfo.unitId,
  checkUnitId: '',
  likeFields: 'hazardPosition,hazardSourceName',
  likeFieldValue: '',
  roleCodes: userinfo.roleCodes,
})
const paging = ref()
/* 待复查/已复查 ---条件查询 */
function handleChange(event) {
  parameDataModel.value.pageNo = 1
  initialData.value = []
  activeIndex.value = event
  parameDataModel.value.reviewState = event
  paging.value.reload()
  // GetLoadList()
}
// GetLoadList()
onShow(() => {
  setTimeout(function () {
    paging.value.reload()
  }, 200)
})
const total = ref(0)
const initialData = ref<pageForm[]>([])
function GetLoadList(pageNo) {
  uni.showLoading({ mask: true })
  parameDataModel.value.pageNo = pageNo
  posthazardReviewPageAPI(parameDataModel.value)
    .then((res: any) => {
      uni.hideLoading()
      if (res.data) {
        total.value = res.data.total
        paging.value.completeByTotal(res.data.rows, total.value)
      }
    })
    .finally(() => {
      uni.hideLoading()
    })
}
// 进入批量复查页面
function jumpBatch() {
  uni.navigateTo({
    url: `/pages/reviewBatch/index`,
  })
}
// 滚动加载
function handleScrollToLower() {
  if (total.value === initialData.value.length) {
    return
  }
  setTimeout(() => {
    parameDataModel.value.pageNo++
    // encodeURIComponent(parameDataModel.value.likeFieldValue)
    posthazardReviewPageAPI(parameDataModel.value)
      .then((res: any) => {
        if (res.data) {
          initialData.value = [...initialData.value, ...res.data.rows]
        } else {
          initialData.value = []
        }
      })
      .finally(() => { })
  }, 500)
}
/* 等级进行筛选 */
function getGradevalue(event) {
  parameDataModel.value.pageNo = 1
  parameDataModel.value.hazardLevel = event.id === '0000' ? '' : event.id
  // GetLoadList()
  paging.value.reload()
}
/* 单位进行筛选 */
function getUnitvalue(event) {
  parameDataModel.value.pageNo = 1
  parameDataModel.value.checkUnitId = event.id === '0000' ? '' : event.id
  // GetLoadList()
  paging.value.reload()
}
// 输入框模糊查询
let timeoutId: ReturnType<typeof setTimeout> | null = null
function search(event) {
  if (timeoutId !== null) {
    clearTimeout(timeoutId)
    timeoutId = null
  }
  timeoutId = setTimeout(() => {
    parameDataModel.value.pageNo = 1
    // GetLoadList()
    paging.value.reload()
  }, 500)
}
// 清空输入框
function clear() {
  parameDataModel.value.likeFieldValue = ''
}
</script>

<style lang="scss" scoped>
::v-deep {
  .navbar {
    position: unset !important;
  }
}

body {
  background-color: #f9f9f9 !important;
}

._hearder_tab {
  position: relative;

  ._button_fc {
    position: absolute;
    top: 0.3rem;
    right: 0.6rem;
  }
}

.container {
  height: 100%;

  // margin-top: -25px;
  .infobox {
    padding-bottom: 1.125rem;
    // margin-top: 1.125rem;
    background-color: white;

    .infobox_title {
      display: flex;
      flex-direction: row;
      align-items: center;
      padding: 0.25rem 0 0.9375rem 1.125rem;

      .fasticon {
        width: 0.25rem;
        height: 0.875rem;
        margin-right: 0.1875rem;
        background-color: #597bf7;
        border-radius: 1.125rem;
      }
    }

    .box {
      display: flex;
      flex-direction: row;
      flex-wrap: nowrap;
      justify-content: space-between;
      padding: 0px 1.125rem 0.75rem 1.125rem;

      .box-itme {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 10.25rem;
        height: 4.375rem;
        margin: 0.3125rem;
        background-color: rgba(188, 201, 218, 0.2);
        border-radius: 0.75rem;

        .imgbox {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 2.25rem;
          height: 2.25rem;

          .img {
            width: 100%;
            height: 100%;
          }
        }
      }

      .box-itme2 {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-around;
        width: 21.1875rem;
        height: 3.4375rem;
        margin: 0.3125rem;
        background-color: rgba(188, 201, 218, 0.2);
        border-radius: 12px;
      }
    }
  }
}
</style>
