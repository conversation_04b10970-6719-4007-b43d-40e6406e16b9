<route lang="json5">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '隐患复查',
  },
}
</route>

<template>
  <SafetyNavbar title="隐患复查"></SafetyNavbar>
  <view class="container">
    <wd-cell-group class="group" border>
      <wd-select-picker required label="复查结果" v-model="reviewChecktModel.reviewState" align-right type="radio"
        :show-confirm="false" :columns="columns" placeholder="请选择"></wd-select-picker>
      <view>
        <!-- <view class="c_label">复查说明</view> -->
        <view>
          <wd-textarea label="复查说明" required v-model="reviewChecktModel.reviewDesc" :maxlength="500" show-word-limit
            clearable auto-height placeholder="请输入" />
        </view>
      </view>
      <view>
        <view class="c_label">现场照片</view>
        <view style="padding: 10px 15px">
          <uploadButton @getFileObjList="getFilelist"></uploadButton>
        </view>
      </view>
    </wd-cell-group>
    <view class="fixed-bottom">
      <wd-button type="info" size="large" @click="goBack" block>取消</wd-button>
      <wd-button type="primary" size="large" @click="handleClickRight" :loading="loading" block>
        提交
      </wd-button>
    </view>
  </view>
</template>

<script lang="ts" setup>
import SafetyNavbar from '../components/safety-navbar.vue'
import uploadButton from '@/components/upload/upload-button.vue'
import { CheckReviewForm } from './type'
import { posthazardReviewCheckAPI, hazardReviewCheckList } from './featch'
import { useUserStore } from '@/store'
const userStore = useUserStore()
const userinfo: any = userStore.userInfo ? userStore.userInfo : {}
const isType = ref('')
const reviewChecktModel = ref<CheckReviewForm>({
  dealId: '',
  dealUserId: '',
  disposeId: '',
  filePathList: [],
  randomCheckEventId: '',
  reviewDesc: '',
  reviewState: 1,
  reviewStateName: '',
  reviewTime: 0,
  reviewUserId: userinfo.id,
  reviewUserName: userinfo.userName,
  reviewUserPhone: userinfo.userTelphone,
  zhId: '',
  idList: [],
})
const columns = ref<any>([
  {
    value: 1,
    label: '通过',
  },
  {
    value: 2,
    label: '不通过',
  },
])
const loading = ref(false)

onLoad((params) => {
  if (params.type === 'batch') {
    isType.value = params.type
    // console.log('params ----', params.idList.split(','))
    reviewChecktModel.value.idList = params.idList.split(',')
  } else {
    reviewChecktModel.value.dealId = params.dealId
    reviewChecktModel.value.disposeId = params.disposeId
    reviewChecktModel.value.zhId = params.zhId
    reviewChecktModel.value.randomCheckEventId = params.randomCheckEventId
  }
})
// 获取图片列表
function getFilelist(event) {
  reviewChecktModel.value.filePathList = event
}
// 取消
function goBack() {
  uni.navigateBack() || window.history.back()
}
function handleClickRight() {
  // -----非空判断开始-----
  if (!reviewChecktModel.value.reviewDesc) {
    uni.showToast({
      icon: 'none',
      title: '请输入复查说明',
    })
    return
  }
  // if (reviewChecktModel.value.filePathList.length === 0) {
  //   uni.showToast({
  //     icon: 'none',
  //     title: '请上传现场照片',
  //   })
  //   return
  // }
  // -----非空判断结束-----
  uni.showModal({
    title: '提示',
    content: '确认要提交隐患复查信息吗？',
    success: function (res) {
      if (res.confirm) {
        loading.value = true
        if (isType.value === 'batch') {
          hazardReviewCheckList(reviewChecktModel.value)
            .then((res: any) => {
              if (res.code === 'success') {
                uni.showToast({
                  icon: 'none',
                  title: '提交成功',
                })
                uni.navigateBack({
                  delta: 1,
                })
              } else {
                uni.showToast({
                  icon: 'none',
                  title: res.message,
                })
                loading.value = false
              }
            })
            .catch(() => {
              uni.showToast({
                icon: 'none',
                title: '未知错误',
              })
              loading.value = false
            })
            .finally(() => { })
        } else {
          posthazardReviewCheckAPI(reviewChecktModel.value)
            .then((res: any) => {
              if (res.code === 'success') {
                uni.showToast({
                  icon: 'none',
                  title: '提交成功',
                })
                uni.navigateBack({
                  delta: 1,
                })
              } else {
                uni.showToast({
                  icon: 'none',
                  title: res.message,
                })
                loading.value = false
              }
            })
            .catch(() => {
              uni.showToast({
                icon: 'none',
                title: '未知错误',
              })
              loading.value = false
            })
            .finally(() => { })
        }
      } else if (res.cancel) {
        // 在这里执行取消后的操作
        loading.value = false
      }
    },
  })
}
</script>

<style lang="scss" scoped>
::v-deep {

  // .wd-textarea__value {
  //   border-bottom: 0.0187rem solid #ebebeb;
  // }
  .wd-button.is-block {
    width: 100% !important;
    height: 100% !important;
    border-radius: 0 !important;
  }
}

.container {
  .group {
    padding-bottom: 4rem;
  }

  .c_label {
    padding: 10px 15px;
    font-family: Source Han Sans-Medium;
    font-size: 0.875rem;
    color: #1a1a1a;
  }

  .c-switch {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px 15px;
  }

  .required::before {
    // position: absolute;
    // left: 0;
    top: 0.125rem;
    line-height: 1.1;
    color: #fa4350;
    content: '*';
  }

  .fixed-bottom {
    position: fixed;
    bottom: 0;
    display: flex;
    flex-direction: row;
    align-items: center;
    width: 100%;
    height: 3.375rem;
    line-height: 3.375rem;
    color: #fff;
    text-align: center;
    background-color: white;
  }
}
</style>
