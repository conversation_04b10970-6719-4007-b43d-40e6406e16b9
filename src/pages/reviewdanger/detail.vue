<route lang="json5">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '隐患复查详情',
  },
}
</route>

<template>
  <SafetyNavbar title="隐患复查详情"></SafetyNavbar>
  <view class="container">
    <scroll-view class="scroll-content" scroll-y="true">
      <info1 :info1Itme="hazardInventoryDetail"></info1>
      <CustomTabs :tabs="tabs" :activeIndex="activeIndex" @handleClick="handleChange"></CustomTabs>
      <div v-if="activeIndex === 0">
        <info2 :info2Itme="hazardInventoryDetail"></info2>
      </div>
      <div v-if="activeIndex === 1">
        <changerecord :disposeId="disposeId"></changerecord>
      </div>
    </scroll-view>
    <!-- ----底部按钮开始----- -->
    <view class="fixed-bottom">
      <!-- <wd-button
        @click="showActions"
        style="min-width: 0; margin-left: 1.5rem; border-radius: 4px"
        size="medium"
        v-if="hazardInventoryDetail.reviewState === 0"
      >
        重新指派
      </wd-button> -->
      <wd-button @click="navigateTocheck()" style="min-width: 0; border-radius: 4px" plain hairline size="medium"
        v-if="hazardInventoryDetail.reviewState === 0">
        复查审核
      </wd-button>
    </view>
    <!-- ----底部按钮结束----- -->
  </view>
  <!-- -------弹出框部分开始-----隐患整改指派------ -->
  <wd-action-sheet v-model="show" title="隐患整改指派" @close="close">
    <view>
      <!-- <commonSelectpicker :title="'整改处置人员'" @send="reception"></commonSelectpicker> -->
      <commonSelectpicker :title="'复查处置人员'" @send="reception1"></commonSelectpicker>
    </view>
    <view class="sheetbom">
      <wd-button style="width: 100%; border-radius: 0" @click="close" type="primary" plain size="medium">
        取消
      </wd-button>
      <wd-button style="width: 100%" type="primary" @click="reassignClick" size="medium">
        确定
      </wd-button>
    </view>
  </wd-action-sheet>
  <!-- -------弹出框部分结束-----隐患整改指派------ -->
</template>

<script lang="ts" setup>
import SafetyNavbar from '../components/safety-navbar.vue'
import CustomTabs from '../components/custom-Tabs.vue'
import info1 from './components/info1.vue'
import info2 from './components/info2.vue'
import changerecord from './components/change_record.vue'
import commonSelectpicker from '@/components/commonSelect/common-selectpicker.vue'
// import customSelectpicker from '../components/custom-selectpicker.vue'
import { posthazardReviewDesignateAPI, postHazardReviewDetailAPI } from './featch'
import { useUserStore } from '@/store'
const userStore = useUserStore()
const userinfo: any = userStore.userInfo ? userStore.userInfo : {}
// const tabs = ['检查信息', '整改记录']

const disposeId = ref()
const reviewId = ref()
onLoad((params) => {
  disposeId.value = params.disposeId
  reviewId.value = params.id
  getHazardReviewDetail(params.id)
})
onShow(() => {
  // // console.log(params,"===================onshow")
  getHazardReviewDetail(reviewId.value)
})
const hazardInventoryDetail = ref<any>({})
const tabs = ref([])
const activeIndex = ref(0)
function handleChange(event) {
  // // console.log('父组件取子组件的值', event)
  // activeIndex.value = event
  if ([1, 3, 14, 15, 16, 17, 18, 19, 20].includes(hazardInventoryDetail.value.hazardSource)) {
    activeIndex.value = 1
  } else {
    activeIndex.value = event
  }
}
// 获取详情
function getHazardReviewDetail(id) {
  postHazardReviewDetailAPI(id)
    .then((res: any) => {
      // // console.log(res)
      hazardInventoryDetail.value = res.data

      if ([1, 3, 14, 15, 16, 17, 18, 19, 20].includes(hazardInventoryDetail.value.hazardSource)) {
        tabs.value = ['整改记录']
        activeIndex.value = 1
      } else {
        tabs.value = ['检查信息', '整改记录']
      }
    })
    .finally(() => {
      // console.log(111)
    })
}
// 重新指派
const show = ref<boolean>(false)
function showActions() {
  show.value = true
}
function close() {
  show.value = false
}

const modelArray = ref<any[]>([])
// 接收整改处置人员数据
// function reception(event) {
//   // console.log(event, '=========接收整改处置人员数据')
//   modelArray.value.push(convertToreformUserJson(JSON.parse(JSON.stringify(event)), 0))
// }
// 接收复查处置人员数据
function reception1(event) {
  // console.log(event, '=========接收复查处置人员数据')
  modelArray.value.push(...convertToreformUserJson(JSON.parse(JSON.stringify(event)), 1))
}
function convertToreformUserJson(items, type) {
  return items.map((item) => ({
    reformUserName: item.userName,
    reformUserId: item.id,
    reformType: type,
    zhId: hazardInventoryDetail.value.zhId,
    randomCheckEventId: hazardInventoryDetail.value.randomCheckEventId,
    id: hazardInventoryDetail.value.id,
    reviewUserId: userinfo.id,
    reviewUserName: userinfo.userName,
  }))
}
// 重新指派
function reassignClick() {
  // 确认要提交隐患整改指派人员信息吗？
  uni.showModal({
    title: '提示',
    content: '确认要提交隐患整改指派人员信息吗？',
    success: function (res) {
      if (res.confirm) {
        // console.log('用户点击确定')
        // 在这里执行确定后的操作
        if (modelArray.value.length === 0) {
          uni.showToast({
            icon: 'none',
            title: '请选择处置人员',
          })
          return
        }
        posthazardReviewDesignateAPI(modelArray.value)
          .then((res: any) => {
            if (res.code === 'success') {
              uni.showToast({
                icon: 'none',
                title: '指派成功',
              })
              getHazardReviewDetail(reviewId.value)
              show.value = false
            }
            uni.showToast({
              icon: 'none',
              title: res.message,
            })
          })
          .finally(() => { })
      } else if (res.cancel) {
        // 在这里执行取消后的操作
      }
    },
  })
}

// f复查审核
function navigateTocheck() {
  // dealId  ====整改记录id
  // disposeId ====处置id
  uni.navigateTo({
    url: `/pages/reviewdanger/dangercheck?dealId=${hazardInventoryDetail.value.dealId}&disposeId=${hazardInventoryDetail.value.disposeId}&zhId=${hazardInventoryDetail.value.zhId}&randomCheckEventId=${hazardInventoryDetail.value.randomCheckEventId}`,
  })
}
</script>

<style lang="scss" scoped>
body {
  background-color: #f9f9f9 !important;
}

.sheetbom {
  display: flex;
}

.scroll-content {
  padding-bottom: 4.375rem;
}

.fixed-bottom {
  position: fixed;
  bottom: 0;
  width: 100%;
  height: 3.375rem;
  line-height: 3.375rem;
  color: #fff;
  text-align: end;
  background-color: white;
  border-top: 0.3px solid #ccc;
}

::v-deep {
  .wd-button.is-medium.is-round {
    margin-right: 1.5rem;
  }

  .sheetbom {
    .wd-button.is-medium.is-round {
      margin-right: 0;
      border-radius: 0;
    }
  }

  .tabs-header {
    margin-bottom: 0;
    color: #969696;
    background-color: white !important;
    background-image: none;
    border-bottom: 0.3px solid #ccc;

    .tabs-header-item-active {
      background-color: #597bf7;
    }

    .tabs-header-active {
      color: #597bf7;
    }
  }
}
</style>
