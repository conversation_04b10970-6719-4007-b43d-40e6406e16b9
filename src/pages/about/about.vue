<route lang="json5" type="page">
{
  style: {
    navigationStyle: 'custom',
  },
}
</route>

<template>
  <view class="bg-white overflow-hidden">
    <HeaderBar />

    <view class="mb-2">
      <wd-button @click="toApp">返回</wd-button>
    </view>

    <view class="mb-2">
      <wd-button @click="chooseImg(9)">choose img</wd-button>
    </view>
    <view v-if="loading" class="text-blue h-10">上传...</view>
    <template v-else>
      <view class="m-2">上传后返回的接口数据：</view>
      <view class="m-2">{{ uploadRes.length }}</view>
      <view class="h-80 w-full" v-for="(item, index) in uploadRes" :key="index">
        <image :src="item" mode="scaleToFill" />
      </view>
    </template>
  </view>
</template>

<script lang="ts" setup>
import HeaderBar from '@/components/navigationBar/index.vue'
import { uploadFile } from '@/hooks/useUpload'
import { useAppReceiveStore } from '@/store'
// 获取屏幕边界到安全区域距离
const receiveStore = useAppReceiveStore()

const loading = ref(false)
const error = ref(false)
const uploadRes = ref([])

// 监听app传入的数据（不支持小程序） -- 获取相册为例
const appData = computed(() => receiveStore.appData)
watch(
  appData,
  (val) => {
    if (val.action === 'img' && val.status === 'success') {
      const files = val.data

      files.forEach((fileBase64) => {
        const blob = baseToBlob(fileBase64)
        const blobUrl = URL.createObjectURL(blob)
        // console.log('watch', blobUrl)
        uploadRes.value.push(blobUrl)
      })

      // 图片上传示例 >>
      // const formData = { user: '' }
      // uploadFile({ tempFilePath: uploadRes.value[0], formData, loading })
      //   .then((resData) => {
      //     uploadRes.value.push(resData)
      //   })
      //   .catch((err) => {
      //     console.error('uni.uploadFile err->', err)
      //     error.value = true
      //   })
    }
  },
  {
    immediate: false,
  },
)

// base64 => bolb
function baseToBlob(baseData) {
  const arr = baseData.split(',')
  const mime = arr[0].match(/:(.*?);/)[1]
  const bstr = atob(arr[1]) // base64 解码
  let n = bstr.length
  const u8arr = new Uint8Array(bstr.length) // 8位无符号整数，长度1个字节
  // console.log(mime)
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n)
  }
  return new Blob([u8arr], {
    type: mime,
  })
}

function chooseImg(count) {
  // 解决设参数默认值，无法正确传递的问题
  const _count = count || 1
  uni.postMessage({
    data: {
      action: 'img',
      count: _count,
    },
  })
}

function toApp() {
  uni.webView.navigateBack()
}

// 测试 uni API 自动引入
onLoad(() => { })

onBeforeUnmount(() => {
  // 释放createObjectURL创建得对象
  uploadRes.value.forEach((blobUrl) => {
    URL.revokeObjectURL(blobUrl)
  })
})
</script>

<style lang="scss" scoped>
.test-css {
  // mt-4=>1rem=>16px;
  margin-top: 16px;
}
</style>
