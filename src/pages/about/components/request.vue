<route lang="json5">
{
  layout: 'demo',
  style: {
    navigationBarTitleText: '请求',
  },
}
</route>

<template>
  <view class="p-6 text-center">
    <view class="my-2">使用的是 laf 云后台</view>

    <!-- http://localhost:9000/#/pages/index/request -->
    <wd-button @click="getData" class="my-6">发送请求</wd-button>

    <view class="h-12">
      <view v-if="loading">loading...</view>
      <block v-else>
        <view class="text-xl">请求数据如下</view>
        <view class="text-green leading-8">{{ JSON.stringify(initialData) }}</view>
      </block>
    </view>

    <wd-button type="error" @click="reset" class="my-6" :disabled="!initialData">
      重置数据
    </wd-button>
  </view>
</template>

<script lang="ts" setup>
import { getFooAPI } from '../featch'

const loading = ref(true)
const initialData = ref(undefined)

function getData() {
  getFooAPI('Hello')
    .then((res) => {
      initialData.value = res.data
    })
    .finally(() => {
      loading.value = false
    })
}
getFooAPI('Hello')

const reset = () => {
  loading.value = true
  initialData.value = undefined
}
</script>
