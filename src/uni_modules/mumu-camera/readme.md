## 插件简绍

### 实现原理

> 通过 navigator.mediaDevices.getUserMedia(需要https环境) 这个api调用摄像头，获取到到视频流数据。把视频流数据给到一个 video 标签就可以把视频展示在页面上了。在 video 标签的上一层创建一个 canvas 标签，用于展示水印。在创建一个隐藏的 canvas 标签，当用户点击快门的时候截取当前视频流的一帧交给隐藏的 canvas 画出来，之后再把需要添加的水印画到这个隐藏的 canvas 上，最后保存为图片。

### 使用环境

需要https环境才能使用，本地测试可以在 manifest.json 中点击源码展示，找到h5 ，添加："devServer" : { "https" : true}

**请勿使用 UC浏览器 与 夸克等阿里旗下的浏览器，发现他们使用的内核都较低，无法正常获取视频流，并且都有对接视频流截取的插件，导致无法正常获取摄像头的数据。在微信中可以正常使用，推荐在微信内打开演示案例 **

需要https环境才能使用！！！

需要https环境才能使用！！！

需要https环境才能使用！！！

在安卓系统上可以打开闪光灯

### 插件使用

**插件已支持 uni_modules 支持组件easycom，以下代码演示的是普通使用**

```html
<!-- HTML -->
<view>
  <view class="imgBox" v-if="img">
    <image :src="img" class="img" mode="widthFix"></image>
  </view>
  <mumu-camera @success="handlerSuccess" :watermark="watermark" v-else></mumu-camera>
</view>
```

```javascript
// js
import MumuCamera from '@/uni_modules/mumu-camera/components/mumu-camera/mumu-camera.vue'

export default {
  components: { MumuCamera },
  data() {
    return {
      img: '',
      watermark: [
        {
          type: 'text',
          x: 20,
          y: 40,
          font_color: '#fff',
          font_size: 20,
          text: '程序员毕业会',
        },
        {
          type: 'text',
          x: 15,
          y: 100,
          font_color: '#fff',
          font_size: 50,
          text: '你好呀',
        },
      ],
    }
  },
  onLoad() {},
  methods: {
    handlerSuccess(img) {
      this.img = img
    },
  },
}
```

### 相关API

##### 可传属性（Props）

| 参数      | 说明                                             | 类型   | 默认值      |
| --------- | ------------------------------------------------ | ------ | ----------- |
| exact     | 选调用摄像头。environment 后摄像头 user 前摄像头 | String | environment |
| watermark | 添加水印，支持本地图片与文件。详情见下           | Array  | []          |

- watermark 添加水印：

  watermark 需要的是一个数组，数组中是对象。

  **对象属性**

  | 参数       | 类型          | 说明                               |
  | ---------- | ------------- | ---------------------------------- |
  | type       | img 或者 text | img 标识图片水印，text标识文字水印 |
  | x          | number        | 水印位置 X 轴                      |
  | y          | number        | 水印位置 Y 轴                      |
  | w          | number        | 图片的宽度，只对图片水印有效       |
  | h          | number        | 图片的高度，只对图片水印有效       |
  | url        | string        | 本地图片地址，只对图片水印有效     |
  | font_color | 十六进制色码  | 文字颜色，只对文字水印有效         |
  | font_size  | number        | 文字大小                           |
  | text       | string        | 添加的文字水印                     |

  ```javascript
  // 演示
  watermark: [
    {
      type: 'img',
      x: 20,
      y: 120,
      w: 70,
      h: 70,
      url: '/static/logo.jpg',
    },
    {
      type: 'text',
      x: 20,
      y: 40,
      font_color: '#fff',
      font_size: 20,
      text: '程序员毕业会',
    },
    {
      type: 'text',
      x: 15,
      y: 100,
      font_color: '#fff',
      font_size: 50,
      text: '你好呀',
    },
  ]
  ```

##### 事件（Events）

| 事件名  | 说明                   | 回调参数                     |
| ------- | ---------------------- | ---------------------------- |
| success | 按下快门生成的图片数据 | 图片数据，返回的是base64数据 |

### 案例演示

![enter description here](https://h5plugin.mumudev.top/public/camera/qrcode.png)

## 支持作者

![支持作者](https://student.mumudev.top/wxMP.jpg)
