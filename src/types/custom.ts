export type Nullable<T> = T | null

export type Voidable<T> = T | undefined

export type Indexable<T> = {
  [key: string]: T
}

export type Hash<T> = Indexable<T>

export type IObj<T> = Indexable<T>

export type IFunction<T> = (...args: any[]) => T

export type emptyable<T> = T | Indexable<any>

export type Merge<M, N> = Omit<M, Extract<keyof M, keyof N>> & N

export type IPageRes<T> = {
  rows: T[]
  pageNo: number
  pageSize: number
  pages: number
  total: number
}

export interface IDict {
  dictLabel: string
  dictValue: string
  [prop: string]: string
}
