<template>
  <wd-config-provider :themeVars="themeVars">
    <view class="box-border" :style="{ paddingTop, height, opacity: remoteLoadComplete ? 1 : 0 }">
      <slot :key="remoteLoadComplete" />
    </view>
    <wd-toast />
    <wd-message-box />
  </wd-config-provider>
</template>

<script lang="ts" setup>
import type { ConfigProviderThemeVars } from 'wot-design-uni'
import { callRemoteMethod, UniNavbar } from '@/utils/hel-micro'

const themeVars: ConfigProviderThemeVars = {
  // colorTheme: 'red',
  // buttonPrimaryBgColor: '#07c160',
  // buttonPrimaryColor: '#07c160',
}
const paddingTop = ref('')
const height = ref('')
const remoteLoadComplete = ref(false)

function computedHeight() {
  // const paddingTopValue = document.querySelector('.navbar')?.clientHeight
  paddingTop.value = document.querySelector('.navbar')?.clientHeight + 'px'
  const screenHeight = window.innerHeight
  // height.value = `${screenHeight - Number(paddingTopValue)}px`
  height.value = `${screenHeight}px`
}
onMounted(() => {
  callRemoteMethod().then(() => {
    computedHeight()
    remoteLoadComplete.value = true
  })
})

watch(
  () => UniNavbar.value,
  () => {
    computedHeight()
  },
)
</script>
