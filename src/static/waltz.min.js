const waltz = (function () {
  'use strict'
  function i(e) {
    return (i =
      typeof Symbol === 'function' && typeof Symbol.iterator === 'symbol'
        ? function (e) {
            return typeof e
          }
        : function (e) {
            return e &&
              typeof Symbol === 'function' &&
              e.constructor === Symbol &&
              e !== Symbol.prototype
              ? 'symbol'
              : typeof e
          })(e)
  }
  function s(e, t) {
    if (!(e instanceof t)) throw new TypeError('Cannot call a class as a function')
  }
  function n(e, t) {
    for (let a = 0; a < t.length; a++) {
      const n = t[a]
      ;(n.enumerable = n.enumerable || !1),
        (n.configurable = !0),
        'value' in n && (n.writable = !0),
        Object.defineProperty(e, n.key, n)
    }
  }
  function o(e, t, a) {
    return (
      t && n(e.prototype, t),
      a && n(e, a),
      Object.defineProperty(e, 'prototype', { writable: !1 }),
      e
    )
  }
  function e(e, t) {
    if (typeof t !== 'function' && t !== null)
      throw new TypeError('Super expression must either be null or a function')
    ;(e.prototype = Object.create(t && t.prototype, {
      constructor: { value: e, writable: !0, configurable: !0 },
    })),
      Object.defineProperty(e, 'prototype', { writable: !1 }),
      t && a(e, t)
  }
  function r(e) {
    return (r = Object.setPrototypeOf
      ? Object.getPrototypeOf.bind()
      : function (e) {
          return e.__proto__ || Object.getPrototypeOf(e)
        })(e)
  }
  function a(e, t) {
    return (a = Object.setPrototypeOf
      ? Object.setPrototypeOf.bind()
      : function (e, t) {
          return (e.__proto__ = t), e
        })(e, t)
  }
  function l(e) {
    if (void 0 === e)
      throw new ReferenceError("this hasn't been initialised - super() hasn't been called")
    return e
  }
  function c(a) {
    const n = (function () {
      if (typeof Reflect === 'undefined' || !Reflect.construct) return !1
      if (Reflect.construct.sham) return !1
      if (typeof Proxy === 'function') return !0
      try {
        return Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})), !0
      } catch (e) {
        return !1
      }
    })()
    return function () {
      let e
      var t = r(a)
      var t =
        ((e = n
          ? ((e = r(this).constructor), Reflect.construct(t, arguments, e))
          : t.apply(this, arguments)),
        this)
      if (e && (typeof e === 'object' || typeof e === 'function')) return e
      if (void 0 !== e)
        throw new TypeError('Derived constructors may only return object or undefined')
      return l(t)
    }
  }
  function H(e, t) {
    return (
      (function (e) {
        if (Array.isArray(e)) return e
      })(e) ||
      (function (e, t) {
        let a =
          e == null
            ? null
            : (typeof Symbol !== 'undefined' && e[Symbol.iterator]) || e['@@iterator']
        if (a != null) {
          let n
          let i
          const r = []
          let s = !0
          let o = !1
          try {
            for (
              a = a.call(e);
              !(s = (n = a.next()).done) && (r.push(n.value), !t || r.length !== t);
              s = !0
            );
          } catch (e) {
            ;(o = !0), (i = e)
          } finally {
            try {
              s || a.return == null || a.return()
            } finally {
              if (o) throw i
            }
          }
          return r
        }
      })(e, t) ||
      (function (e, t) {
        let a
        if (e)
          return typeof e === 'string'
            ? u(e, t)
            : (a =
                  (a = Object.prototype.toString.call(e).slice(8, -1)) === 'Object' && e.constructor
                    ? e.constructor.name
                    : a) === 'Map' || a === 'Set'
              ? Array.from(e)
              : a === 'Arguments' || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)
                ? u(e, t)
                : void 0
      })(e, t) ||
      (function () {
        throw new TypeError(
          'Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.',
        )
      })()
    )
  }
  function u(e, t) {
    ;(t == null || t > e.length) && (t = e.length)
    for (var a = 0, n = new Array(t); a < t; a++) n[a] = e[a]
    return n
  }
  function t(e, t) {
    Object.defineProperty(e, t, { writable: !1 })
  }
  function d(e) {
    if (!(e = e.match(/[^,|^\s]+=.+?(,|$)/g))) return {}
    for (
      var t = [],
        a = [],
        n =
          (e.forEach(function (e) {
            t.push(e.split(/=/)[0]), a.push(e.match(/.*?=([^,]+)/)[1])
          }),
          {}),
        i = 0,
        r = t.length;
      i < r;
      i++
    )
      n[t[i]] = a[i]
    return n
  }
  const f = {
    warn: function (e) {
      console.warn('[waltz warn]: ' + e)
    },
    error: function (e) {
      console.error('[waltz error]: ' + e)
    },
    info: function (e) {
      console.info('[waltz info]: ' + e)
    },
    log: function (e) {
      // console.log('[waltz log]: ' + e)
    },
  }
  var h = {
    store: new Map(),
    key: function (e) {
      let t
      return h.store.has(e) ? h.store.get(e) : ((t = Symbol(e)), h.store.set(e, t), t)
    },
  }
  const B = (function () {
    function e() {
      s(this, e),
        (this.os = {
          type: this._getUaInfo(),
          supportWaltz: this._getIsSupportWaltz(),
          isZipModel: this._getIsZipModel(),
          appEnv: this._getAppEnv(),
          device: this._getDevice(),
          deviceName: this._getDeviceName(),
        })
    }
    return (
      o(e, [
        {
          key: '_getUaInfo',
          value: function () {
            let e = ''
            var t = window.navigator.userAgent.match(/systemName=(.+?),/)
            var t = t ? t[1] : ''
            return (
              /iOS/i.test(t)
                ? (e = 'iOS')
                : /android/i.test(t)
                  ? (e = 'android')
                  : /pc/i.test(t)
                    ? (e = 'pc')
                    : f.warn('Unrecognized operating OS'),
              e
            )
          },
        },
        {
          key: '_getIsSupportWaltz',
          value: function () {
            return /waltz/i.test(window.navigator.userAgent)
          },
        },
        {
          key: '_getIsZipModel',
          value: function () {
            return /file:/i.test(window.location.protocol)
          },
        },
        {
          key: '_getAppEnv',
          value: function () {
            return d(window.navigator.userAgent).appEnvironment
          },
        },
        {
          key: '_getDevice',
          value: function () {
            return d(window.navigator.userAgent).device
          },
        },
        {
          key: '_getDeviceName',
          value: function () {
            return d(window.navigator.userAgent).deviceName
          },
        },
      ]),
      e
    )
  })()
  var m = {
    on: function (e, t) {
      this._eventHandlers || (this._eventHandlers = {}),
        this._eventHandlers[e] || (this._eventHandlers[e] = []),
        this._eventHandlers[e].push(t)
    },
    off: function (e, t) {
      const a = this._eventHandlers && this._eventHandlers[e]
      if (a) for (let n = 0; n < a.length; n++) a[n] === t && a.splice(n--, 1)
    },
    trigger: function (e) {
      for (var t = this, a = arguments.length, n = new Array(a > 1 ? a - 1 : 0), i = 1; i < a; i++)
        n[i - 1] = arguments[i]
      this._eventHandlers &&
        this._eventHandlers[e] &&
        this._eventHandlers[e].forEach(function (e) {
          return e.apply(t, n)
        })
    },
  }
  const y = o(function e() {
    s(this, e), Object.assign(this, new B())
  })
  Object.assign(y.prototype, m)
  const p = function (e) {
    return typeof e === 'function'
  }
  const v = function (e) {
    try {
      return JSON.parse(e), !0
    } catch (e) {
      return !1
    }
  }
  const g = {
    NATIVE_CALL_JS: 'native-call-js',
    NATIVE_CALL_JS_SUCCESS: 'native-call-js-success',
    NATIVE_CALL_JS_ERROR: 'native-call-js-error',
    JS_CALL_NATIVE: 'js-call-native',
    JS_CALL_NATIVE_SUCCESS: 'js-call-native-success',
    JS_CALL_NATIVE_ERROR: 'js-call-native-error',
    MESSAGE: 'message',
    LIGHT_APP_MESSAGE: 'light-app-message',
  }
  const F = 'window-blur'
  const j = 'window-focus'
  const M = 'window-destroy'
  const V = (function () {
    e(a, y)
    const t = c(a)
    function a() {
      let e
      return (
        s(this, a),
        ((e = t.call(this)).uniqueId = 'xxxxxxxx-xxxx-xxxx-yxxx-yxxxxxxxxyxx'.replace(
          /[xy]/g,
          function (e) {
            const t = (16 * Math.random()) | 0
            return (e === 'x' ? t : (3 & t) | 8).toString(16)
          },
        )),
        (e.sequence = 0),
        (e.callbackFuncIdReference = {}),
        (e.nativeCallWebOneWayFuncNameList = []),
        e
      )
    }
    return (
      o(
        a,
        [
          {
            key: '_id',
            get: function () {
              return 'waltzWebId:'.concat(this.uniqueId, ':').concat(this._nextSequence())
            },
          },
          {
            key: '_nextSequence',
            value: function () {
              return ++this.sequence
            },
          },
          {
            key: '_cacheCallbackId',
            value: function (e) {
              const t = this._id
              return (this.callbackFuncIdReference[t] = e), t
            },
          },
          {
            key: '_removeCacheCallback',
            value: function (e) {
              const t = this.callbackFuncIdReference[e]
              return t
                ? (delete this._cacheCallbackId[e], { isCallback: !0, callback: t })
                : { isCallback: !1 }
            },
          },
          {
            key: '_webCallNative',
            value: function (e, t) {
              e.callback = this._cacheCallbackId(t)
              let a = !0
              switch (this.os.type) {
                case 'android':
                  window.waltzSDK && window.waltzSDK.callNative(JSON.stringify(e))
                  break
                case 'iOS':
                  window.webkit && window.webkit.messageHandlers.callNative.postMessage(e)
                  break
                case 'pc':
                  window.callNative && window.callNative(JSON.stringify(e))
                  break
                default:
                  ;(a = !1), f.error('Call native error, unrecognized operating OS.')
              }
              a && this.trigger(g.JS_CALL_NATIVE, e)
            },
          },
          {
            key: 'webCallNative',
            value: function (i) {
              const r = this
              return new Promise(function (a, n) {
                let e = i.apiName
                const t = i.listener
                p(t) &&
                  ((e = ''.concat(e, ':') + r._nextSequence()),
                  r.on(e, t),
                  (i.listenerId = e),
                  delete i.listener)
                r._webCallNative(i, function (e) {
                  const t = e.id
                  var e = e.data
                  ;(e.code === '10000' ? (v(e.body) && (e.body = JSON.parse(e.body)), a) : n)({
                    id: t,
                    data: e,
                  })
                })
              })
            },
          },
          {
            key: 'nativeCallWeb',
            value: function (e, t) {
              this._nativeCallWebEventTrigger(e, t)
              var a = this._removeCacheCallback(e)
              const n = a.isCallback
              var a = a.callback
              n
                ? a({ id: e, data: t })
                : ~this.nativeCallWebOneWayFuncNameList.indexOf(e) && this.trigger(e, t)
            },
          },
          {
            key: '_nativeCallWebEventTrigger',
            value: function (e, t) {
              switch ((this.trigger(g.NATIVE_CALL_JS, t), e)) {
                case g.MESSAGE:
                  this.trigger(g.MESSAGE, t)
                  break
                case g.LIGHT_APP_MESSAGE:
                  this.trigger(h.key(g.LIGHT_APP_MESSAGE), t)
              }
            },
          },
        ],
        [
          {
            key: 'getInstance',
            value: function () {
              return a.instance || (a.instance = new a())
            },
          },
        ],
      ),
      a
    )
  })()
  var m = (function () {
    function e() {
      s(this, e),
        (this.bridge = V.getInstance()),
        (this.registerAPIs = {}),
        (this.bridge.nativeCallWebOneWayFuncNameList = [F, j, M])
    }
    return (
      o(e, [
        {
          key: 'registerSystemCallAPI',
          value: function (o) {
            const l = this
            return function (e) {
              let t
              const a = e.apiName
              const n = e.alias
              const i = e.classify
              const r = e.paramsValidate
              const s = e.additional
              var e = e.execute
              a
                ? l._checkRegister(i, a)
                  ? (o[i] || (o[i] = {}),
                    (t = o[i]),
                    e && p(e)
                      ? (t[n || a] = e)
                      : (t[n || a] = function (e) {
                          if (!(p(r) && (e = r(e)) instanceof Error))
                            return (
                              (e = { apiName: a, data: e || {} }),
                              Object.assign(e, s),
                              l.bridge.webCallNative(e)
                            )
                          f.warn(
                            e.toString().split(':').slice(1).join(':').trim() ||
                              "API '".concat(n || a, "' is invalid interface"),
                          )
                        }))
                  : f.error("the API '".concat(i, '.').concat(a, "' has been registered"))
                : f.error("missing parameter 'apiName' in registerSystemCallAPI")
            }
          },
        },
        {
          key: '_checkRegister',
          value: function (e, t) {
            if (this.registerAPIs[e]) {
              if (this.registerAPIs[e].includes(t)) return !1
              this.registerAPIs[e].push(t)
            } else this.registerAPIs[e] = [t]
            return !0
          },
        },
        {
          key: 'event',
          get: function () {
            const t = this
            return {
              on: function () {
                let e
                ;(e = t.bridge).on.apply(e, arguments)
              },
            }
          },
        },
        {
          key: 'register',
          value: function (e, t) {
            ~this.bridge.nativeCallWebOneWayFuncNameList.indexOf(e) ||
              this.bridge.nativeCallWebOneWayFuncNameList.push(e),
              this.bridge.on(e, t)
          },
        },
        {
          key: 'on',
          value: function (e, t) {
            this.bridge.on(h.key(e), t)
          },
        },
        {
          key: 'call',
          value: function (e) {
            const t = e.module
            const a = e.handlerName
            var e = e.data
            return this.bridge.webCallNative({
              module: t,
              handlerName: a,
              data: void 0 === e ? {} : e,
            })
          },
        },
      ]),
      e
    )
  })()
  const w = o(function e() {
    s(this, e), (this.module = 'WZNavigation')
  })
  const N = (function () {
    e(r, w)
    const i = c(r)
    function r(e) {
      let t
      const a = e.ctx
      var e = e.classify
      const n = (s(this, r), (t = i.call(this)), 'setTitle')
      return (
        a.registerSystemCallAPI({
          apiName: n,
          classify: e,
          additional: { module: t.module, handlerName: n },
          paramsValidate: function (e) {
            const t = e.title
            const a = e.color
            var e = e.size
            return {
              title: void 0 === t ? '标题' : t,
              titleColor: void 0 === a ? '#333333' : a,
              fontSize: void 0 === e ? 17 : e,
            }
          },
        }),
        t
      )
    }
    return o(r)
  })()
  const x = (function () {
    e(r, w)
    const i = c(r)
    function r(e) {
      let t
      const a = e.ctx
      var e = e.classify
      const n = (s(this, r), (t = i.call(this)), 'setBackgroundColor')
      return (
        a.registerSystemCallAPI({
          classify: e,
          apiName: n,
          additional: { module: t.module, handlerName: n },
          paramsValidate: function (e) {
            return { color: e }
          },
        }),
        t
      )
    }
    return o(r)
  })()
  const U = (function () {
    e(r, w)
    const i = c(r)
    function r(e) {
      let t
      const a = e.ctx
      var e = e.classify
      const n = (s(this, r), (t = i.call(this)), 'setStateBarStyle')
      return (
        a.registerSystemCallAPI({
          alias: 'setStatusBar',
          apiName: n,
          classify: e,
          additional: { module: t.module, handlerName: n },
          paramsValidate: function (e) {
            ;(e = e.black), (e = void 0 === e || e)
            return { style: (e = e ? 'dark' : 'light') }
          },
        }),
        t
      )
    }
    return o(r)
  })()
  const D = (function () {
    e(r, w)
    const i = c(r)
    function r(e) {
      let t
      const a = e.ctx
      var e = e.classify
      const n = (s(this, r), (t = i.call(this)), 'setStatusBar')
      return (
        a.registerSystemCallAPI({
          classify: e,
          apiName: n,
          additional: { module: t.module, handlerName: n },
          paramsValidate: function (e) {
            const t = e.black
            var e = e.backgroundColor
            return { black: void 0 !== t && t, backgroundColor: void 0 === e ? '#FFFFFF' : e }
          },
        }),
        t
      )
    }
    return o(r)
  })()
  const S = o(function e(t) {
    const a = t.ctx
    var t = t.classify
    var t = (s(this, e), { ctx: a, classify: t })
    new (a.os.type === 'iOS' ? U : D)(t)
  })
  const I = (function () {
    e(r, w)
    const i = c(r)
    function r(e) {
      let t
      const a = e.ctx
      var e = e.classify
      const n = (s(this, r), (t = i.call(this)), 'showNavigationBar')
      return (
        a.registerSystemCallAPI({
          alias: 'show',
          apiName: n,
          classify: e,
          additional: { module: t.module, handlerName: n },
        }),
        t
      )
    }
    return o(r)
  })()
  const A = (function () {
    e(r, w)
    const i = c(r)
    function r(e) {
      let t
      const a = e.ctx
      var e = e.classify
      const n = (s(this, r), (t = i.call(this)), 'hideNavigationBar')
      return (
        a.registerSystemCallAPI({
          alias: 'hide',
          apiName: n,
          classify: e,
          additional: { module: t.module, handlerName: n },
        }),
        t
      )
    }
    return o(r)
  })()
  const b = (function () {
    e(i, w)
    const n = c(i)
    function i(e) {
      let t
      const a = e.ctx
      var e = e.classify
      s(this, i), (t = n.call(this))
      return (
        a.registerSystemCallAPI({
          alias: 'back',
          apiName: 'goBack',
          classify: e,
          additional: { module: t.module, handlerName: 'goBack' },
          paramsValidate: function (e) {
            return { index: e || 1 }
          },
        }),
        t
      )
    }
    return o(i)
  })()
  const C = (function () {
    e(i, w)
    const n = c(i)
    function i(e) {
      let t
      const a = e.ctx
      var e = e.classify
      s(this, i), (t = n.call(this))
      return (
        a.registerSystemCallAPI({
          classify: e,
          apiName: 'jump',
          additional: { module: t.module, handlerName: 'jump' },
          paramsValidate: function (e) {
            return { url: e }
          },
        }),
        t
      )
    }
    return o(i)
  })()
  const P = (function () {
    e(i, w)
    const n = c(i)
    function i(e) {
      let t
      const a = e.ctx
      var e = e.classify
      s(this, i), (t = n.call(this))
      return (
        a.registerSystemCallAPI({
          classify: e,
          apiName: 'close',
          additional: { module: t.module, handlerName: 'close' },
        }),
        t
      )
    }
    return o(i)
  })()
  const k = (function () {
    e(i, w)
    const n = c(i)
    function i(e) {
      let t
      const a = e.ctx
      var e = e.classify
      s(this, i), (t = n.call(this))
      return (
        a.registerSystemCallAPI({
          classify: e,
          apiName: 'open',
          additional: { module: t.module, handlerName: 'open' },
          paramsValidate: function (e) {
            return { url: e.url, tipsTitle: e.tipsTitle, tipsMessage: e.tipsMessage }
          },
        }),
        t
      )
    }
    return o(i)
  })()
  const z = {
    WZToken: ['get', 'getUserInfo'],
    WZApi: ['launchAppForResult', 'launchAppWithUrl'],
    WZNavigation: ['setBounces', 'isAllowGoBack', 'setAllowGoBack'],
  }
  const q = { WZNavigation: ['setBounces', 'isAllowGoBack', 'setAllowGoBack'] }
  const G = { WZNavigation: ['setStatusBarColor'] }
  function _(e, t, a) {
    let n = null
    switch (e) {
      case 'ios':
        n = G[t]
        break
      case 'android':
        n = q[t]
        break
      case 'pc':
        n = z[t]
    }
    return (
      !Array.isArray(n) ||
      !n.includes(a) ||
      (f.warn('您的系统暂不支持此功能, module: '.concat(t, ', handlerName: ').concat(a)), !1)
    )
  }
  const R = (function () {
    e(r, w)
    const i = c(r)
    function r(e) {
      let t
      const a = e.ctx
      var e = e.classify
      const n = (s(this, r), (t = i.call(this)), 'setBounces')
      return (
        a.registerSystemCallAPI({
          classify: e,
          apiName: n,
          additional: { module: t.module, handlerName: n },
          paramsValidate: function (e) {
            return _(a.os.type, this.module, n)
              ? { bounce: !!e }
              : Error("API 'setBounces' only works on iOS")
          },
        }),
        t
      )
    }
    return o(r)
  })()
  const Z = (function () {
    e(r, w)
    const i = c(r)
    function r(e) {
      let t
      const a = e.ctx
      var e = e.classify
      const n = (s(this, r), (t = i.call(this)), 'setBrightnessStyle')
      return (
        a.registerSystemCallAPI({
          classify: e,
          apiName: n,
          additional: { module: t.module, handlerName: n },
          paramsValidate: function (e) {
            return ['light', 'dark'].includes(e)
              ? { style: e }
              : Error('API \'setBrightnessStyle\' parameters range is "light" or "dark"')
          },
        }),
        t
      )
    }
    return o(r)
  })()
  const J = (function () {
    e(r, w)
    const i = c(r)
    function r(e) {
      let t
      const a = e.ctx
      var e = e.classify
      const n = (s(this, r), (t = i.call(this)), 'isAllowGoBack')
      return (
        a.registerSystemCallAPI({
          apiName: n,
          classify: e,
          additional: { module: t.module, handlerName: n },
          paramsValidate: function (e) {
            return _(a.os.type, this.module, n) ? e : Error("API 'IsAllowGoBack' only works on iOS")
          },
        }),
        t
      )
    }
    return o(r)
  })()
  const X = (function () {
    e(r, w)
    const i = c(r)
    function r(e) {
      let t
      const a = e.ctx
      var e = e.classify
      const n = (s(this, r), (t = i.call(this)), 'setAllowGoBack')
      return (
        a.registerSystemCallAPI({
          apiName: n,
          classify: e,
          additional: { module: t.module, handlerName: n },
          paramsValidate: function (e) {
            return _(a.os.type, this.module, n)
              ? { allowGoBack: !!e.allowGoBack }
              : Error("API 'setAllowGoBack' only works on iOS")
          },
        }),
        t
      )
    }
    return o(r)
  })()
  const O = o(function e() {
    s(this, e), (this.module = 'WZApi')
  })
  const K = (function () {
    e(r, O)
    const i = c(r)
    function r(e) {
      let t
      const a = e.ctx
      var e = e.classify
      const n = (s(this, r), (t = i.call(this)), 'getPageParam')
      return (
        a.registerSystemCallAPI({
          alias: 'getRequestData',
          apiName: n,
          classify: e,
          additional: { module: t.module, handlerName: n },
        }),
        t
      )
    }
    return o(r)
  })()
  const $ = (function () {
    e(r, O)
    const i = c(r)
    function r(e) {
      let t
      const a = e.ctx
      var e = e.classify
      const n = (s(this, r), (t = i.call(this)), 'sendMessage')
      return (
        a.registerSystemCallAPI({
          apiName: n,
          classify: e,
          additional: { module: t.module, handlerName: n },
          paramsValidate: function (e) {
            return { type: e.type, appId: e.appId, data: e.data }
          },
        }),
        t
      )
    }
    return o(r)
  })()
  const Q = (function () {
    e(r, O)
    const i = c(r)
    function r(e) {
      let t
      const a = e.ctx
      var e = e.classify
      const n = (s(this, r), (t = i.call(this)), 'openFile')
      return (
        a.registerSystemCallAPI({
          alias: 'openLocalFile',
          apiName: n,
          classify: e,
          additional: { module: t.module, handlerName: n },
          paramsValidate: function (e) {
            return { filePath: e.filePath, fileName: e.fileName, fileSize: e.fileSize }
          },
        }),
        t
      )
    }
    return o(r)
  })()
  const Y = (function () {
    e(r, O)
    const i = c(r)
    function r(e) {
      let t
      const a = e.ctx
      var e = e.classify
      const n = (s(this, r), (t = i.call(this)), 'cacheSrcFile')
      return (
        a.registerSystemCallAPI({
          alias: 'cacheFileToLocal',
          apiName: n,
          classify: e,
          additional: { module: t.module, handlerName: n },
          paramsValidate: function (e) {
            return { filePath: e.filePath, fileName: e.fileName }
          },
        }),
        t
      )
    }
    return o(r)
  })()
  const ee = (function () {
    e(r, O)
    const i = c(r)
    function r(e) {
      let t
      const a = e.ctx
      var e = e.classify
      const n = (s(this, r), (t = i.call(this)), 'openCacheFile')
      return (
        a.registerSystemCallAPI({
          alias: 'cacheFileToLocalAndOpen',
          apiName: n,
          classify: e,
          additional: { module: t.module, handlerName: n },
          paramsValidate: function (e) {
            return { filePath: e.filePath, fileName: e.fileName, fileSize: e.fileSize }
          },
        }),
        t
      )
    }
    return o(r)
  })()
  const te = (function () {
    e(i, O)
    const n = c(i)
    function i(e) {
      let t
      const a = e.ctx
      var e = e.classify
      s(this, i), (t = n.call(this))
      return (
        a.registerSystemCallAPI({
          alias: 'shareFile',
          apiName: 'share',
          classify: e,
          additional: { module: t.module, handlerName: 'share' },
        }),
        t
      )
    }
    return o(i)
  })()
  const ae = (function () {
    e(r, O)
    const i = c(r)
    function r(e) {
      let t
      const a = e.ctx
      var e = e.classify
      const n = (s(this, r), (t = i.call(this)), 'openExternalLinks')
      return (
        a.registerSystemCallAPI({
          apiName: n,
          classify: e,
          additional: { module: t.module, handlerName: n },
        }),
        t
      )
    }
    return o(r)
  })()
  const E = o(function e() {
    s(this, e), (this.module = 'WZToken')
  })
  const ne = (function () {
    e(i, E)
    const n = c(i)
    function i(e) {
      let t
      const a = e.ctx
      var e = e.classify
      s(this, i), (t = n.call(this))
      return (
        a.registerSystemCallAPI({
          alias: 'getToken',
          apiName: 'get',
          classify: e,
          additional: { module: t.module, handlerName: 'get' },
        }),
        t
      )
    }
    return o(i)
  })()
  const ie = (function () {
    e(r, E)
    const i = c(r)
    function r(e) {
      let t
      const a = e.ctx
      var e = e.classify
      const n = (s(this, r), (t = i.call(this)), 'getUserInfo')
      return (
        a.registerSystemCallAPI({
          apiName: n,
          classify: e,
          additional: { module: t.module, handlerName: n },
        }),
        t
      )
    }
    return o(r)
  })()
  const re = o(function e(t) {
    const a = t.ctx
    var t = t.classify
    const n = (s(this, e), (this.module = 'WZInfo'), 'getInitInfo')
    a.registerSystemCallAPI({
      apiName: n,
      classify: t,
      additional: { module: this.module, handlerName: n },
    })
  })
  const se = o(function e() {
    s(this, e), (this.module = 'WZPlugin')
  })
  const oe = (function () {
    e(r, se)
    const i = c(r)
    function r(e) {
      let t
      const a = e.ctx
      var e = e.classify
      const n = (s(this, r), (t = i.call(this)), 'getPluginBasePath')
      return (
        a.registerSystemCallAPI({
          apiName: n,
          classify: e,
          additional: { module: t.module, handlerName: n },
          paramsValidate: function (e) {
            return { appId: e.appId, sysVersionNum: e.sysVersionNum }
          },
        }),
        t
      )
    }
    return o(r)
  })()
  const le = (function () {
    e(r, se)
    const i = c(r)
    function r(e) {
      let t
      const a = e.ctx
      var e = e.classify
      const n = (s(this, r), (t = i.call(this)), 'getPluginFileContent')
      return (
        a.registerSystemCallAPI({
          apiName: n,
          classify: e,
          additional: { module: t.module, handlerName: n },
          paramsValidate: function (e) {
            return { path: e.path }
          },
        }),
        t
      )
    }
    return o(r)
  })()
  const ce = o(function e() {
    s(this, e), (this.module = 'WZDevice')
  })
  const ue = (function () {
    e(r, ce)
    const i = c(r)
    function r(e) {
      let t
      const a = e.ctx
      var e = e.classify
      const n = (s(this, r), (t = i.call(this)), 'getDeviceInfo')
      return (
        a.registerSystemCallAPI({
          classify: e,
          apiName: n,
          additional: { module: t.module, handlerName: n },
        }),
        t
      )
    }
    return o(r)
  })()
  const de = (function () {
    e(r, ce)
    const i = c(r)
    function r(e) {
      let t
      const a = e.ctx
      var e = e.classify
      const n = (s(this, r), (t = i.call(this)), 'getSystemInfo')
      return (
        a.registerSystemCallAPI({
          classify: e,
          apiName: n,
          additional: { module: t.module, handlerName: n },
        }),
        t
      )
    }
    return o(r)
  })()
  const fe = o(function e() {
    s(this, e), (this.module = 'WZScanner')
  })
  const he = (function () {
    e(i, fe)
    const n = c(i)
    function i(e) {
      let t
      const a = e.ctx
      var e = e.classify
      s(this, i), (t = n.call(this))
      return (
        a.registerSystemCallAPI({
          apiName: 'scan',
          classify: e,
          additional: { module: t.module, handlerName: 'scan' },
        }),
        t
      )
    }
    return o(i)
  })()
  const me = o(function e() {
    s(this, e), (this.module = 'WZCamera')
  })
  const ye = (function () {
    e(r, me)
    const i = c(r)
    function r(e) {
      let t
      const a = e.ctx
      var e = e.classify
      const n = (s(this, r), (t = i.call(this)), 'takePhoto')
      return (
        a.registerSystemCallAPI({
          classify: e,
          apiName: n,
          additional: { module: t.module, handlerName: n },
        }),
        t
      )
    }
    return o(r)
  })()
  const pe = (function () {
    e(r, me)
    const i = c(r)
    function r(e) {
      let t
      const a = e.ctx
      var e = e.classify
      const n = (s(this, r), (t = i.call(this)), 'pickImage')
      return (
        a.registerSystemCallAPI({
          classify: e,
          apiName: n,
          additional: { module: t.module, handlerName: n },
        }),
        t
      )
    }
    return o(r)
  })()
  const ve = o(function e() {
    s(this, e), (this.module = 'WZLocation')
  })
  const ge = (function () {
    e(r, ve)
    const i = c(r)
    function r(e) {
      let t
      const a = e.ctx
      var e = e.classify
      const n = (s(this, r), (t = i.call(this)), 'getLoactionInfo')
      return (
        a.registerSystemCallAPI({
          classify: e,
          apiName: n,
          additional: { module: t.module, handlerName: n },
        }),
        t
      )
    }
    return o(r)
  })()
  const we = o(function e() {
    s(this, e), (this.module = 'WZInteractiveLiveness')
  })
  const Ne = (function () {
    e(r, we)
    const i = c(r)
    function r(e) {
      let t
      const a = e.ctx
      var e = e.classify
      const n = (s(this, r), (t = i.call(this)), 'openInteractiveLiveness')
      return (
        a.registerSystemCallAPI({
          classify: e,
          apiName: n,
          additional: { module: t.module, handlerName: n },
        }),
        t
      )
    }
    return o(r)
  })()
  const xe = o(function e(t) {
    const a = t.ctx
    const o = t._internalCtx
    var t = t.classify
    s(this, e),
      a.registerSystemCallAPI({
        apiName: 'launchApp',
        classify: t,
        execute: function (e) {
          const t = e.appId
          const a = e.version
          const n = e.serviceId
          const i = e.linkPath
          const r = e.data
          const s = e.actionItems
          return e.needCallback
            ? o.internal.launchAppForResult({ appId: t, version: a, data: r, actionItems: s })
            : n
              ? o.internal.callAppService({
                  appId: t,
                  version: a,
                  serviceId: n,
                  data: r,
                  actionItems: s,
                })
              : i
                ? o.internal.launchAppWithUrl({
                    appId: t,
                    version: a,
                    linkPath: i,
                    data: r,
                    actionItems: s,
                  })
                : o.internal.launchApp({ appId: t, version: a, data: r, actionItems: s })
        },
      })
  })
  const Se = o(function e(t) {
    const a = t.ctx
    const n = t._internalCtx
    var t = t.classify
    s(this, e),
      a.registerSystemCallAPI({
        apiName: 'returnCallerApp',
        classify: t,
        execute: function (e) {
          const t = e.returnData
          var e = e.isClose
          n.internal.setResultData(t), e && a.window.close()
        },
      })
  })
  const Ie = o(function e(t) {
    const a = t.ctx
    const i = t._internalCtx
    var t = t.classify
    s(this, e),
      a.registerSystemCallAPI({
        apiName: 'setContainerStyle',
        classify: t,
        execute: function (e) {
          const t = e.title
          const a = e.bounces
          const n = e.statusBar
          var e = e.backgroundColor
          t && (t.text && ((t.title = t.text), delete t.text), i.internal.setTitle(t)),
            n && i.internal.setStatusBar(n),
            e && i.internal.setBackgroundColor(e),
            a && i.internal.setBounces(a)
        },
      })
  })
  const Ae = o(function e(t) {
    const a = t.ctx
    const n = t._internalCtx
    var t = t.classify
    s(this, e),
      a.registerSystemCallAPI({
        apiName: 'showNavigationBar',
        classify: t,
        execute: function (e) {
          e ? n.internal.show() : n.internal.hide()
        },
      })
  })
  const be = o(function e(t) {
    const a = t.ctx
    const n = t._internalCtx
    var t = t.classify
    s(this, e),
      a.registerSystemCallAPI({
        apiName: 'back',
        classify: t,
        execute: function (e) {
          n.internal.back(e)
        },
      })
  })
  const Ce = o(function e(t) {
    const a = t.ctx
    const n = t._internalCtx
    var t = t.classify
    s(this, e),
      a.registerSystemCallAPI({
        apiName: 'close',
        classify: t,
        execute: function () {
          n.internal.close()
        },
      })
  })
  const Pe = o(function e(t) {
    const a = t.ctx
    const n = t._internalCtx
    var t = t.classify
    s(this, e),
      a.registerSystemCallAPI({
        apiName: 'jump',
        classify: t,
        execute: function (e) {
          n.internal.jump(e)
        },
      })
  })
  const ke = o(function e(t) {
    const a = t.ctx
    const n = t._internalCtx
    var t = t.classify
    s(this, e),
      a.registerSystemCallAPI({
        apiName: 'open',
        classify: t,
        execute: function (e) {
          const t = e.url
          const a = e.tipsTitle
          var e = e.tipsMessage
          n.internal.open({ url: t, tipsTitle: a, tipsMessage: void 0 === e ? '正在加载...' : e })
        },
      })
  })
  const _e = (function () {
    e(r, O)
    const i = c(r)
    function r(e) {
      let t
      const a = e.ctx
      var e = e.classify
      const n = (s(this, r), (t = i.call(this)), 'launchApp')
      return (
        a.registerSystemCallAPI({
          apiName: n,
          classify: e,
          additional: { module: t.module, handlerName: n },
        }),
        t
      )
    }
    return o(r)
  })()
  const Re = (function () {
    e(r, O)
    const i = c(r)
    function r(e) {
      let t
      const a = e.ctx
      var e = e.classify
      const n = (s(this, r), (t = i.call(this)), 'launchAppForResult')
      return (
        a.registerSystemCallAPI({
          apiName: n,
          classify: e,
          additional: { module: t.module, handlerName: n },
          paramsValidate: function (e) {
            const t = e.appId
            const a = e.version
            const n = e.data
            var e = e.actionItems
            return {
              appId: t,
              version: void 0 === a ? 1 : a,
              data: void 0 === n ? {} : n,
              actionItems: void 0 === e ? [] : e,
            }
          },
        }),
        t
      )
    }
    return o(r)
  })()
  const Oe = (function () {
    e(r, O)
    const i = c(r)
    function r(e) {
      let t
      const a = e.ctx
      var e = e.classify
      const n = (s(this, r), (t = i.call(this)), 'callAppService')
      return (
        a.registerSystemCallAPI({
          apiName: n,
          classify: e,
          additional: { module: t.module, handlerName: n },
          paramsValidate: function (e) {
            const t = e.appId
            const a = e.version
            const n = e.serviceId
            const i = e.data
            var e = e.actionItems
            return {
              appId: t,
              version: void 0 === a ? 1 : a,
              serviceId: n,
              data: void 0 === i ? {} : i,
              actionItems: void 0 === e ? [] : e,
            }
          },
        }),
        t
      )
    }
    return o(r)
  })()
  const Ee = (function () {
    e(r, O)
    const i = c(r)
    function r(e) {
      let t
      const a = e.ctx
      var e = e.classify
      const n = (s(this, r), (t = i.call(this)), 'launchAppWithUrl')
      return (
        a.registerSystemCallAPI({
          apiName: n,
          classify: e,
          additional: { module: t.module, handlerName: n },
        }),
        t
      )
    }
    return o(r)
  })()
  const Te = (function () {
    e(r, O)
    const i = c(r)
    function r(e) {
      let t
      const a = e.ctx
      var e = e.classify
      const n = (s(this, r), (t = i.call(this)), 'setResultData')
      return (
        a.registerSystemCallAPI({
          apiName: n,
          classify: e,
          additional: { module: t.module, handlerName: n },
          paramsValidate: function (e) {
            return e
          },
        }),
        t
      )
    }
    return o(r)
  })()
  const Le = o(function e() {
    s(this, e), (this.module = 'WZHttp')
  })
  const We = (function () {
    e(r, Le)
    const i = c(r)
    function r(e) {
      let t
      const a = e.ctx
      var e = e.classify
      const n = (s(this, r), (t = i.call(this)), 'request2')
      return (
        a.registerSystemCallAPI({
          alias: 'request',
          classify: e,
          apiName: n,
          additional: { module: t.module, handlerName: n },
          paramsValidate: function (e) {
            const t = e.url
            const a = e.method
            const n = e.async
            const i = e.headers
            const r = e.data
            var e = e.dataType
            return {
              url: t,
              method: a,
              async: void 0 === n || n,
              headers: void 0 === i ? {} : i,
              data: r,
              dataType: void 0 === e ? 'json' : e,
            }
          },
        }),
        t
      )
    }
    return o(r)
  })()
  const He = new m()
  const T = (function () {
    e(n, y)
    const a = c(n)
    function n() {
      s(this, n)
      let e
      var t = { ctx: l((e = a.call(this))), classify: 'internal' }
      var t = (new _e(t), new Re(t), new Oe(t), new Ee(t), { ctx: l(e), classify: 'internal' })
      var t = (new Te(t), { ctx: l(e), classify: 'internal' })
      var t =
        (new N(t),
        new x(t),
        new S(t),
        new R(t),
        new I(t),
        new A(t),
        new b(t),
        new C(t),
        new P(t),
        new k(t),
        { ctx: l(e), classify: 'http' })
      return new We(t), e
    }
    return (
      o(
        n,
        [
          {
            key: 'registerSystemCallAPI',
            get: function () {
              return He.registerSystemCallAPI(this)
            },
          },
        ],
        [
          {
            key: 'getInstance',
            value: function () {
              return n.instance || (n.instance = new n())
            },
          },
        ],
      ),
      n
    )
  })().getInstance()
  const Be = (function () {
    function e() {
      s(this, e)
    }
    return (
      o(e, [
        {
          key: 'register',
          value: function (e) {
            var t = { ctx: e, classify: 'navigator' }
            var t =
              (new N(t),
              new x(t),
              new S(t),
              new I(t),
              new A(t),
              new Z(t),
              new J(t),
              new X(t),
              { ctx: e, classify: 'window' })
            var t =
              (new b(t), new P(t), new k(t), new C(t), new R(t), { ctx: e, classify: 'service' })
            var t = (new ne(t), new ie(t), new re(t), { ctx: e, classify: 'device' })
            var t = (new ue(t), new de(t), { ctx: e, classify: 'utils' })
            var t =
              (new he(t),
              new ye(t),
              new pe(t),
              new ge(t),
              new Ne(t),
              new xe({ ctx: e, _internalCtx: T, classify: 'process' }),
              { ctx: e, _internalCtx: T, classify: 'container' })
            var t =
              (new Se(t),
              new Ie(t),
              new Ae(t),
              new be(t),
              new Ce(t),
              new Pe(t),
              new ke(t),
              { ctx: e, classify: 'container' })
            var t = (new K(t), new $(t), { ctx: e, classify: 'io' })
            var t =
              (new Q(t), new Y(t), new ee(t), new te(t), new ae(t), { ctx: e, classify: 'program' })
            new oe(t), new le(t)
          },
        },
      ]),
      e
    )
  })()
  const Fe = (function () {
    function e() {
      s(this, e), (this.realXhr = 'RealXMLHttpRequest')
    }
    return (
      o(e, [
        {
          key: 'hook',
          value: function (r) {
            window[this.realXhr] = window[this.realXhr] || XMLHttpRequest
            const n = this
            return (
              (window.XMLHttpRequest = function () {
                let e
                const t = new window[n.realXhr]()
                for (e in t) {
                  let a = ''
                  try {
                    a = i(t[e])
                  } catch (e) {}
                  a === 'function'
                    ? (this[e] = (function (t) {
                        return function () {
                          const e = [].slice.call(arguments)
                          if (!r[t] || !r[t].call(this, e, this.xhr))
                            return this.xhr[t].apply(this.xhr, e)
                        }
                      })(e))
                    : Object.defineProperty(this, e, {
                        get: (function (a) {
                          return function () {
                            const e = this.hasOwnProperty(a + '_') ? this[a + '_'] : this.xhr[a]
                            const t = (r[a] || {}).getter
                            return (t && t(e, this)) || e
                          }
                        })(e),
                        set: (function (i) {
                          return function (t) {
                            const e = this.xhr
                            const a = this
                            let n = r[i]
                            if (typeof n === 'function')
                              e[i] = function () {
                                r[i](a) || t.apply(e, arguments)
                              }
                            else {
                              n = (n || {}).setter
                              t = (n && n(t, a)) || t
                              try {
                                e[i] = t
                              } catch (e) {
                                this[i + '_'] = t
                              }
                            }
                          }
                        })(e),
                        enumerable: !0,
                      })
                }
                this.xhr = t
              }),
              window[this.realXhr]
            )
          },
        },
        {
          key: 'unHook',
          value: function () {
            window[this.realXhr] && (window.XMLHttpRequest = window[this.realXhr]),
              (window[this.realXhr] = null)
          },
        },
      ]),
      e
    )
  })()
  const L = {
    100: 'Continue ',
    101: ' Switching Protocols',
    200: ' OK ',
    201: ' Created',
    202: ' Accepted ',
    203: ' Non-Authoritative Information',
    204: ' No Content ',
    205: ' Reset Content',
    206: ' Partial Content',
    300: ' Multiple Choices ',
    301: ' Moved Permanently',
    302: ' Found',
    303: ' See Other',
    304: ' Not Modified ',
    305: ' Use Proxy',
    307: ' Temporary Redirect ',
    400: ' Bad Request',
    401: ' Unauthorized ',
    402: ' Payment Required ',
    403: ' Forbidden',
    404: ' Not Found',
    405: ' Method Not Allowed ',
    406: ' Not Acceptable ',
    407: ' Proxy Authentication Required',
    408: ' Request Timeout',
    409: ' Conflict ',
    410: ' Gone ',
    411: ' Length Required',
    412: ' Precondition Failed',
    413: ' Payload Too Large',
    414: ' URI Too Long ',
    415: ' Unsupported Media Type ',
    416: ' Range Not Satisfiable',
    417: ' Expectation Failed ',
    426: ' Upgrade Required ',
    500: ' Internal Server Error',
    501: ' Not Implemented',
    502: ' Bad Gateway',
    503: ' Service Unavailable',
    504: ' Gateway Timeout',
    505: ' HTTP Version Not Supported',
    4e3: 'UNKNOWN',
    4001: 'NETWORK_ERROR',
    4002: 'TIMEOUT',
    4003: 'SSL_ERROR',
    4004: 'HOST_NOT_FOUND',
    4005: 'SSL_NOT_FOUND',
    4006: 'REQUEST_ERROR',
  }
  const je = (function () {
    e(a, y)
    const t = c(a)
    function a() {
      let e
      return (
        s(this, a),
        ((e = t.call(this)).xHRHook = null),
        (e._originUseNativeProxy = e.os.isZipModel),
        (e.useNativeProxy = e.os.isZipModel),
        (e.openData = { method: '', url: '', async: '' }),
        (e.headersData = {}),
        (e.timeout = 12e4),
        e
      )
    }
    return (
      o(a, [
        {
          key: 'initXHRHook',
          value: function () {
            const e = new Fe()
            const a = this
            e.hook({
              open: function (e) {
                ;(a.useNativeProxy = a._originUseNativeProxy),
                  (a.openData = { method: e[0], url: e[1], async: e[2] })
              },
              setRequestHeader: function (e) {
                var e = H(e, 2)
                const t = e[0]
                var e = e[1]
                if (t === 'useNativeProxy') return (a.useNativeProxy = !!e), !0
                a.headersData[t] = e
              },
              send: function (e, t) {
                if (a.useNativeProxy)
                  return (
                    f.info('send the request using the native proxy'),
                    [
                      'readyState',
                      'response',
                      'responseText',
                      'responseURL',
                      'status',
                      'statusText',
                    ].forEach(function (e) {
                      Object.defineProperty(t, e, {
                        configurable: !0,
                        enumerable: !0,
                        writable: !0,
                      })
                    }),
                    (a.timeout = t.timeout || a.timeout),
                    a._nativeProxyRequest(e, t),
                    !0
                  )
              },
            }),
              (this.xHRHook = e)
          },
        },
        {
          key: 'relieveXHRHook',
          value: function () {
            this.xHRHook.unHook()
          },
        },
        {
          key: 'setUseNativeProxy',
          value: function (e) {
            this._originUseNativeProxy = e
          },
        },
        {
          key: '_nativeProxyRequest',
          value: function (e, a) {
            let n
            const i = this
            let r = {}
            let t =
              (/post|put/i.test(this.openData.method) &&
                e &&
                ((e = e[0]),
                v(e)
                  ? (r = JSON.parse(e))
                  : e.split('&').forEach(function (e) {
                      e = e.split('=')
                      r[e[0]] = e[1]
                    })),
              /get|delete/i.test(this.openData.method) &&
                ((e = decodeURIComponent(this.openData.url)
                  .split(/\?|=|&/g)
                  .slice(1)),
                (n = ''),
                e.forEach(function (e, t) {
                  t % 2 == 0 ? ((r[e] = ''), (n = e)) : (r[n] = e)
                }),
                (this.openData.url = this.openData.url.split('?')[0])),
              !0)
            const s = setTimeout(function () {
              t &&
                i._trggerReadystatechange(
                  a,
                  4,
                  408,
                  L[408],
                  'Error: timeout of '.concat(i.timeout, 'ms exceeded'),
                )
            }, this.timeout)
            function o() {
              ;(t = !1), clearTimeout(s)
            }
            ;(this.headersData['Content-Type'] = 'application/json; charset=UTF-8'),
              T.http
                .request({
                  method: this.openData.method,
                  url: this.openData.url,
                  headers: this.headersData,
                  data: r,
                  async: this.openData.async,
                })
                .then(function (e) {
                  a.responseURL = i.openData.url.split('?')[0]
                  const t = e.data.body
                  e.data.code === '10000'
                    ? ((e = +t.code == 1 ? 200 : +t.code == 0 ? 400 : Number(t.code)),
                      i._trggerReadystatechange(
                        a,
                        4,
                        e,
                        L[e],
                        v(t.data) ? JSON.parse(t.data) : t.data,
                      ))
                    : i._trggerReadystatechange(a, 4, 400, L[400]),
                    o()
                })
                .catch(function (e) {
                  i._trggerReadystatechange(a, 4, 400, L[400], e), o()
                })
          },
        },
        {
          key: '_trggerReadystatechange',
          value: function (e, t, a, n, i) {
            ;(e.status = a),
              (e.statusText = n),
              (e.readyState = t),
              (e.response = e.responseText = i),
              typeof e.onreadystatechange === 'function' && e.onreadystatechange(),
              typeof e.onload === 'function' && e.onload(),
              typeof e.onloadend === 'function' && e.onloadend()
          },
        },
      ]),
      a
    )
  })()
  const W = new m()
  const Me = '2.1.4-alpha'
  var m = (function () {
    function e() {
      let a
      s(this, e),
        (this.version = Me),
        (this.os = W.bridge.os),
        (this.event = W.event),
        new Be().register(this),
        (a = W).event.on(h.key(g.LIGHT_APP_MESSAGE), function (e) {
          let t
          var e = e.body
          e &&
            e.eventName &&
            ((t = e.eventName), (e = e.params), a.bridge.trigger(t, void 0 === e ? null : e))
        }),
        this.os.supportWaltz
          ? ((this._hook = new je()), this._hook.initXHRHook())
          : f.warn('Waltz is not supported in the current OS.')
    }
    return (
      o(e, [
        {
          key: 'registerSystemCallAPI',
          get: function () {
            return W.registerSystemCallAPI(this)
          },
        },
        {
          key: 'call',
          get: function () {
            return W.call.bind(W)
          },
        },
        {
          key: 'register',
          get: function () {
            return W.register.bind(W)
          },
        },
        {
          key: 'on',
          get: function () {
            return W.on.bind(W)
          },
        },
        {
          key: 'setUseNativeProxy',
          value: function (e) {
            this._hook.setUseNativeProxy(e)
          },
        },
        {
          key: 'install',
          value: function (e) {
            e.prototype.$waltz || (e.prototype.$waltz = this)
          },
        },
      ]),
      e
    )
  })()
  var m =
    ((window.handleMessageFromNative = function (a) {
      setTimeout(function () {
        var e = (a = v(a) ? JSON.parse(a) : a)
        const t = e.responseId
        var e = e.responseData
        W.bridge.nativeCallWeb(t, e)
      })
    }),
    t(window, 'handleMessageFromNative'),
    console.log(
      '%cWaltz%cv'.concat(Me),
      'padding: 1px 12px; background-color: #555; color: #fff; font-size: 12px; border-radius: 4px 0 0 4px',
      'padding: 1px 12px; background-color: #0080ff; color: #fff; font-size: 12px; border-radius: 0 4px 4px 0',
    ),
    new m())
  return (window.waltz = m), t(window, 'waltz'), m
})()
