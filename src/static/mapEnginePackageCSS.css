/*
* WgId:67
* WgName:安全生产风险管控
* WgInfo:安全生产风险管控
* MepName:IndoorThree_CSS
* MepInfo:IndoorThree_CSS
* ./api/v2/gissetter-service/MapEnginePackage/mapEnginePackageCSS?mepName=IndoorThree_CSS&wgId=67
*/
.ol-box {
  box-sizing: border-box;
  border: 2px solid #00f;
  border-radius: 2px;
}
.ol-mouse-position {
  position: absolute;
  top: 8px;
  right: 8px;
}
.ol-scale-line {
  position: absolute;
  bottom: 8px;
  left: 8px;
  padding: 2px;
  background: rgba(0, 60, 136, 0.3);
  border-radius: 4px;
}
.ol-scale-line-inner {
  margin: 1px;
  font-size: 10px;
  color: #eee;
  text-align: center;
  border: 1px solid #eee;
  border-top: none;
  will-change: contents, width;
}
.ol-overlay-container {
  will-change: left, right, top, bottom;
}
.ol-unsupported {
  display: none;
}
.ol-unselectable,
.ol-viewport {
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-tap-highlight-color: transparent;
}
.ol-selectable {
  -webkit-touch-callout: default;
  -webkit-user-select: auto;
  -moz-user-select: auto;
  -ms-user-select: auto;
  user-select: auto;
}
.ol-grabbing {
  cursor: -webkit-grabbing;
  cursor: -moz-grabbing;
  cursor: grabbing;
}
.ol-grab {
  cursor: move;
  cursor: -webkit-grab;
  cursor: -moz-grab;
  cursor: grab;
}
.ol-control {
  position: absolute;
  padding: 2px;
  background-color: rgba(255, 255, 255, 0.4);
  border-radius: 4px;
}
.ol-control:hover {
  background-color: rgba(255, 255, 255, 0.6);
}
.ol-zoom {
  top: 0.5em;
  left: 0.5em;
}
.ol-rotate {
  top: 0.5em;
  right: 0.5em;
  transition:
    opacity 0.25s linear,
    visibility 0s linear;
}
.ol-rotate.ol-hidden {
  visibility: hidden;
  opacity: 0;
  transition:
    opacity 0.25s linear,
    visibility 0s linear 0.25s;
}
.ol-zoom-extent {
  top: 4.643em;
  left: 0.5em;
}
.ol-full-screen {
  top: 0.5em;
  right: 0.5em;
}
@media print {
  .ol-control {
    display: none;
  }
}
.ol-control button {
  display: block;
  width: 1.375em;
  height: 1.375em;
  padding: 0;
  margin: 1px;
  font-size: 1.14em;
  font-weight: 700;
  line-height: 0.4em;
  color: #fff;
  text-align: center;
  text-decoration: none;
  background-color: rgba(0, 60, 136, 0.5);
  border: none;
  border-radius: 2px;
}
.ol-control button::-moz-focus-inner {
  padding: 0;
  border: none;
}
.ol-zoom-extent button {
  line-height: 1.4em;
}
.ol-compass {
  display: block;
  font-size: 1.2em;
  font-weight: 400;
  will-change: transform;
}
.ol-touch .ol-control button {
  font-size: 1.5em;
}
.ol-touch .ol-zoom-extent {
  top: 5.5em;
}
.ol-control button:focus,
.ol-control button:hover {
  text-decoration: none;
  background-color: rgba(0, 60, 136, 0.7);
}
.ol-zoom .ol-zoom-in {
  border-radius: 2px 2px 0 0;
}
.ol-zoom .ol-zoom-out {
  border-radius: 0 0 2px 2px;
}
.ol-attribution {
  right: 0.5em;
  bottom: 0.5em;
  max-width: calc(100% - 1.3em);
  text-align: right;
}
.ol-attribution ul {
  padding: 0 0.5em;
  margin: 0;
  font-size: 0.7rem;
  line-height: 1.375em;
  color: #000;
  text-shadow: 0 0 2px #fff;
}
.ol-attribution li {
  display: inline;
  line-height: inherit;
  list-style: none;
}
.ol-attribution li:not(:last-child):after {
  content: ' ';
}
.ol-attribution img {
  max-width: inherit;
  max-height: 2em;
  vertical-align: middle;
}
.ol-attribution button,
.ol-attribution ul {
  display: inline-block;
}
.ol-attribution.ol-collapsed ul {
  display: none;
}
.ol-attribution.ol-logo-only ul {
  display: block;
}
.ol-attribution:not(.ol-collapsed) {
  background: rgba(255, 255, 255, 0.8);
}
.ol-attribution.ol-uncollapsible {
  right: 0;
  bottom: 0;
  height: 1.1em;
  line-height: 1em;
  border-radius: 4px 0 0;
}
.ol-attribution.ol-logo-only {
  bottom: 0.4em;
  height: 1.1em;
  line-height: 1em;
  background: 0 0;
}
.ol-attribution.ol-uncollapsible img {
  max-height: 1.6em;
  margin-top: -0.2em;
}
.ol-attribution.ol-logo-only button,
.ol-attribution.ol-uncollapsible button {
  display: none;
}
.ol-zoomslider {
  top: 4.5em;
  left: 0.5em;
  height: 200px;
}
.ol-zoomslider button {
  position: relative;
  height: 10px;
}
.ol-touch .ol-zoomslider {
  top: 5.5em;
}
.ol-overviewmap {
  bottom: 0.5em;
  left: 0.5em;
}
.ol-overviewmap.ol-uncollapsible {
  bottom: 0;
  left: 0;
  border-radius: 0 4px 0 0;
}
.ol-overviewmap .ol-overviewmap-map,
.ol-overviewmap button {
  display: inline-block;
}
.ol-overviewmap .ol-overviewmap-map {
  width: 150px;
  height: 150px;
  margin: 2px;
  border: 1px solid #7b98bc;
}
.ol-overviewmap:not(.ol-collapsed) button {
  position: absolute;
  bottom: 1px;
  left: 2px;
}
.ol-overviewmap.ol-collapsed .ol-overviewmap-map,
.ol-overviewmap.ol-uncollapsible button {
  display: none;
}
.ol-overviewmap:not(.ol-collapsed) {
  background: rgba(255, 255, 255, 0.8);
}
.ol-overviewmap-box {
  border: 2px dotted rgba(0, 60, 136, 0.7);
}
.ol-overviewmap.ol-overviewmap-box:hover {
  cursor: move;
}
.ol-popup {
  position: absolute;
  bottom: 12px;
  left: -50px;
  min-width: 280px;
  padding: 15px;
  background-color: white;
  border: 1px solid #ccc;
  border-radius: 10px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
}
.ol-popup:after,
.ol-popup:before {
  position: absolute;
  top: 100%;
  width: 0;
  height: 0;
  pointer-events: none;
  content: ' ';
  border: solid transparent;
}
.ol-popup:after {
  left: 48px;
  margin-left: -10px;
  border-width: 10px;
  border-top-color: white;
}
.ol-popup:before {
  left: 48px;
  margin-left: -11px;
  border-width: 11px;
  border-top-color: #ccc;
}
.ol-popup-closer {
  position: absolute;
  top: 2px;
  right: 8px;
  text-decoration: none;
}
.ol-popup-closer:after {
  content: '✖';
}
.ol-compass2 {
  position: absolute;
  width: 60px;
  height: 60px;
  background: url(data:image/png;base64,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)
    no-repeat;
}
.ol-compass2 .compassTurnCenter {
  position: absolute;
  top: 13px;
  left: 24px;
  width: 12px;
  height: 34px;
  margin: 0;
  background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAiCAYAAABr0zihAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAZdEVYdFNvZnR3YXJlAEFkb2JlIEltYWdlUmVhZHlxyWU8AAABqklEQVQ4T42SMW4CMRBFbVOkiZQSiQukSMUBwglS0NOilbgByhm4AQ112igFN8ghcoEoJRIFBZs39niwvRspSI+xZ/6f8XrX9c5B0GhMe+cfe++bmi9FFKOAtQ9r4tZqXk3UVZwL1vEdPqvu2kwTUrDiPdPOxCudZ5pLDCaIyYcl617ZpOPcJtniRjgQs+Fo51eTbowJx/khZsMFHiDVh0dyCwwqztGttAaBP336GL3fJZGJhbdYjwwnfEES+pANJ9Z3uXEpfoJa7JmU1i9QGqL7FXLXcoKwh9IgBN5sFiCW7rZ33xBAhBF5o1coRS3P6ZaSoYvJ+hj6DHlSvEFWLn4vHyaqyA8vMcgNzqT7vDpvKjbTrD53XdfNof8n0TCDjyL5F18wE4MgvzFRyQ64nmSQKVcYE2aewQzCJ4wJhW8IUBleYUws7CHqSsMTjImFFxgYBLmJVnyCOxg1yE20hjcwTSkWFtAaVmCaUixM4Aey+AIPYJpSnDlANhw1Z1QbZQnZsNGcUW2UeziDvHn5Aqp6tSl4B3nzg9ogoaxh2+Qig4QyhccmB537BZRjcTKYiIi9AAAAAElFTkSuQmCC)
    no-repeat;
}
.ol-compass2 .compassTurnLeft,
.ol-compass2 .compassTurnRight {
  position: absolute;
  top: 20px;
  width: 8px;
  height: 23px;
  margin: 0;
  cursor: pointer;
}
.ol-compass2 .compassTurnLeft {
  left: 7px;
  background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAXCAYAAAA2jw7FAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAZdEVYdFNvZnR3YXJlAEFkb2JlIEltYWdlUmVhZHlxyWU8AAAA/ElEQVQoU2XSsUpCYRiH8YwWOboE0uLYBQRBrg0OoUNri45fd9AYBEJbFO3dgESJt+DkKA5CIOhQV9CQiPb85Xs/3uMZfq9+73nOQeEchBC8DBN8oKrdfvCELWY4085fvMAa36jHXS74hO7uuF0KjvGHBY7iLhfcQnf34rkQ9KGgEc+F4Acr5B4vGjXYX8tdFA09VsHQlp7GFRQ829LTuIGCR1t6Gm0oeLWlp3EOBQNbehoVKFja0rMvUyg6jedC8AIFd/HcjJ8puISCLzxggxOkoIQ5FJkWUqAfOoYP7rELDjGKS2/3t+0JemG6eMcvFOjVS4FXxjXeQgjZP0v1FBhmp7tAAAAAAElFTkSuQmCC)
    no-repeat;
}
.ol-compass2 .compassTurnLeft:hover {
  background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAcAAAAXCAYAAADHhFVIAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAZdEVYdFNvZnR3YXJlAEFkb2JlIEltYWdlUmVhZHlxyWU8AAAA20lEQVQoU23RsQpBcRTHcWQRFiWL0QOoW6wGgxisFlZvYFTKzSayewGJm1cwGWVQSjFcT2Ag+fud2zm30/Ufvrfu+fzP7V83ZowJc1yTRke0QdkoTpFBZ1TWUEEf5KMizTRuEW11ZSaQQy90Q8ko9hFtjQU0rhirNnygNwo/GSAGeRRcX4NglXFnwwbjzIYdxokNW4wLGzqMng0zjPc/pAfgxAdKNpwzDvi9rrHGeEEj9EUFwTi6IjogNQXpUgcF1JAggfZqKHmyST+7h9boiQj9AHUYplDbcc3yBwf2pp4UhuEzAAAAAElFTkSuQmCC)
    no-repeat;
}
.ol-compass2 .compassTurnRight {
  right: 5px;
  background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAcAAAAXCAYAAADHhFVIAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAZdEVYdFNvZnR3YXJlAEFkb2JlIEltYWdlUmVhZHlxyWU8AAAA4UlEQVQoU23SscpBYQDGcWTRx6JkMboApZzVYBCD1cJ43IFRKWUT2d2AhNyCySiD+uorBq7gG0j4P6fzcjq9ww/v8+/onIi4rpvCEnv8QJtHLwUc8cJIo2E+5HDBAyV/+0RpQVevzBaMcZxwQ1pbMMoAurqjczg6UJzrHI766juuOoejmMfK2OIGio4tjqFYtcUhFJu2OIVi3RbXUCza4hmKyXDIQ+Ggsxkr/nsXihOdNWTxRB+/UCzDizV/MP4QhRd7/mjskIQXza0HbRFT1N9Dwz8WaOPzY8/QQELDlxt5A1FX+IDYybKYAAAAAElFTkSuQmCC)
    no-repeat;
}
.ol-compass2 .compassTurnRight:hover {
  background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAcAAAAXCAYAAADHhFVIAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAZdEVYdFNvZnR3YXJlAEFkb2JlIEltYWdlUmVhZHlxyWU8AAAA3UlEQVQoU23SPwsBcRzH8R9Z5CxKFqMHoK7cajCIwWph9QyMSpFNZPcEJOQpmIwyKKUYeAQGkp/Pl+83d3yH1/35vn933XVn3K6Nwww2ELPWGkExCzuw0A9E2mCYhjM8IBeIvKAGdPVcixE4wg0SgcgLOkBXN7TocZxokW59h8tf5AXyWkktLjl6WhxwLGqxx7GqxRHHshYXHF0tnjg6vyHDYUvnMizwvslx+I44SMET2rAHinmJJR6IA4QktnyBrMGRKI/ut4IwRfo9aHCFKdTh87FxMIYKRGnwZc0LrNamnm9X52gAAAAASUVORK5CYII=)
    no-repeat;
}
.gs-tjs-compass {
  position: absolute;
  width: 95px;
  height: 95px;
  overflow: hidden;
  pointer-events: auto;
}
.gs-tjs-compass-outer-ring {
  position: absolute;
  top: 0px;
  left: 0px;
  width: 95px;
  height: 95px;
  fill: rgba(255, 255, 255, 0.5);
}
.gs-tjs-compass-outer-ring-background {
  position: absolute;
  top: 14px;
  left: 14px;
  box-sizing: content-box;
  width: 44px;
  height: 44px;
  border: 12px solid rgba(47, 53, 60, 0.8);
  border-radius: 44px;
}
.gs-tjs-compass-gyro {
  position: absolute;
  top: 0px;
  left: 0px;
  width: 95px;
  height: 95px;
  pointer-events: none;
  fill: #ccc;
}
.gs-tjs-compass-gyro-background {
  position: absolute;
  top: 30px;
  left: 30px;
  box-sizing: content-box;
  width: 33px;
  height: 33px;
  background-color: rgba(47, 53, 60, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 33px;
}
.gs-tjs-compass-gyro-background:hover + .gs-tjs-compass-gyro {
  fill: #68adfe;
}
.gs-tjs-navigation-controls {
  position: absolute;
  width: 30px;
  font-weight: 300;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  background-color: rgba(47, 53, 60, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.1);
  -webkit-touch-callout: none;
}
.gs-tjs-navigation-control {
  cursor: pointer;
  border-bottom: #fff;
}
.gs-tjs-navigation-control-last {
  cursor: pointer;
  border-bottom: #fff;
  border-bottom: 0;
}
.gs-tjs-navigation-control-icon-zoom-in {
  position: relative;
  padding-bottom: 4px;
  font-size: 20px;
  color: #fff;
  text-align: center;
}
.gs-tjs-navigation-control-icon-zoom-in:hover {
  color: #68adfe;
}
.gs-tjs-navigation-control-icon-zoom-out {
  position: relative;
  font-size: 20px;
  color: #fff;
  text-align: center;
}
.gs-tjs-navigation-control-icon-zoom-out:hover {
  color: #68adfe;
}
.gs-tjs-navigation-control-icon-reset {
  position: relative;
  left: 10px;
  box-sizing: content-box;
  width: 10px;
  padding-top: 6px;
  padding-bottom: 6px;
  fill: rgba(255, 255, 255, 0.8);
}
.gs-tjs-navigation-control-icon-reset:hover {
  fill: #68adfe;
}
