/*
 * @Author: 悦悦 <EMAIL>
 * @Date: 2024-11-28 12:17:08
 * @LastEditors: 悦悦 <EMAIL>
 * @LastEditTime: 2024-11-29 00:06:26
 * @FilePath: /隐患排查app/vitePlugin/reBuildName.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import fs from 'fs'
import path from 'path'

function renameFn(outputDir, newOutputDir) {
  try {
    // 确保新文件夹不存在
    if (!fs.existsSync(newOutputDir)) {
      fs.mkdirSync(newOutputDir)
    }

    // 遍历原始文件夹中的文件和子文件夹
    const files = fs.readdirSync(outputDir)
    for (const file of files) {
      // console.log('产物文件夹名称：', file)
      const srcPath = path.join(outputDir, file)
      const destPath = path.join(newOutputDir, file)

      // 使用 fs.renameSync 方法重命名文件或文件夹
      fs.renameSync(srcPath, destPath)
    }

    // 删除原始文件夹
    fs.rmdirSync(outputDir)

    // console.log('产物文件夹名称已成功修改为：', newOutputDir)
  } catch (error) {
    console.error('在修改产物文件夹名称时发生错误：', error)
  }
}

export default (newOutputDir) => {
  return {
    name: 'vite-plugin-rename-build-h5',
    apply: 'build',
    closeBundle: () => {
      renameFn('dist/build/h5', newOutputDir)
    },
  }
}
