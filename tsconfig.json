{"compilerOptions": {"target": "es2017", "composite": true, "skipLibCheck": true, "module": "ESNext", "moduleResolution": "Node", "resolveJsonModule": true, "noImplicitThis": true, "allowSyntheticDefaultImports": true, "allowJs": true, "sourceMap": true, "baseUrl": ".", "paths": {"@/*": ["./src/*"]}, "outDir": "dist", "lib": ["esnext", "dom"], "types": ["@dcloudio/types", "@types/wechat-miniprogram", "wot-design-uni/global.d.ts"]}, "exModuleInterop": true, "vueCompilerOptions": {"target": 3, "nativeTags": ["block", "template", "component", "slot"]}, "exclude": ["node_modules"], "include": ["src/**/*.ts", "src/**/*.js", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.jsx", "src/**/*.vue", "src/**/*.json"]}