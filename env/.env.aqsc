# 变量必须以 VITE_ 为前缀才能暴露给外部读取
NODE_ENV = 'aqsc'
# # # # 测试环境v1/演示环境
# VITE_APP_PUBLIC_BASE=/aqsc/v1/hazard/h5
VITE_APP_PUBLIC_BASE=./
# VITE_APP_OUTDIR='hazard-h5'
# VITE_APP_OUTDIR='ehs-hazard-h5'
# 演示环境gis服务拼接---/aqsc/v1/api/v3/bw-svc-indoor-gis-service
VITE_APP_GIS = '/aqsc/v1'
VITE_APP_PROXY_PREFIX = '/aqsc/v1/api/v1/'
# ehs-clnt-hazard-service
VITE_SERVER_MODEL = '/aqsc/v1/api/v3/'
# 头部引用 --
VITE_BASE_PREFIX = '/aqsc/v1'
# 打包使用
VITE_SERVER_BASEURL = ''

# 是否去除console 和 debugger
VITE_DELETE_CONSOLE = false
# 是否开启sourcemap
VITE_SHOW_SOURCEMAP = false
